from pathlib import Path

from .logger import logger


def write_file_to_disk(
    file_path: str,
    file_text: str,
    repo_name: str,
    branch_name: str
):
    try:
        disk_path = get_disk_path(
            path=file_path,
            repo_name=repo_name,
            branch_name=branch_name
        )
        disk_file = Path(disk_path)
        logger.info(f'Writing file to local disk: {disk_path}')
        # Create parent directories if they don't exist
        disk_file.parent.mkdir(parents=True, exist_ok=True)
        disk_file.write_text(data=file_text)
    except Exception as e:
        logger.error(f"Failed to write file to disk: {e}")
        raise


def get_path_handle(
    path: str,
    repo_name: str,
    branch_name: str
) -> Path:
    disk_path = get_disk_path(
        path=path,
        repo_name=repo_name,
        branch_name=branch_name
    )
    return Path(disk_path)


def get_disk_path(
    path: str,
    repo_name: str,
    branch_name: str
) -> str:
    # first 10 chars of branch name
    branch_name = branch_name[:10]
    return f"blitzy/{repo_name}/{branch_name}/{path}"


def read_file_from_disk(
    file_path: str,
    repo_name: str,
    branch_name: str
) -> str:
    try:
        disk_path = get_disk_path(
            path=file_path,
            repo_name=repo_name,
            branch_name=branch_name
        )
        logger.info(f'reading path from local disk: {disk_path}')
        disk_file = Path(disk_path)
        if not disk_file.is_file():
            logger.warning(f"File not found: {disk_file}")
            return ""

        return disk_file.read_text()
    except Exception as e:
        logger.error(f"Failed to read file from disk: {e}")
        raise
