import os

import requests
from typing import List, <PERSON><PERSON>, <PERSON><PERSON>

from github import Github, GithubException, InputGitTreeElement, UnknownObjectException
from github.Repository import Repository
from github.GitCommit import G<PERSON><PERSON>ommit
from github.PullRequest import <PERSON><PERSON><PERSON><PERSON><PERSON>
from github.GitRef import G<PERSON><PERSON><PERSON>
from github.ContentFile import ContentFile

from .errors import FailedToFetchCredentials
from .logger import logger
from .consts import GITHUB_CREATE_REPO_DESCRIPTION
from .common import get_google_authorized_request_headers, blitzy_exponential_retry
from .disk import write_file_to_disk


class BlitzyGitFile:
    def __init__(
        self,
        path: str,
        text: str
    ):
        self.path = path
        self.text = text


@blitzy_exponential_retry()
def get_user_secret_info(user_id: str, server: str):
    """Fetch GitHub access token from the secret server."""
    try:
        url = f"{server}/secret/{user_id}"
        headers = get_google_authorized_request_headers(url)
        response = requests.get(url, headers=headers)

        if response.status_code == 200:
            data = response.json()
            return data.get('accessToken'), data.get('installationID')
        else:
            logger.error(f"Failed to fetch access token. Status code: {response.status_code}")
            return None, None
    except Exception as e:
        logger.error(f"Error fetching access token: {e}")
        return None, None


@blitzy_exponential_retry()
def get_github_installation_secret_info(installation_id: str, server: str):
    """Fetch GitHub access token from the github handler."""
    try:
        url = f"{server}/v1/github/secret/{installation_id}"
        headers = get_google_authorized_request_headers(url)
        response = requests.get(url, headers=headers)

        if response.status_code == 200:
            data = response.json()
            return data.get('accessToken'), data.get('installationID')
        else:
            logger.error(f"Failed to fetch access token. Status code: {response.status_code}")
            return None, None
    except Exception as e:
        logger.error(f"Error fetching access token: {e}")
        return None, None


@blitzy_exponential_retry()
def get_github_installations(access_token: str):
    """Get GitHub installations for the authenticated user."""
    headers = {
        'Accept': 'application/vnd.github+json',
        'Authorization': f'Bearer {access_token}',
        'X-GitHub-Api-Version': '2022-11-28'
    }

    response = requests.get(
        'https://api.github.com/user/installations',
        headers=headers
    )

    if response.status_code == 200:
        data = response.json()
        return data.get('installations')
    else:
        logger.error(f"Failed to get installations. Status code: {response.status_code}")
        return None


@blitzy_exponential_retry()
def get_credentials_by_repo_id(repo_id: str) -> Tuple[str, str]:
    """Get GitHub credentials by repo id using github_handler service"""

    github_handler_server = os.environ.get('SERVICE_URL_GITHUB')
    if not github_handler_server:
        raise Exception("SERVICE_URL_GITHUB not set, can't fetch credentials by repo id. Please set it.")
    try:
        url = f"{github_handler_server}/v1/github/repositories/{repo_id}/secret"
        logger.info(f"Fetching credentials by repo id from {url}")
        headers = get_google_authorized_request_headers(url)
        response = requests.get(url, headers=headers, timeout=60)
        if response.status_code == 200:
            logger.debug("Successfully fetched credentials by repo id")
            data = response.json()
            return data['accessToken'], data['installationID']
        else:
            err_msg = (f"Failed to fetch credentials from {url} for repo id {repo_id}. "
                       f"Status code: {response.status_code}, response: {response.text}")
            raise FailedToFetchCredentials(err_msg)
    except FailedToFetchCredentials:
        raise  # this is an exception we just raised, no need to process just reraise
    except Exception as e:
        logger.error(f"Error fetching access token: {e}")
        raise FailedToFetchCredentials(
            f"Failed to fetch credentials from {url} for repo id {repo_id}, error {e}"
        ) from e


def _get_token_and_installation_id(server: str, user_id: Optional[str], repo_id: Optional[str]) -> Tuple[str, str]:
    """
    This function is a temporary wrapper around getting access token and installation ID call. We are currently
    migrating to shared github repository and will use repo_id to get the access token and installation ID instead
    of user_id. But blitzy_utils being used in many places, and we can't make change an atomic operation.
    """
    if repo_id and os.environ.get('SERVICE_URL_GITHUB'):
        logger.info(f"Using repo id {repo_id} for getting access token and installation ID.")
        access_token, installation_id = get_credentials_by_repo_id(repo_id=repo_id)
    else:
        logger.info(f"Using user id {user_id} for getting access token and installation ID.")
        access_token, installation_id = get_user_secret_info(user_id=user_id, server=server)
    return access_token, installation_id


@blitzy_exponential_retry()
def get_github_repo(repo_name: str, user_id: str, server: str, create=True, repo_id=None) -> Tuple[Repository, bool]:
    """Get or create a GitHub repository using GitHub App authentication.

    Handles both user and organization repositories based on the installation target type.
    """
    # Get user access token
    access_token, installation_id = _get_token_and_installation_id(server=server, user_id=user_id, repo_id=repo_id)

    if not access_token:
        raise Exception("Failed to get GitHub access token")

    # Initialize GitHub client with user token
    g = Github(access_token)
    installations = get_github_installations(access_token=access_token)

    login_name = ''
    target_type = 'User'
    for installation in installations:
        if str(installation['id']) == installation_id:
            login_name = installation['account']['login']
            target_type = installation['account']['type']
            break
    full_repo_name = f"{login_name}/{repo_name}"

    repo = None
    is_new_repo = False

    repo_options = {
        "name": repo_name,
        "private": True,
        "description": GITHUB_CREATE_REPO_DESCRIPTION,
        "has_issues": True,
        "has_projects": True,
        "has_wiki": True,
        "auto_init": True  # Initialize with README
    }

    try:
        repo = g.get_repo(full_repo_name)
    except GithubException as e:
        if e.status == 404 and create:
            logger.warning(f'Repository does not exist: {repo_name}')
            if create:
                logger.info(f'Creating new repository: {repo_name}')
                if target_type == 'Organization':
                    org = g.get_organization(login_name)
                    # Create new organization repository

                    repo = org.create_repo(**repo_options)
                else:
                    user = g.get_user()
                    repo = user.create_repo(**repo_options)
                is_new_repo = True
        else:
            logger.error(f'Failed to fetch repo: {full_repo_name}')
            raise

    return repo, is_new_repo


def download_single_file(repo_name: str, user_id: str, server: str,
                         file_path: str, commit_hash: str, repo_id: Optional[str] = None) -> Optional[str]:
    """Download a single file from the repository at stored commit hash, with submodule support."""
    github_repo, _ = get_github_repo(repo_name=repo_name, user_id=user_id,
                                     server=server, create=False, repo_id=repo_id)
    if not commit_hash:
        raise ValueError("Invalid commit hash")

    try:
        # First attempt: try to get file content directly from the main repository
        file_content = get_contents_with_retry(repo=github_repo, path=file_path, ref=commit_hash)
        if isinstance(file_content, list):
            logger.warning(f'Attempted to download folder instead of file: {file_path}')
            return None
        logger.info(f'Downloaded file {file_path} from GitHub into memory')
        return decode_file_content(content_file=file_content)

    except GithubException as e:
        # If file not found (404), check if it's in a submodule
        if e.status == 404:
            logger.info(f"File {file_path} not found in main repo, checking submodules...")

            try:
                submodule_repo, relative_path, matching_submodule_path = get_submodule_for_file(
                    repo=github_repo,
                    user_id=user_id,
                    server=server,
                    file_path=file_path,
                    head_commit_hash=commit_hash
                )

                if not submodule_repo:
                    return None

                # Get the submodule commit hash at this specific commit in the main repo
                try:
                    # Get the commit object for the submodule at this specific commit in parent repo
                    submodule_commit_entry = get_contents_with_retry(
                        repo=github_repo, path=matching_submodule_path, ref=commit_hash)
                    submodule_commit_hash = submodule_commit_entry.sha

                    # Try to get the file content using the submodule's commit hash
                    submodule_file = get_contents_with_retry(
                        repo=submodule_repo, path=relative_path, ref=submodule_commit_hash)
                    logger.info(f'Downloaded file {relative_path} at commit {submodule_commit_hash}')
                    return decode_file_content(content_file=submodule_file)
                except GithubException as commit_error:
                    logger.warning(f"Could not get file from submodule at specific commit: {commit_error}")
                    return None

            except Exception as submodule_error:
                logger.error(f"Error processing submodules: {submodule_error}")
                return None
        else:
            logger.error(f"Error downloading {file_path} at commit {commit_hash}: {e}")
            return None


@blitzy_exponential_retry()
def decode_file_content(content_file: ContentFile):
    """
    Safely decode file content from GitHub API
    """
    try:
        # Check if this is a large file (encoding is None)
        if content_file.content == '' or content_file.encoding == 'none':
            # Large file - use download_url
            logger.info(f'Large file detected ({content_file.size} bytes), downloading from URL')
            response = requests.get(content_file.download_url)
            response.raise_for_status()

            # Try to decode as text
            try:
                return response.text
            except UnicodeDecodeError:
                # Try other encodings
                encodings = ['latin-1', 'cp1252', 'utf-16', 'ascii']
                for encoding in encodings:
                    try:
                        return response.content.decode(encoding)
                    except UnicodeDecodeError:
                        continue

                logger.error(f'Could not decode file {content_file.path}, all encodings failed')
                return None
        else:
            # Regular file - use decoded_content (PyGithub handles the encoding)
            decoded_bytes = content_file.decoded_content

            # Try UTF-8 decoding first
            try:
                return decoded_bytes.decode('utf-8')
            except UnicodeDecodeError:
                # Try other common encodings
                encodings = ['latin-1', 'cp1252', 'utf-16', 'ascii']
                for encoding in encodings:
                    try:
                        return decoded_bytes.decode(encoding)
                    except UnicodeDecodeError:
                        continue

                # If all text decodings fail, return as binary string representation
                # or handle binary files differently
                logger.error(f'Could not decode file {content_file.path}, all encodings failed')
                return None  # Return raw bytes for binary files

    except Exception as e:
        logger.error(f'Could not decode file {content_file.path}: {e}')
        return None


def parse_gitmodules(content: str) -> dict:
    """Parse .gitmodules content and return a dictionary mapping paths to repository information."""
    submodules = {}
    current_submodule = None

    lines = content.strip().split('\n')
    for line in lines:
        line = line.strip()
        if not line or line.startswith('#'):
            continue

        if line.startswith('[submodule "'):
            # Extract submodule name between quotes
            name = line[line.find('"')+1:line.rfind('"')]
            current_submodule = name
            submodules[current_submodule] = {'name': current_submodule}
        elif current_submodule and '=' in line:
            key, value = [part.strip() for part in line.split('=', 1)]
            submodules[current_submodule][key] = value

    # Reorganize to use path as the key
    result = {}
    for name, info in submodules.items():
        if 'path' in info and 'url' in info:
            result[info['path']] = {
                'name': name,
                'url': info['url']
            }

    return result


def ssh_to_https_url(ssh_url: str) -> str:
    """Convert a Git SSH URL to HTTPS URL, supporting both GitHub.com and GitHub Enterprise."""
    # Handle standard GitHub SSH URL (**************:owner/repo.git)
    if ssh_url.startswith('**************:'):
        path = ssh_url[15:]  # Remove '**************:'
        return f"https://github.com/{path}"

    # Handle GitHub Enterprise SSH URL (************************:owner/repo.git)
    elif ssh_url.startswith('git@') and ':' in ssh_url:
        # Extract the domain and path
        domain_part = ssh_url[4:ssh_url.find(':')]  # Extract domain part (github.mycompany.com)
        path = ssh_url[ssh_url.find(':')+1:]        # Extract path part (owner/repo.git)
        return f"https://{domain_part}/{path}"

    # Handle SSH protocol URLs (ssh://**************/owner/repo.git)
    elif ssh_url.startswith('ssh://git@'):
        # Remove 'ssh://git@' prefix
        server_path = ssh_url[10:]
        # Split into server and path parts
        server, *path_parts = server_path.split('/', 1)
        path = path_parts[0] if path_parts else ''
        return f"https://{server}/{path}"

    # If it's already an HTTPS URL or another format, return as is
    return ssh_url


def extract_repo_info_from_url(url: str) -> Tuple[str, str, str]:
    """Extract owner, repo name, and domain from a GitHub URL."""
    # Convert to HTTPS if it's SSH
    https_url = ssh_to_https_url(url)

    # Check if it's an HTTPS URL
    if https_url.startswith('https://'):
        # Extract the domain and path
        url_parts = https_url[8:].split('/', 1)
        if len(url_parts) < 2:
            return '', '', url_parts[0] if url_parts else ''

        domain = url_parts[0]  # e.g., github.com or github.mycompany.com
        path = url_parts[1]    # e.g., owner/repo.git

        # Remove .git extension if present
        if path.endswith('.git'):
            path = path[:-4]

        # Split by / to get owner and repo
        parts = path.split('/')
        if len(parts) >= 2:
            return parts[0], parts[1], domain

    # Return empty strings if parsing fails
    return '', '', ''


def get_all_github_file_paths(
    repo_name: str,
    user_id: str,
    server: str,
    commit_hash: str,
    repo_id: Optional[str] = None
) -> List[str]:
    # deprecated - see download_all_git_files_to_disk
    """
    Get all file paths from a GitHub repository, optionally including files in submodules.

    Args:
        repo_name: Name of the repository
        user_id: User ID for authentication
        server: Server URL for authentication
        branch_name: Branch to get files from
        include_submodules: Whether to include files in submodules
        repo_id: ID of the repository, if available(will be used to fetch credentials)

    Returns:
        List of file paths
    """
    github_repo, _ = get_github_repo(repo_name=repo_name, user_id=user_id,
                                     server=server, create=False, repo_id=repo_id)
    all_files = []

    def get_contents(repo: Repository, path: str, commit_hash: str) -> List[str]:
        contents = []
        try:
            items = get_contents_with_retry(repo=repo, path=path, ref=commit_hash)

            # Handle single file
            if not isinstance(items, list):
                items = [items]

            for item in items:
                if item.type == "dir":
                    contents.extend(get_contents(repo, item.path, commit_hash))
                elif item.type == "file":
                    contents.append(item.path)
        except GithubException as e:
            logger.error(f"Error accessing {path}: {e}")
            return []

        return contents

    # Get files in the main repository
    main_files = get_contents(github_repo, "", commit_hash)
    all_files.extend(main_files)

    try:
        gitmodules_text = get_gitmodules_content(repo=github_repo, ref=commit_hash)
        submodules = parse_gitmodules(gitmodules_text)

        # Process each submodule
        for submodule_path, info in submodules.items():
            # Get submodule commit hash
            submodule_commit = get_submodule_commit_sha(
                repo=github_repo,
                submodule_path=submodule_path,
                commit_hash=commit_hash)
            if not submodule_commit:
                logger.warning(f"Couldn't determine commit for submodule {submodule_path}")
                continue

            # Get the submodule repo
            submodule_url = info['url']
            https_url = ssh_to_https_url(submodule_url)
            submodule_owner, submodule_repo_name, domain = extract_repo_info_from_url(https_url)

            if not submodule_owner or not submodule_repo_name:
                logger.error(f"Could not extract owner/repo from URL: {https_url}")
                continue

            # Get access token and create GitHub client
            access_token, _ = _get_token_and_installation_id(server, user_id, repo_id)

            if domain and domain != 'github.com':
                g = Github(base_url=f"https://{domain}/api/v3", login_or_token=access_token)
            else:
                g = Github(access_token)

            try:
                submodule_repo = g.get_repo(f"{submodule_owner}/{submodule_repo_name}")

                # Get all files in the submodule
                submodule_files = get_contents(submodule_repo, "", submodule_commit)

                # Add submodule path prefix to all files
                for file_path in submodule_files:
                    all_files.append(f"{submodule_path}/{file_path}")

                logger.info(f"Added {len(submodule_files)} files from submodule {submodule_path}")

            except GithubException as e:
                logger.error(f"Error accessing submodule repo: {e}")

    except GithubException as e:
        logger.warning(f"No .gitmodules file found: {e}")

    return all_files


@blitzy_exponential_retry()
def get_contents_with_retry(repo: Repository, path: str, ref: str):
    """Wrapper function to get contents with retry logic"""
    return repo.get_contents(path, ref=ref)


def write_contents(
    repo: Repository,
    path: str,
    repo_name: str,
    branch_name: str,
    commit_hash: str
) -> List[BlitzyGitFile]:
    """
    Recursively write repository contents to disk with retry logic for network errors.

    This function will retry individual file operations on network errors but will NOT
    restart the loop. If any file fails after all retries, the entire operation fails.
    """
    contents = []

    # Keep track of processed paths to avoid reprocessing on function retry
    processed_paths = set()

    try:
        # Get directory contents with retry
        items = get_contents_with_retry(repo, path, commit_hash)

        # Handle single file
        if not isinstance(items, list):
            items = [items]

        for item in items:
            # Skip if already processed (in case of function-level retry)
            if item.path in processed_paths:
                logger.info(f"Skipping already processed path: {item.path}")
                continue

            try:
                if item.type == "dir":
                    # Recursive call for directories
                    subcontents = write_contents(
                        repo, item.path, repo_name, branch_name, commit_hash
                    )
                    contents.extend(subcontents)
                    processed_paths.add(item.path)

                elif item.type == "file":
                    # Process individual file with retry
                    file_content = process_single_file(
                        repo=repo,
                        item=item,
                        repo_name=repo_name,
                        branch_name=branch_name,
                        commit_hash=commit_hash
                    )

                    if file_content:
                        contents.append(file_content)
                        processed_paths.add(item.path)

            except Exception as e:
                # If we get here, all retries for this specific file have failed
                logger.error(f"Failed to process {item.path} after all retries: {type(e).__name__}: {str(e)}")
                # Re-raise to fail the entire operation
                raise Exception(f"Critical failure processing {item.path}: {str(e)}") from e

    except UnknownObjectException:
        # Path doesn't exist - this is a permanent error
        logger.error(f"Path not found: {path}")
        raise
    except Exception as e:
        logger.error(f"Error accessing {path}: {type(e).__name__}: {str(e)}")
        raise

    return contents


@blitzy_exponential_retry()
def process_single_file(
    repo: Repository,
    item: ContentFile,
    repo_name: str,
    branch_name: str,
    commit_hash: str
) -> Optional[BlitzyGitFile]:
    """
    Process a single file with retry logic.

    This function is decorated with retry, so connection errors will be retried
    without affecting the parent loop.
    """
    try:
        # For files, we might need to fetch the content separately
        # if decoded_content is not available
        if hasattr(item, 'decoded_content') and item.decoded_content is not None:
            file_text = decode_file_content(content_file=item)
        else:
            # Fetch file content - this will retry on connection errors
            logger.info(f"Fetching content for {item.path}")
            file_content = get_contents_with_retry(repo=repo, path=item.path, ref=commit_hash)
            file_text = decode_file_content(content_file=file_content)

        # Create the file object
        blitzy_file = BlitzyGitFile(path=item.path, text=file_text)

        write_file_to_disk(
            file_path=item.path,
            file_text=file_text,
            repo_name=repo_name,
            branch_name=branch_name
        )

        return blitzy_file
    except AssertionError as e:
        logger.warning(f"Skipping file due to encoding error {item.path}: {type(e).__name__}: {str(e)}")
        return None
    except Exception as e:
        # This will be caught by the retry decorator first
        # Only if all retries fail will it bubble up
        logger.warning(f"Error processing file {item.path}: {type(e).__name__}: {str(e)}")
        raise


@blitzy_exponential_retry()
def get_gitmodules_content(repo: Repository, ref: str) -> str:
    """Get .gitmodules content with retry logic"""
    gitmodules_content = repo.get_contents(".gitmodules", ref=ref)
    return decode_file_content(content_file=gitmodules_content)


@blitzy_exponential_retry()
def get_submodule_repo(github_client: Github, owner: str, repo_name: str):
    """Get submodule repository with retry logic"""
    return github_client.get_repo(f"{owner}/{repo_name}")


@blitzy_exponential_retry()
def get_submodule_commit_sha_with_retry(repo: Repository, submodule_path: str, commit_hash: str) -> Optional[str]:
    """Get submodule commit SHA with retry logic"""
    commit_obj = get_git_commit(sha=commit_hash, repo=repo)
    tree = repo.get_git_tree(commit_obj.tree.sha, recursive=True)

    for item in tree.tree:
        if item.path == submodule_path and item.type == 'commit':
            logger.info(f"Found submodule {submodule_path} with SHA {item.sha}")
            return item.sha

    return None


def download_all_git_files_to_disk(
    repo_name: str,
    branch_name: str,
    user_id: str,
    server: str,
    commit_hash: str,
    repo_id: Optional[str] = None,
) -> List[BlitzyGitFile]:
    """
    Download all files from a git repository including submodules.
    Network errors will be retried at the file level, not the entire operation.
    If any file fails after all retries, the entire operation fails.
    """
    try:
        github_repo, _ = get_github_repo(
            repo_name=repo_name, user_id=user_id, server=server, create=False, repo_id=repo_id
        )
    except Exception as e:
        logger.error(f"Failed to get repository {repo_name}: {e}")
        raise

    all_files = []

    # Get files in the main repository
    try:
        main_files = write_contents(github_repo, "", repo_name, branch_name, commit_hash)
        all_files.extend(main_files)
        logger.info(f"Downloaded {len(main_files)} files from main repository")
    except Exception as e:
        logger.error(f"Failed to download main repository files: {e}")
        raise

    # Process submodules
    try:
        gitmodules_text = get_gitmodules_content(repo=github_repo, ref=commit_hash)
        submodules = parse_gitmodules(gitmodules_text)

        # Process each submodule
        for submodule_path, info in submodules.items():
            try:
                # Get submodule commit hash with retry
                submodule_commit = get_submodule_commit_sha_with_retry(
                    repo=github_repo,
                    submodule_path=submodule_path,
                    commit_hash=commit_hash
                )

                if not submodule_commit:
                    logger.warning(f"Couldn't determine commit for submodule {submodule_path}")
                    continue

                # Parse submodule URL
                submodule_url = info['url']
                https_url = ssh_to_https_url(submodule_url)
                submodule_owner, submodule_repo_name, domain = extract_repo_info_from_url(https_url)

                if not submodule_owner or not submodule_repo_name:
                    logger.error(f"Could not extract owner/repo from URL: {https_url}")
                    continue

                # Get access token and create GitHub client
                access_token, _ = _get_token_and_installation_id(server, user_id, repo_id)

                if domain and domain != 'github.com':
                    g = Github(base_url=f"https://{domain}/api/v3", login_or_token=access_token)
                else:
                    g = Github(access_token)

                # Get submodule repository with retry
                submodule_repo = get_submodule_repo(g, submodule_owner, submodule_repo_name)

                # Get all files in the submodule
                submodule_files = write_contents(
                    submodule_repo, "", repo_name, branch_name, submodule_commit
                )

                # Process each submodule file
                for file in submodule_files:
                    file.path = f"{submodule_path}/{file.path}"
                    all_files.append(file)
                    write_file_to_disk(
                        file_path=file.path,
                        file_text=file.text,
                        repo_name=repo_name,
                        branch_name=branch_name
                    )

                logger.info(f"Downloaded {len(submodule_files)} files from submodule {submodule_path}")

            except Exception as e:
                logger.error(f"Failed to process submodule {submodule_path}: {e}")
                # Fail the entire operation if any submodule fails
                raise

    except UnknownObjectException:
        logger.info("No .gitmodules file found - repository has no submodules")
    except Exception as e:
        logger.error(f"Error processing submodules: {e}")
        # Fail the entire operation
        raise

    logger.info(f"Total files downloaded: {len(all_files)}")
    return all_files


@blitzy_exponential_retry()
def get_head_commit_hash(repo_name: str, user_id: str, server: str,
                         branch_name: str = "main", repo_id: Optional[str] = None) -> str:
    github_repo, _ = get_github_repo(repo_name=repo_name, user_id=user_id, server=server, create=False, repo_id=repo_id)

    # Get the head commit hash
    branch = github_repo.get_branch(branch_name)
    commit_hash = branch.commit.sha
    logger.info(f"Head commit hash for branch {branch_name}: {commit_hash}")
    return commit_hash


@blitzy_exponential_retry()
def get_git_commit(sha: str, repo: Repository):
    return repo.get_git_commit(sha)


@blitzy_exponential_retry()
def get_commit(sha: str, repo: Repository):
    return repo.get_commit(sha)


@blitzy_exponential_retry()
def compare_git_sha(base: str, head: str, repo: Repository):
    return repo.compare(base=base, head=head)


@blitzy_exponential_retry()
def get_changed_files_between_commits(
    repo_name: str,
    user_id: str,
    server: str,
    base_commit: str,
    head_commit: str,
    repo_id: Optional[str] = None,
) -> List[str]:
    """
    Get all files changed between two commits in a GitHub repository, including changes in submodules.

    Args:
        repo_name: Name of the repository
        user_id: User ID for authentication
        server: Server URL for authentication
        base_commit: Base commit hash to compare from
        head_commit: Head commit hash to compare to
        include_submodule_changes: Whether to include files changed in submodules

    Returns:
        List of file paths that were changed between the two commits
    """
    github_repo, _ = get_github_repo(repo_name=repo_name, user_id=user_id, server=server, create=False, repo_id=repo_id)

    try:
        # Get comparison between the two commits
        comparison = compare_git_sha(base=base_commit, head=head_commit, repo=github_repo)

        # Initialize a set to store unique file paths
        changed_files_set = set()

        for commit in comparison.commits:
            full_commit = get_commit(sha=commit.sha, repo=github_repo)
            for file in full_commit.files:
                changed_files_set.add(file.filename)

        # Parse .gitmodules to identify submodules
        try:
            gitmodules_text = get_gitmodules_content(repo=github_repo, ref=head_commit)
            submodules = parse_gitmodules(gitmodules_text)
            logger.info(f"Found {len(submodules)} submodules in repository")

            # Check which submodules have changed
            changed_submodules = []
            for file_path in changed_files_set:
                for submodule_path in submodules:
                    if file_path == submodule_path:
                        changed_submodules.append(submodule_path)
                        break

            # Process each changed submodule
            for submodule_path in changed_submodules:
                info = submodules[submodule_path]
                logger.info(f"Processing changes in submodule: {submodule_path}")

                # Get the submodule commit hash at the base commit
                old_sha = get_submodule_commit_sha(github_repo, submodule_path, base_commit)

                # Get the submodule commit hash at the head commit
                new_sha = get_submodule_commit_sha(github_repo, submodule_path, head_commit)

                # Skip if we don't have both SHAs or they're the same
                if not new_sha or old_sha == new_sha:
                    logger.info(f"Skipping submodule {submodule_path} - no changes detected")
                    continue

                # Get the submodule repository
                submodule_url = info['url']
                https_url = ssh_to_https_url(submodule_url)
                submodule_owner, submodule_repo_name, domain = extract_repo_info_from_url(https_url)

                if not submodule_owner or not submodule_repo_name:
                    logger.error(f"Could not extract owner and repo name from URL: {https_url}")
                    continue

                # Get access to the submodule repository
                access_token, _ = _get_token_and_installation_id(server, user_id, repo_id)

                # Create appropriate Github object based on domain
                if domain and domain != 'github.com':
                    # GitHub Enterprise
                    g = Github(base_url=f"https://{domain}/api/v3", login_or_token=access_token)
                else:
                    # Public GitHub
                    g = Github(access_token)

                try:
                    submodule_repo = g.get_repo(f"{submodule_owner}/{submodule_repo_name}")

                    # Handle case where old_sha doesn't exist (newly added submodule)
                    if not old_sha:
                        logger.info(
                            f"Submodule {submodule_path} appears to be newly added - getting all files at {new_sha}")

                        # Get all files at the new commit hash
                        submodule_files = get_files_in_submodule(
                            submodule_repo=submodule_repo,
                            commit_hash=new_sha
                        )

                        # Add all files with the submodule path prefix
                        for file_path in submodule_files:
                            full_path = f"{submodule_path}/{file_path}"
                            changed_files_set.add(full_path)
                    else:
                        # Original comparison logic when we have both old and new SHA
                        submodule_comparison = compare_git_sha(base=old_sha, head=new_sha, repo=submodule_repo)

                        # Process each commit in the submodule to properly handle pagination
                        for submodule_commit in submodule_comparison.commits:
                            full_submodule_commit = get_commit(sha=submodule_commit.sha, repo=submodule_repo)

                            for submodule_file in full_submodule_commit.files:
                                # Add the file with the submodule path prefix
                                full_path = f"{submodule_path}/{submodule_file.filename}"
                                changed_files_set.add(full_path)

                    # Remove the submodule itself from the list since we're adding its contents
                    changed_files_set.discard(submodule_path)

                except GithubException as e:
                    logger.error(f"Error processing submodule {submodule_path}: {e}")

        except GithubException as e:
            logger.warning(f"Could not find .gitmodules file: {e}")

        # Convert the set to a list
        changed_files = list(changed_files_set)

        logger.info(f"Found {len(changed_files)} changed files between commits {base_commit[:7]} and {head_commit[:7]}")
        return changed_files

    except GithubException as e:
        logger.error(f"Error comparing commits {base_commit} and {head_commit}: {e}")
        return []


@blitzy_exponential_retry()
def get_submodule_commit_sha(repo: Repository, submodule_path: str, commit_hash: str) -> Optional[str]:
    """
    Get the commit SHA that a submodule is pointing to at a specific commit in the parent repository.
    """
    try:
        commit_obj = get_git_commit(sha=commit_hash, repo=repo)
        tree = repo.get_git_tree(commit_obj.tree.sha, recursive=True)

        for item in tree.tree:
            if item.path == submodule_path and item.type == 'commit':
                logger.info(f"Found submodule {submodule_path} with SHA {item.sha} in tree")
                return item.sha
    except GithubException as e:
        logger.warning(f"Could not get submodule from Git tree: {e}")
        return None


def get_files_in_submodule(submodule_repo: Repository, commit_hash: str) -> List[str]:
    """Get all file paths in a submodule repository at a specific commit."""
    file_paths = []

    def get_contents_recursively(path=""):
        try:
            contents = get_contents_with_retry(repo=submodule_repo, path=path, ref=commit_hash)

            # Handle single file
            if not isinstance(contents, list):
                contents = [contents]

            for content in contents:
                if content.type == "dir":
                    get_contents_recursively(content.path)
                elif content.type == "file":
                    file_paths.append(content.path)
        except GithubException as e:
            logger.error(f"Error getting contents at {path}, commit {commit_hash}: {e}")

    get_contents_recursively()
    return file_paths


@blitzy_exponential_retry()
def create_github_commit(
    repo: Repository,
    branch_name: str,
    base_branch: str,
    file_path: str,
    head_commit_hash: str,
    content="",
    create_new_branch=True,
    delete_file=False,
    is_new_repo=False,
    user_id=None,
    server=None,
    commit_message=None
) -> GitCommit | None | bool:
    """Create a commit for a file change with submodule support."""
    logger.info(f"Creating GitHub commit for file: {file_path}")

    # 1. Check if this file is in a submodule
    submodule_repo = None
    relative_path = None
    submodule_path = None

    # TODO: user_id will be gone
    if user_id and server and not is_new_repo:
        # This is the key check to properly detect submodules
        submodule_repo, relative_path, submodule_path = get_submodule_for_file(
            repo=repo,
            user_id=user_id,
            server=server,
            file_path=file_path,
            head_commit_hash=head_commit_hash
        )

    # 2. If file is in a submodule, handle it with the submodule-specific logic
    if submodule_repo:
        logger.info(f"File {file_path} is in submodule {submodule_path}, handling through submodule workflow")
        return commit_to_submodule(
            main_repo=repo,
            branch_name=branch_name,
            base_branch=base_branch,
            submodule_repo=submodule_repo,
            submodule_path=submodule_path,
            relative_path=relative_path,
            content=content,
            create_new_branch=create_new_branch,
            delete_file=delete_file,
            commit_message=commit_message
        )

    # 3. Regular (non-submodule) file handling continues below
    logger.info(f"File {file_path} is not in a submodule, proceeding with normal commit")

    # Regular file handling (not in submodule)
    # Use repo's default branch if base_branch is not provided
    if not base_branch or is_new_repo:
        base_branch = repo.default_branch

    # If branch_name and base_branch are the same, ensure we're using the correct reference
    if branch_name == base_branch:
        raise Exception("Base branch cannot be same as target branch")

    # Handle base branch
    base_branch_ref = None
    try:
        base_branch_ref = repo.get_git_ref(f"heads/{base_branch}")
        logger.info(f"Base branch '{base_branch}' exists")
    except GithubException as e:
        if e.status == 404:
            if is_new_repo:
                logger.info(f"Creating base branch '{base_branch}' with initial README.md")
                repo.create_file(
                    path="README.md",
                    message="Initial commit with README.md",
                    content=f"Repository created by Blitzy",
                    branch=base_branch
                )
                base_branch_ref = repo.get_git_ref(f"heads/{base_branch}")
            else:
                logger.error(f"Base branch '{base_branch}' does not exist and is_new_repo is False")
                raise
        else:
            raise

    # Handle target branch
    branch_ref = None
    try:
        branch_ref = repo.get_git_ref(f"heads/{branch_name}")
        logger.info(f"Branch '{branch_name}' already exists")
    except GithubException as e:
        if e.status == 404:
            if create_new_branch:
                logger.info(f"Creating new branch '{branch_name}' from '{base_branch}'")
                branch_ref = repo.create_git_ref(
                    ref=f"refs/heads/{branch_name}",
                    sha=base_branch_ref.object.sha
                )
            else:
                logger.error(f"Branch '{branch_name}' does not exist and create_new_branch is False")
                raise
        else:
            raise

    # Perform the requested file operation
    if delete_file:
        return commit_delete_file(
            repo=repo,
            branch_name=branch_name,
            branch_ref=branch_ref,
            file_path=file_path,
            commit_message=commit_message
        )
    else:
        return commit_create_or_update_file(
            repo=repo,
            branch_ref=branch_ref,
            file_path=file_path,
            content=content,
            commit_message=commit_message
        )


@blitzy_exponential_retry()
def get_submodule_for_file(repo: Repository, user_id: str, server: str,
                           file_path: str, head_commit_hash: str) \
        -> Tuple[Optional[Repository], Optional[str], Optional[str]]:
    try:
        # Get .gitmodules file content
        try:
            gitmodules_text = get_gitmodules_content(repo=repo, ref=head_commit_hash)
        except GithubException as gm_error:
            logger.warning(f"Could not find .gitmodules file: {gm_error}")
            return None, None, None

        # Parse .gitmodules file
        submodules = parse_gitmodules(gitmodules_text)

        # Find the submodule containing the file path
        matching_submodule_path = None
        for submodule_path in submodules:
            if file_path.startswith(submodule_path + '/'):
                matching_submodule_path = submodule_path
                break

        if matching_submodule_path:
            submodule_info = submodules[matching_submodule_path]
            submodule_url = submodule_info['url']

            # Extract relative path within submodule
            relative_path = file_path[len(matching_submodule_path) + 1:]

            # Convert SSH URL to HTTPS if needed
            https_url = ssh_to_https_url(submodule_url)

            # Extract owner, repo name, and domain
            submodule_owner, submodule_repo_name, domain = extract_repo_info_from_url(https_url)

            if not submodule_owner or not submodule_repo_name:
                logger.error(f"Could not extract owner and repo name from URL: {https_url}")
                return None

            # Get the submodule repository
            access_token, _ = _get_token_and_installation_id(server, user_id, str(repo.id))

            # Create appropriate Github object based on domain
            if domain and domain != 'github.com':
                # GitHub Enterprise
                g = Github(base_url=f"https://{domain}/api/v3", login_or_token=access_token)
            else:
                # Public GitHub
                g = Github(access_token)

            try:
                submodule_repo = g.get_repo(f"{submodule_owner}/{submodule_repo_name}")
                return submodule_repo, relative_path, matching_submodule_path
            except GithubException as repo_error:
                logger.error(f"Could not access submodule repository: {repo_error}")
                return None, relative_path, matching_submodule_path
        else:
            logger.error(f"File {file_path} does not match any submodule path")
            return None, None, None

    except Exception as submodule_error:
        logger.error(f"Error processing submodules: {submodule_error}")
        return None, None, None


@blitzy_exponential_retry()
def commit_delete_file(
    repo: Repository,
    branch_name: str,
    branch_ref: GitRef,
    file_path: str,
    commit_message=None
):
    """Helper function to delete a file and create a commit."""
    try:
        # Get the file content first to get its sha
        file_content = get_contents_with_retry(repo=repo, path=file_path, ref=branch_ref)

        # Delete the file
        message = commit_message or f"Delete file: {file_path}"
        repo.delete_file(
            path=file_path,
            message=message,
            sha=file_content.sha,
            branch=branch_name
        )

        return True

    except GithubException as e:
        if e.status == 404:
            # File doesn't exist, nothing to delete
            logger.warning(f"File {file_path} does not exist, nothing to delete")
            return None
        else:
            # For other exceptions, re-raise
            logger.error(f"Error deleting GitHub file {file_path}: {str(e)}")
            raise


@blitzy_exponential_retry()
def commit_create_or_update_file(
    repo: Repository,
    branch_ref: GitRef,
    file_path: str,
    content: str,
    commit_message=None
):
    """Helper function to create or update a file and create a commit."""
    try:
        # Create blob
        blob = repo.create_git_blob(content, "utf-8")

        # Get the latest commit on the branch
        base_tree = repo.get_git_tree(branch_ref.object.sha)

        # Create tree
        element = InputGitTreeElement(
            path=file_path,
            mode='100644',
            type='blob',
            sha=blob.sha
        )
        tree = repo.create_git_tree([element], base_tree)

        # Create commit
        parent = get_git_commit(sha=branch_ref.object.sha, repo=repo)
        message = commit_message or f"File: {file_path}"

        commit = repo.create_git_commit(
            message,
            tree,
            [parent]
        )

        # Update branch reference to point to the new commit
        branch_ref.edit(commit.sha, force=True)

        return commit
    except Exception as e:
        logger.error(f"Error creating GitHub commit for {file_path}: {str(e)}")
        raise


@blitzy_exponential_retry()
def commit_to_submodule(
    main_repo: Repository,
    branch_name: str,
    base_branch: str,
    submodule_repo: Repository,
    submodule_path: str,
    relative_path: str,
    content="",
    create_new_branch=True,
    delete_file=False,
    commit_message=None
) -> GitCommit:
    """
    Handle committing changes to files within submodules.

    This will:
    1. Commit the change to the submodule
    2. Update the submodule reference in the main repository
    """
    logger.info(f"Handling change to file {relative_path} in submodule {submodule_path}")

    # Get current submodule commit hash from main repo
    try:
        submodule_entry = get_contents_with_retry(repo=main_repo, path=submodule_path, ref=branch_name)
        current_submodule_commit = submodule_entry.sha
        logger.info(f"Current submodule commit is {current_submodule_commit}")
    except GithubException as e:
        logger.error(f"Error getting submodule reference: {e}")
        raise

    try:
        # Get submodule default branch
        submodule_default_branch = submodule_repo.default_branch

        # Try to get existing branch or create a new one
        try:
            submodule_branch_ref = submodule_repo.get_git_ref(f"heads/{branch_name}")
            logger.info(f"Branch {branch_name} already exists in submodule")
        except GithubException as e:
            if e.status == 404 and create_new_branch:
                # Get the default branch ref
                submodule_default_ref = submodule_repo.get_git_ref(f"heads/{submodule_default_branch}")

                # Create new branch in submodule
                submodule_branch_ref = submodule_repo.create_git_ref(
                    ref=f"refs/heads/{branch_name}",
                    sha=submodule_default_ref.object.sha
                )
                logger.info(f"Created branch {branch_name} in submodule")
            else:
                raise

        # Commit the change to the submodule
        if delete_file:
            submodule_commit = commit_delete_file(
                repo=submodule_repo,
                branch_name=branch_name,
                branch_ref=submodule_branch_ref,
                file_path=relative_path,
                commit_message=commit_message
            )
        else:
            submodule_commit = commit_create_or_update_file(
                repo=submodule_repo,
                branch_ref=submodule_branch_ref,
                file_path=relative_path,
                content=content,
                commit_message=commit_message
            )

        if not submodule_commit:
            logger.error("Failed to create commit in submodule")
            raise Exception("Failed to create commit in submodule")

        # Now update the submodule reference in the main repo
        # First, ensure the branch exists in the main repo
        main_branch_ref = None
        try:
            main_branch_ref = main_repo.get_git_ref(f"heads/{branch_name}")
            logger.info(f"Branch {branch_name} exists in main repo")
        except GithubException as e:
            if e.status == 404 and create_new_branch:
                # Get the base branch ref
                main_base_ref = main_repo.get_git_ref(f"heads/{base_branch or main_repo.default_branch}")

                # Create new branch
                main_branch_ref = main_repo.create_git_ref(
                    ref=f"refs/heads/{branch_name}",
                    sha=main_base_ref.object.sha
                )
                logger.info(f"Created branch {branch_name} in main repo")
            else:
                raise

        # Update the submodule in the main repo to point to the new commit
        main_tree = main_repo.get_git_tree(main_branch_ref.object.sha)

        # Create a new tree with the updated submodule
        new_element = InputGitTreeElement(
            path=submodule_path,
            mode='160000',  # Special mode for submodules
            type='commit',
            sha=submodule_commit.sha
        )

        new_tree = main_repo.create_git_tree([new_element], main_tree)

        # Create the commit in the main repo
        parent = get_git_commit(sha=main_branch_ref.object.sha, repo=main_repo)
        main_commit_message = commit_message or f"Update submodule {submodule_path} to include changes to {relative_path}"

        main_commit = main_repo.create_git_commit(
            message=main_commit_message,
            tree=new_tree,
            parents=[parent]
        )

        # Update the branch reference
        main_branch_ref.edit(main_commit.sha, force=True)

        logger.info(f"Updated submodule reference in main repo, commit {main_commit.sha}")

        return main_commit

    except Exception as e:
        logger.error(f"Error updating submodule: {e}")
        raise


@blitzy_exponential_retry()
def create_all_pull_requests(
    repo: Repository,
    head_branch: str,
    user_id: str,
    server: str,
    base_branch="",
    pr_title=None,
    pr_body=None,
    is_new_repo=False
) -> List[PullRequest]:
    """
    Create pull requests for the specified branch and optionally for matching branches in submodules.

    Args:
        repo: The GitHub repository object
        head_branch: The branch containing changes to be merged
        base_branch: The target branch for the PR (defaults to repo's default branch)
        user_id: User ID for authentication (needed for submodule access)
        server: Server URL for authentication (needed for submodule access)
        check_submodules: Whether to also check for and create PRs in submodules
        pr_title: Custom PR title (defaults to "Autonomous changes created by Blitzy")
        pr_body: Custom PR body (defaults to standard message)

    Returns:
        List of created/existing pull request objects
    """
    if not base_branch or is_new_repo:
        base_branch = repo.default_branch

    created_prs = []

    # Create PR for main repository
    main_pr = create_single_pull_request(
        repo=repo,
        head_branch=head_branch,
        base_branch=base_branch,
        pr_title=pr_title or "Autonomous changes created by Blitzy",
        pr_body=pr_body or "This PR contains automated updates created by Blitzy"
    )
    created_prs.append(main_pr)

    # Check for submodules with matching branch
    try:
        # Get .gitmodules file content
        try:
            gitmodules_text = get_gitmodules_content(repo=repo, ref=base_branch)
        except GithubException as e:
            logger.warning(f"No .gitmodules file found assuming no submodules: {e}")
            return created_prs

        # Parse .gitmodules file
        submodules = parse_gitmodules(gitmodules_text)

        # For each submodule, check if the branch exists and create PR if it does
        for submodule_path, submodule_info in submodules.items():
            submodule_url = submodule_info['url']

            # Convert SSH URL to HTTPS if needed
            https_url = ssh_to_https_url(submodule_url)

            # Extract owner, repo name, and domain
            submodule_owner, submodule_repo_name, domain = extract_repo_info_from_url(https_url)

            if not submodule_owner or not submodule_repo_name:
                logger.error(f"Could not extract owner and repo name from URL: {https_url}")
                continue

            # Get the submodule repository
            access_token, _ = _get_token_and_installation_id(server, user_id, str(repo.id))

            # Create appropriate Github object based on domain
            if domain and domain != 'github.com':
                # GitHub Enterprise
                g = Github(base_url=f"https://{domain}/api/v3", login_or_token=access_token)
            else:
                # Public GitHub
                g = Github(access_token)

            try:
                submodule_repo = g.get_repo(f"{submodule_owner}/{submodule_repo_name}")

                # Check if the same branch exists in the submodule
                try:
                    submodule_repo.get_branch(head_branch)
                    logger.info(f"Found matching branch '{head_branch}' in submodule {submodule_path}")

                    # Create PR for the submodule
                    submodule_pr_title = f"Submodule update: {pr_title or 'Autonomous changes created by Blitzy'}"
                    submodule_pr_body = f"This PR is part of changes in the parent repository.\n\n{pr_body or 'This PR contains automated updates created by Blitzy'}"

                    if main_pr:
                        submodule_pr_body += f"\n\nRelated to parent repository PR: {main_pr.html_url}"

                    submodule_pr = create_single_pull_request(
                        repo=submodule_repo,
                        head_branch=head_branch,
                        base_branch=submodule_repo.default_branch,  # Use the submodule's default branch
                        pr_title=submodule_pr_title,
                        pr_body=submodule_pr_body
                    )
                    created_prs.append(submodule_pr)

                    new_body = main_pr.body + f"\n\nSubmodule PR created: {submodule_pr.html_url}"
                    main_pr.edit(body=new_body)

                except GithubException as branch_error:
                    if branch_error.status == 404:
                        logger.info(f"Branch '{head_branch}' does not exist in submodule {submodule_path}")
                    else:
                        logger.error(f"Error checking branch in submodule {submodule_path}: {branch_error}")

            except GithubException as repo_error:
                logger.error(f"Could not access submodule repository: {repo_error}")

    except Exception as e:
        logger.error(f"Error processing submodules for PR: {e}")

    return created_prs


@blitzy_exponential_retry()
def create_single_pull_request(
        repo: Repository,
        head_branch: str,
        base_branch: str,
        pr_title: str,
        pr_body: str
) -> Optional[PullRequest]:
    """Create a single pull request in the specified repository."""
    try:
        # Check if PR already exists
        existing_prs = repo.get_pulls(
            state='open',
            head=f"{repo.owner.login}:{head_branch}",
            base=base_branch
        )

        # Use existing PR if it exists
        for pr in existing_prs:
            logger.info(f"Using existing PR #{pr.number}: {pr.html_url}")
            return pr

        # Create new PR if none exists
        pr = repo.create_pull(
            title=pr_title,
            body=pr_body,
            head=head_branch,
            base=base_branch
        )

        logger.info(f"Created new PR #{pr.number}: {pr.html_url}")
        return pr

    except Exception as e:
        logger.error(f"Error handling pull request: {str(e)}")
        return None


@blitzy_exponential_retry()
def setup_github_branch(
        repo: Repository,
        branch_name: str,
        base_branch="",
        create_new_branch=True,
        delete_existing_branch=True
) -> str:
    if not base_branch:
        base_branch = repo.default_branch

    try:
        # Check if repo is empty by attempting to get the default branch
        is_empty_repo = False
        try:
            repo.get_git_ref(f"heads/{base_branch}")
        except GithubException as e:
            if e.status == 409:
                logger.warning("Cannot create branch for an empty repository")
                is_empty_repo = True
            else:
                # If it's another GitHub exception, re-raise it
                raise

        if not is_empty_repo:
            branch_exists = True
            try:
                # Try to get the branch reference
                branch_ref = repo.get_git_ref(f"heads/{branch_name}")

                # If branch exists and delete_existing_branch is True, delete it
                if delete_existing_branch:
                    logger.info(f'Deleting existing branch {branch_name}')
                    branch_ref.delete()
                    branch_exists = False
                    # We'll need to create a new branch
                else:
                    # Return existing branch if we're not deleting it
                    return branch_ref

            except GithubException as e:
                # Only treat 404 as branch doesn't exist
                if e.status == 404:
                    logger.warning(f'Branch {branch_name} does not exist')
                    branch_exists = False
                else:
                    # For any other GitHub exception, re-raise it
                    raise

            # Create a new branch if needed (branch doesn't exist or was deleted)
            if (not branch_exists) and create_new_branch:
                # Get the base branch reference
                base_ref = repo.get_git_ref(f"heads/{base_branch}")

                # Create new branch from default branch
                branch_ref = repo.create_git_ref(
                    ref=f"refs/heads/{branch_name}",
                    sha=base_ref.object.sha
                )
                logger.info(f'Created new branch {branch_name}')
                return branch_ref
            elif not create_new_branch and not branch_exists:
                raise Exception(f"Branch {branch_name} does not exist and create_new_branch is False")

            return branch_ref
        else:
            return None

    except Exception as e:
        logger.error(f"Error creating GitHub branch {branch_name}: {str(e)}")
        raise
