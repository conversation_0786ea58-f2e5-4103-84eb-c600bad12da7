# Configuration
DIST_DIR := dist
PACKAGE_NAME := $(shell python -c "import toml; print(toml.load('pyproject.toml')['project']['name'])")
REPOSITORY_NAME ?= python-us-east1
REPOSITORY_LOCATION ?= us-east1
ARTIFACTORY_PYTHON_DOMAIN ?= us-east1-python.pkg.dev
PROJECT_ID ?= blitzy-os-dev

# Construct the repository URL
TWINE_REPOSITORY_URL ?= https://$(ARTIFACTORY_PYTHON_DOMAIN)/$(PROJECT_ID)/$(REPOSITORY_NAME)

.PHONY: all install-deps clean update-version build upload test-upload

all: install-deps clean update-version build upload

install-deps:
	python -m pip install --upgrade pip setuptools wheel build twine keyrings.google-artifactregistry-auth toml
	@echo "Installed/updated required dependencies."

clean:
	rm -rf $(DIST_DIR) *.egg-info build
	@echo "Cleaned build artifacts."

update-version:
	python ../update_version.py \
		--repository $(REPOSITORY_NAME) \
		--package $(PACKAGE_NAME) \
		--location $(REPOSITORY_LOCATION) \
		--path .
	@echo "Version updated in pyproject.toml."

build: clean
	python -m build
	@echo "Package built successfully."

upload: build
	@if [ -z "$(PROJECT_ID)" ]; then \
		echo "Error: PROJECT_ID is not set"; \
		exit 1; \
	fi
	python -m twine upload --repository-url $(TWINE_REPOSITORY_URL) "$(DIST_DIR)/*"
	@echo "Package uploaded successfully to $(TWINE_REPOSITORY_URL)."

test-upload: build
	python -m twine upload --repository-url https://test.pypi.org/legacy/ "$(DIST_DIR)/*"
	@echo "Package uploaded successfully to test PyPI."