import sys

import uuid
import traceback
from flask import jsonify
from typing import Protocol, Optional, Type

from flask import request, g

from flask_utils.base_error import BaseError

STATUS_500_RESPONSE = "Something went wrong. Please try again."


class ContextualLogger(Protocol):
    def set_context(self, request_id: Optional[str] = None, correlation_id: Optional[str] = None) -> None:
        """Set request context for logging"""
        ...

    def error(self, message: str, **kwargs) -> None:
        """Log error messages"""
        ...


class RequestContextMiddleware:
    def __init__(self, app=None, logger=None):
        self.logger = logger
        if app:
            self.init_app(app)

    def init_app(self, app, logger=None):
        if logger:
            self.logger = logger

        @app.before_request
        def before_request():
            request_id = str(uuid.uuid4())
            correlation_id = request.headers.get('X-Correlation-ID', str(uuid.uuid4()))
            g.request_id = request_id
            g.correlation_id = correlation_id

            if self.logger:
                self.logger.set_context(request_id=request_id, correlation_id=correlation_id)

        @app.after_request
        def after_request(response):
            response.headers['X-Request-ID'] = g.get('request_id', '')
            response.headers['X-Correlation-ID'] = g.get('correlation_id', '')
            return response

        @app.errorhandler(Exception)
        def handle_exception(error):
            exc_type, exc_value, exc_traceback = sys.exc_info()

            # Format traceback into a single line
            if exc_traceback:
                exc_trace = ''.join(traceback.format_exception(
                    exc_type, exc_value, exc_traceback
                )).replace('\n', ' ')
            else:
                # Fallback to error's traceback
                exc_trace = ''.join(traceback.format_exception(
                    type(error), error, error.__traceback__
                )).replace('\n', ' ')

            if isinstance(error, BaseError):
                # Handle known errors
                response = {
                    'error': error.message,
                }
                status_code = error.status_code

                if error.error_code:
                    response['errorCode'] = error.error_code

                if self.logger:
                    self.logger.warning(
                        f"Error occurred: {error.message}",
                        error_type=error.__class__.__name__,
                        error_message=error.message,
                        status_code=status_code,
                        traceback=exc_trace
                    )
            else:
                # Handle unknown errors
                response = {
                    'error': STATUS_500_RESPONSE,
                }
                status_code = 500

                if self.logger:
                    self.logger.error(
                        f"Unexpected error occurred: {str(error)}",
                        error_type=error.__class__.__name__,
                        error_message=str(error),
                        status_code=status_code,
                        traceback=exc_trace
                    )

            return jsonify(response), status_code
