import base64
import json
import os
from slack_sdk.webhook import WebhookClient


def truncate_text(text, max_length=2900):
    """Truncate text to max_length and add ellipsis if needed"""
    if not text or len(text) <= max_length:
        return text
    return text[:max_length] + "...\n(truncated)"


def format_error_message(error_text):
    """Format error message, extracting the main error if it's a stack trace"""
    if not error_text:
        return "No error message"

    if "at " in error_text and "\n" in error_text:
        main_error = error_text.split('\n')[0]
        return f"{main_error}\n(Full stack trace available in logs)"

    return truncate_text(error_text)


def should_skip_error(error_message):
    """Check if error message should be skipped"""
    if not error_message:
        return False

    skip_patterns = [
        "com.google.cloud.spanner.pgadapter.error.PGException: java.io.EOFException",
        "java.io.EOFException",
        "Traceback (most recent call last)",
        "java.net.SocketException",
        "com.google.api.gax.rpc.UnavailableException",  # Comes from pgadapter
        "com.google.api.gax.rpc.DeadlineExceededException",
        "com.google.api.gax.rpc.InvalidArgumentException"
    ]

    return any(pattern in error_message for pattern in skip_patterns)


def get_error_message(log_entry):
    """Extract error message from various possible locations in the log entry"""
    # First check our existing paths
    json_payload = log_entry.get("jsonPayload", {})
    error_data = json_payload.get("data", {})

    error_message = (
            error_data.get("error_message") or
            json_payload.get("message") or
            log_entry.get("textPayload")
    )

    # If no message found, check httpRequest for errors
    if not error_message:
        http_request = log_entry.get("httpRequest", {})
        if http_request:
            status = http_request.get("status")
            if status and status >= 400:
                error_parts = [
                    f"HTTP {status}",
                    f"Method: {http_request.get('requestMethod', 'UNKNOWN')}",
                    f"URL: {http_request.get('requestUrl', 'UNKNOWN')}",
                    f"Latency: {http_request.get('latency', 'UNKNOWN')}",
                ]
                if http_request.get('userAgent'):
                    error_parts.append(f"User Agent: {http_request.get('userAgent')}")
                error_message = "\n".join(error_parts)

    if not error_message:
        # Print the log entry for debugging but return standard message
        print(f"Debug - No error message found in payload: {json.dumps(log_entry, indent=2)}")
        return "No error message"

    return error_message


def log_alert(cloud_event):
    """
    Cloud Function triggered by Pub/Sub to send error logs to Slack
    """
    try:
        # Initialize Slack webhook
        print(f"Original event: {cloud_event}")
        webhook = WebhookClient(os.environ["SLACK_WEBHOOK_URL"])

        # Get environment (dev/prod)
        environment = os.environ.get("ENVIRONMENT", "unknown")

        # Extract and decode the Pub/Sub message
        pubsub_message = base64.b64decode(cloud_event['data']).decode()
        log_entry = json.loads(pubsub_message)
        print(f"Received log entry: {log_entry}")

        # Extract error from either jsonPayload or textPayload
        json_payload = log_entry.get("jsonPayload", {})
        error_data = json_payload.get("data", {})

        # Get error message using the new function
        raw_error = get_error_message(log_entry)
        error = format_error_message(raw_error)

        # Check for HTTP 500 errors specifically
        if log_entry.get("httpRequest", {}).get("status") == 500:
            print("Skipping HTTP 500 error")
            return {"success": True, "skipped": True}

        # Skip if this is a known error we want to ignore
        if should_skip_error(error):
            print("Skipping known error")
            return {"success": True, "skipped": True}

        error_type = error_data.get("error_type", "HTTP Error" if log_entry.get("httpRequest") else "Unknown")
        correlation_id = json_payload.get("correlation_id", "N/A")
        request_id = json_payload.get("request_id", log_entry.get("spanId", "N/A"))

        # Extract other information
        service = log_entry["resource"]["labels"]["service_name"]
        severity = log_entry.get("severity", "ERROR")
        timestamp = log_entry.get("timestamp", "No timestamp")
        location = log_entry["resource"]["labels"].get("location", "")

        # Get severity emoji
        severity_emoji = {
            "ERROR": "🚨",
            "CRITICAL": "💥",
            "WARNING": "⚠️",
            "DEFAULT": "ℹ️"
        }.get(severity, "🚨")

        # Format blocks with truncated text
        blocks = [
            {
                "type": "header",
                "text": {
                    "type": "plain_text",
                    "text": truncate_text(f"{severity_emoji} Error in {service} [{environment.upper()}]", 150)
                }
            },
            {
                "type": "section",
                "fields": [
                    {
                        "type": "mrkdwn",
                        "text": f"*Service:*\n{truncate_text(service)}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": f"*Environment:*\n{environment.upper()}"
                    }
                ]
            },
            {
                "type": "section",
                "fields": [
                    {
                        "type": "mrkdwn",
                        "text": f"*Error Type:*\n{truncate_text(error_type)}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": f"*Severity:*\n{severity}"
                    }
                ]
            },
            {
                "type": "section",
                "fields": [
                    {
                        "type": "mrkdwn",
                        "text": f"*Request ID:*\n{truncate_text(request_id)}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": f"*Correlation ID:*\n{truncate_text(correlation_id)}"
                    }
                ]
            },
            {
                "type": "section",
                "fields": [
                    {
                        "type": "mrkdwn",
                        "text": f"*Location:*\n{truncate_text(location)}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": f"*Time:*\n{timestamp}"
                    }
                ]
            },
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": f"*Error Message:*\n```{error}```"
                }
            }
        ]

        # Add shortened traceback if available
        traceback = error_data.get("traceback")
        if traceback:
            blocks.append({
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": f"*Traceback:*\n```{truncate_text(traceback)}```"
                }
            })

        # Send to Slack
        response = webhook.send(blocks=blocks)

        if response.status_code != 200:
            print(f"Error sending to Slack: {response.status_code} with body {response.body}")
            return {"success": False, "error": f"Slack API error: {response.status_code}"}

        return {"success": True}

    except Exception as e:
        print(f"Error processing log entry: {e}")
        return {"success": False, "error": str(e)}