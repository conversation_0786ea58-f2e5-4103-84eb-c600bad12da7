openapi: 3.0.0
info:
  title: Job API
  version: 1.0.0
  description: API for managing Cloud Run jobs

paths:
  /auth/login:
    post:
      summary: Authenticate user
      description: Authenticates a user with email and password credentials
      tags:
      - Authentication
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
              - email
              - password
              properties:
                email:
                  type: string
                  format: email
                  description: User's email address
                password:
                  type: string
                  format: password
                  description: User's password
      responses:
        '200':
          description: Authentication successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Login successful
                  token:
                    type: string
                    description: JWT token for authentication
                  email:
                    type: string
                    format: email
                    description: Email of the authenticated user
        '400':
          description: Bad request - missing credentials
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status400'
        '401':
          description: Authentication failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status401'

  /job:
    get:
      summary: Get Cloud Run jobs
      description: Retrieves Cloud Run jobs with support for paging, filtering, and sorting
      security:
      - bearerAuth: []
      parameters:
      - in: query
        name: page
        schema:
          type: integer
          minimum: 1
          default: 1
        description: Page number for pagination
      - in: query
        name: limit
        schema:
          type: integer
          minimum: 1
          maximum: 100
          default: 20
        description: Number of items per page
      - in: query
        name: status
        schema:
          type: string
        description: Filter jobs by status
      - in: query
        name: sortBy
        schema:
          type: string
          enum: [jobName, createdAt, jobStatus, jobPhase]
          default: createdAt
        description: Field to sort by
      - in: query
        name: sortOrder
        schema:
          type: string
          enum: [asc, desc]
          default: desc
        description: Sort order (ascending or descending)
      - in: query
        name: search
        schema:
          type: string
        description: Search by user's first name, user's last name, email, job ID i.e. execution ID and project name
      responses:
        '200':
          description: Successfully retrieved jobs
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CloudRunJobListOutput'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status400'
        '401':
          description: Unauthorized access
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status401'

  /job/{jobId}:
    get:
      summary: Get Cloud Run job by ID
      description: Retrieves a specific Cloud Run job by its ID
      security:
      - bearerAuth: []
      parameters:
      - in: path
        name: jobId
        required: true
        schema:
          type: string
        description: Unique identifier of the job
      responses:
        '200':
          description: Successfully retrieved job details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CloudRunJobDetailOutput'
        '404':
          description: Job not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status404'
        '401':
          description: Unauthorized access
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status401'

  /job/{jobId}/refresh:
    post:
      summary: Refresh job metadata by ID
      description: Refreshes a specific Cloud Run job by its ID
      security:
      - bearerAuth: []
      parameters:
      - in: path
        name: jobId
        required: true
        schema:
          type: string
        description: Unique identifier of the job
      responses:
        '200':
          description: Successfully retrieved job details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CloudRunJobDetailOutput'
        '404':
          description: Job not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status404'
        '401':
          description: Unauthorized access
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status401'

  /job/{jobId}/trigger:
    post:
      summary: Trigger job by ID
      description: Triggers a specific Cloud Run job by its ID
      security:
      - bearerAuth: []
      parameters:
      - in: path
        name: jobId
        required: true
        schema:
          type: string
        description: Unique identifier of the job
      responses:
        '200':
          description: Successfully trigger job
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status200'
        '404':
          description: Job not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status404'
        '401':
          description: Unauthorized access
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Status401'

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT token obtained from the /auth/login endpoint

  schemas:
    Status200:
      type: object
      properties:
        message:
          type: string
          example: Ok

    Status400:
      type: object
      properties:
        message:
          type: string
          example: Invalid request parameters

    Status401:
      type: object
      properties:
        message:
          type: string
          example: Unauthorized access

    Status404:
      type: object
      properties:
        message:
          type: string
          example: Not found

    CloudRunJobOutput:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier for the job tracker
        projectId:
          type: string
          description: ID of the associated project
        projectName:
          type: string
          description: Name of the project
        jobId:
          type: string
          description: ID of the Cloud Run job
        jobPhase:
          type: string
          description: Current phase of the job
        jobStatus:
          type: string
          description: Current status of the job
        jobName:
          type: string
          description: Name of the job
        userId:
          type: string
          description: ID of the associated user
        userEmail:
          type: string
          format: email
          description: Email of the associated user
        isTriggered:
          type: boolean
          description: Whether the job has been triggered
        triggerTopic:
          type: string
          description: Topic that triggered the job
        createdAt:
          type: string
          format: date-time
          description: Time the job tracker was created
        updatedAt:
          type: string
          format: date-time
          description: Time the job tracker was last updated
        eventData:
          type: object
          description: JSON data associated with the job event

    CloudRunJobListOutput:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/CloudRunJobOutput'
        pagination:
          type: object
          properties:
            total:
              type: integer
              description: Total number of items
            pages:
              type: integer
              description: Total number of pages
            page:
              type: integer
              description: Current page number
            limit:
              type: integer
              description: Number of items per page
          required:
          - total
          - pages
          - page
          - limit
      required:
      - items
      - pagination

    CloudRunJobDetailOutput:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier for the job tracker
        projectId:
          type: string
          description: ID of the associated project
        projectName:
          type: string
          description: Name of the project
        techSpecId:
          type: string
          description: ID of the associated technical specification
        codeGenId:
          type: string
          description: ID of the associated code generation
        jobId:
          type: string
          description: ID of the Cloud Run job
        jobPhase:
          type: string
          description: Current phase of the job (from CloudRunJobTrackerPhase enum)
        jobStatus:
          type: string
          description: Current status of the job (from CloudRunJobTrackerStatus enum)
        jobName:
          type: string
          description: Name of the job
        eventData:
          type: object
          description: JSON data associated with the job event
          additionalProperties: true
        jobSubmissionMetadata:
          type: object
          description: Metadata related to job submission
          additionalProperties: true
        isTriggered:
          type: boolean
          description: Whether the job has been triggered
        triggerTopic:
          type: string
          description: Topic that triggered the job
        userEmail:
          type: string
          format: email
          description: Email of the associated user
        createdAt:
          type: string
          format: date-time
          description: Time the job tracker was created
        updatedAt:
          type: string
          format: date-time
          description: Time the job tracker was last updated
