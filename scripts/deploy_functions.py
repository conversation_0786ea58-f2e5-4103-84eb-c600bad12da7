#!/usr/bin/env python3
import argparse
import subprocess
import sys
from pathlib import Path
import yaml
import logging
from typing import Dict, Any

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)


def load_config(config_file: str) -> dict:
    """Load configuration from YAML file."""
    logger.info(f"Loading configuration from: {config_file}")
    path = Path(config_file)
    if not path.exists():
        logger.error(f"Configuration file not found: {config_file}")
        sys.exit(1)

    try:
        with open(path) as f:
            config = yaml.safe_load(f)
            logger.info(f"Configuration loaded successfully")
            logger.info(f"Found {len(config['functions'])} function(s) in configuration")
            return config
    except Exception as e:
        logger.error(f"Error loading configuration: {e}")
        sys.exit(1)


def generate_gcloud_command(
        function_name: str,
        config: Dict[str, Any],
        function_config: Dict[str, Any],
) -> str:
    """Generate gcloud command from config."""
    logger.info(f"\n{'=' * 50}")
    logger.info(f"Preparing deployment for function: {function_name}")
    logger.info(f"Trigger topic: {function_config['trigger_topic']}")
    logger.info(f"Job name: {function_config['job_name']}")
    logger.info(f"Runtime: {config['runtime']}")
    logger.info(f"Region: {config['region']}")
    logger.info(f"Project ID: {config['project_id']}")

    # Prepare environment variables
    env_vars = function_config.get('env_vars', {})
    env_vars.update({
        'JOB_NAME': function_config['job_name'],
        'GCS_BUCKET_NAME': config['gcs_bucket'],
        'BLOB_NAME': config['blob_name'],
        'PROJECT_ID': config['project_id'],
        'TOPIC_NAME': config['topic_name'],
        'TRIGGER_TOPIC': function_config['trigger_topic'],
    })
    env_vars_str = ','.join(f"{k}={v}" for k, v in env_vars.items())

    logger.info("Environment variables to be set:")
    for k, v in env_vars.items():
        logger.info(f"  {k}: {v}")

    # Build command from config
    cmd_parts = ["gcloud functions deploy"]

    # Add required parameters
    cmd_parts.extend([
        function_name,
        "--gen2",
        f"--vpc-connector={config['vpc_connector']}",
        f"--timeout={config['timeout']}s",
        f"--runtime={config['runtime']}",
        f"--region={config['region']}",
        f"--project={config['project_id']}",
        "--source=.",
        "--entry-point=trigger_job",
        f"--trigger-topic={function_config['trigger_topic']}",
        f"--run-service-account={config['service_account']}",
        f"--trigger-service-account={config['service_account']}",
        "--set-env-vars",
        f"'{env_vars_str}'"  # Wrap in quotes to handle special characters
    ])

    return " ".join(cmd_parts)


def deploy_function(command: str, dry_run: bool = False) -> None:
    """Execute the gcloud command."""
    if dry_run:
        logger.info("[DRY RUN] Would execute command:")
        logger.info(command)
        logger.info("[DRY RUN] Skipping actual deployment")
        return

    logger.info("Executing deployment command:")
    logger.info(command)

    try:
        subprocess.run(command, shell=True, check=True)
        logger.info("Deployment completed successfully")
    except subprocess.CalledProcessError as e:
        logger.error(f"Deployment failed with error: {e}")
        sys.exit(1)


def main() -> None:
    parser = argparse.ArgumentParser(description="Deploy Cloud Functions")
    parser.add_argument("--config", required=True,
                        help="Path to config YAML file")
    parser.add_argument("--function", help="Specific function to deploy")
    parser.add_argument("--dry-run", action="store_true",
                        help="Print commands without executing")
    args = parser.parse_args()

    logger.info(f"Starting deployment process")
    logger.info(f"Configuration file: {args.config}")
    if args.function:
        logger.info(f"Deploying single function: {args.function}")
    if args.dry_run:
        logger.info("DRY RUN mode enabled - commands will not be executed")

    config = load_config(args.config)

    if args.function:
        if args.function not in config["functions"]:
            logger.error(f"Function {args.function} not found in configuration")
            sys.exit(1)
        command = generate_gcloud_command(
            args.function,
            config,
            config["functions"][args.function]
        )
        deploy_function(command, args.dry_run)
    else:
        logger.info(f"Preparing to deploy {len(config['functions'])} functions")
        for function_name, function_config in config["functions"].items():
            command = generate_gcloud_command(
                function_name,
                config,
                function_config
            )
            deploy_function(command, args.dry_run)

    logger.info("Deployment process completed")


if __name__ == "__main__":
    main()
