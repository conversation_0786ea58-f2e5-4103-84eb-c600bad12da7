import base64
import json
import os
import requests
import logging

# Get webhook URL from environment variable
WEBHOOK_URL = os.environ["SLACK_WEBHOOK_JOBS_URL"]

# Configure logging to use INFO level instead of ERROR
logger = logging.getLogger('cloud_function_logger')
logger.setLevel(logging.INFO)


def extract_job_details(message_data):
    """
    Extracts job details from different possible message structures
    """
    # Initialize default values
    job_details = {
        'job_name': 'Unknown Job',
        'service': 'Unknown Service',
        'error_message': 'No details available',
        'timestamp': 'Unknown Time'
    }

    try:
        logger.info("Processing message data: %s", json.dumps(message_data, indent=2))

        # Get job name from the resource at root level
        if 'resource' in message_data:
            job_details['job_name'] = message_data['resource']['labels']['job_name']

        # Get details from jsonPayload at root level
        json_payload = message_data.get('jsonPayload', {})
        if json_payload:
            job_details['service'] = json_payload.get('service', 'Unknown Service')
            job_details['error_message'] = json_payload.get('message', 'No details available')
            job_details['timestamp'] = json_payload.get('timestamp', 'Unknown Time')

        logger.info("Extracted job details: %s", json.dumps(job_details, indent=2))

        return job_details

    except Exception as e:
        logger.info("Error extracting job details: %s", str(e))
        logger.info("Message structure: %s", json.dumps(message_data, indent=2))
        return job_details


def create_error_notification(message_data):
    """
    Creates Slack message for error notification
    """
    # Extract job details
    details = extract_job_details(message_data)

    message = {
        "attachments": [
            {
                "color": "#ff0000",  # Red for error
                "blocks": [
                    {
                        "type": "header",
                        "text": {
                            "type": "plain_text",
                            "text": "⚠️ Cloud Run Job Failure Alert"
                        }
                    },
                    {
                        "type": "section",
                        "fields": [
                            {
                                "type": "mrkdwn",
                                "text": f"*Job:*\n{details['job_name']}"
                            },
                            {
                                "type": "mrkdwn",
                                "text": f"*Service:*\n{details['service']}"
                            }
                        ]
                    },
                    {
                        "type": "section",
                        "fields": [
                            {
                                "type": "mrkdwn",
                                "text": f"*Time:*\n{details['timestamp']}"
                            }
                        ]
                    },
                    {
                        "type": "section",
                        "text": {
                            "type": "mrkdwn",
                            "text": f"*Error Details:*\n```{details['error_message']}```"
                        }
                    }
                ]
            }
        ]
    }

    return message


def notify_slack(event, context):
    """
    Cloud Function to process Cloud Run job error notifications and send them to Slack
    """
    try:
        # Decode the Pub/Sub message
        pubsub_message = base64.b64decode(event['data']).decode('utf-8')
        message_data = json.loads(pubsub_message)

        # Debug print using INFO level
        logger.info("Decoded message: %s", json.dumps(message_data, indent=2))

        # Check if this is an error message
        severity = message_data.get('severity', 'DEFAULT')
        if severity not in ['ERROR', 'CRITICAL', 'ALERT', 'EMERGENCY']:
            logger.info("Not an error message, skipping notification")
            return

        # Create error message
        message = create_error_notification(message_data)

        # Send message to Slack using webhook
        response = requests.post(
            WEBHOOK_URL,
            json=message,
            headers={'Content-Type': 'application/json'}
        )
        response.raise_for_status()
        logger.info("Error message sent to Slack: %s", response.status_code)

    except Exception as e:
        logger.info("Error processing message: %s", str(e))
        raise