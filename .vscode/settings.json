{"yaml.schemaStore.enable": false, "editor.formatOnSave": true, "editor.formatOnSaveMode": "modifications", "python.formatting.provider": "black", "python.formatting.blackArgs": ["--line-length=120", "--skip-string-normalization", "--skip-magic-trailing-comma"], "python.formatting.blackPath": "black", "editor.rulers": [80, 120], "editor.tabSize": 4, "editor.insertSpaces": true, "editor.detectIndentation": false, "python.linting.enabled": true, "python.linting.flake8Enabled": true, "python.linting.flake8Args": ["--max-line-length=120"], "python.linting.flake8CategorySeverity.E": "Error", "python.linting.flake8CategorySeverity.W": "Warning", "python.linting.flake8CategorySeverity.F": "Error"}