# archie-function-job-trigger

# Cloud Functions Deployment

This repository contains configuration and deployment scripts for various cloud functions in the Blitzy platform.

## Prerequisites

- Python 3.12 or higher
- Google Cloud SDK
- Make
- Access to Google Cloud projects (dev/prod)
- Service account with required permissions

## Initial Setup

1. **Install Required Packages**
   ```bash
   make init
   ```

2. **Configure Google Cloud SDK**
   ```bash
   # Authenticate with Google Cloud
   gcloud auth login

   # Set the project
   gcloud config set project <project-id>
   ```

## Configuration Files

The repository uses environment-specific YAML files for configuration. Each function in the configuration includes:

- Trigger topic
- Job name
- Environment variables
- Service account details
- Region specifications

Example configuration structure:

```yaml
project_id: blitzy-os-dev
region: us-central1
service_account: <EMAIL>
runtime: python312
timeout: 540
gcs_bucket: blitzy-os-internal
blob_name: public-samples

functions:
  document-generator-trigger:
    trigger_topic: generate-document
    job_name: projects/blitzy-os-dev/locations/us-central1/jobs/document-generator
    env_vars:
      FUNCTION_TYPE: "document"
```

## Deployment

### Local Deployment

1. **Dry Run (Recommended before actual deployment)**
   ```bash
   # Test dev deployment
   make dry-run

   # Test prod deployment
   make dry-run ENV=prod
   ```

2. **Deploy All Functions**
   ```bash
   # Deploy to dev (default)
   make deploy-functions

   # Deploy to prod
   make deploy-functions ENV=prod
   ```

3. **Deploy Single Function**
   ```bash
   # Deploy specific function to dev
   make deploy-single-function function=document-generator-trigger

   # Deploy specific function to prod
   make deploy-single-function function=document-generator-trigger ENV=prod
   ```

## Functions List

The repository manages the following cloud functions:

- Document Generator (`document-generator-trigger`)
- Repository Structure Generator (`repo-structure-generator-trigger`)
- Code Specification Generator (`code-spec-generator-trigger`)
- Code Generator (`code-generator-trigger`)
- Code Validator (`code-validator-trigger`)
- Code Uploader (`code-uploader-trigger`)
- Code Graph Generator (`code-graph-trigger`)
- Code Downloader (`code-downloader-trigger`)

## Project Structure

```
├── env_config/            # Environment configurations
│   ├── env-dev.yaml      # Development environment config
│   └── env-prod.yaml     # Production environment config
├── scripts/
│   └── deploy_functions.py  # Deployment script
├── .github/
│   └── workflows/
│       └── deploy.yml    # CI/CD workflow
├── requirements.txt      # Python dependencies
├── Makefile             # Make commands
└── README.md
```

## Common Issues and Solutions

1. **Authentication Errors**
   ```bash
   # Reauthenticate with gcloud
   gcloud auth login
   gcloud auth application-default login
   ```

2. **Configuration Not Found**
    - Ensure config files exist in env_config directory
    - Check file permissions
    - Verify YAML syntax

3. **Deployment Failures**
    - Check service account permissions
    - Verify project ID and region settings
    - Ensure function names match in configuration

## Development Guide

1. **Adding a New Function**
    - Add function configuration to both dev and prod YAML files
    - Follow existing function configuration pattern
    - Test with dry-run before deployment

2. **Modifying Existing Functions**
    - Update configuration in appropriate YAML files
    - Test changes with dry-run
    - Deploy to dev environment first
    - Verify functionality before prod deployment

3. **Best Practices**
    - Always use dry-run before actual deployment
    - Keep dev and prod configurations in sync
    - Document any special environment variables
    - Test changes in dev before prod
