# Archie Job Tracker

A service to track Cloud Run Jobs triggered by Blitzy. This service is built with Python and deployed on Google Cloud Platform.

## Overview

Archie Job Tracker is a microservice designed to monitor and track Cloud Run jobs within the Blitzy ecosystem. It provides tracking capabilities for job statuses, execution times, and other relevant metrics.

## Prerequisites

- Python 3.12.8
- Docker
- Google Cloud Platform account
- Access to GCP project (`blitzy-os-dev`)

## Required Environment Variables

- `GOOGLE_APPLICATION_CREDENTIALS` or `SERVICE_ACCOUNT_KEY_PATH`: Path to Google Cloud credentials
- `PORT`: Server port (default: 8080)

## Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd archie-job-tracker
```

2. Install dependencies:
```bash
make init
```

## Development

### Setup Development Environment

```bash
make pre-setup
```

This will:
- Install deployment utilities
- Generate Pydantic models from swagger specifications

### Building the Docker Image

```bash
make build
```

The image will be built with the following default configuration:
- Region: us-east1
- Project ID: blitzy-os-dev
- Repository: gcf-artifacts

### Deployment

To deploy to Cloud Run:

```bash
make deploy ENV=dev
```

You can specify a different environment by changing the `ENV` parameter:
```bash
make deploy ENV=prod
```

## Available Make Commands

- `make all`: Runs pre-setup and build
- `make init`: Installs project dependencies
- `make pre-setup`: Installs deployment utils and generates models
- `make build`: Builds the Docker image
- `make clean`: Removes dangling Docker images
- `make deploy`: Deploys the service to Cloud Run

## Configuration

The service can be configured using environment-specific YAML files located in the `env_config` directory:
- `env_config/env-dev.yaml`: Development environment configuration
- `env_config/env-prod.yaml`: Production environment configuration
