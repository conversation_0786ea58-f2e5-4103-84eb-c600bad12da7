# archie-firebase-function

A repository to manage cloud functions

## Post user create action

A cloud run function which gets triggered once new user is created using firebase authentication. This function does
following

- Generates auth token
- Registers created user with blitzy backend.

### Command to deploy Cloud Run Function On User Create Event

```shell
gcloud functions deploy on_create_user \
  --trigger-event providers/firebase.auth/eventTypes/user.create \
  --trigger-resource blitzy-os-dev \
  --runtime python312 \
  --env-vars-file env-dev.yaml
```

## Commands to deploy cloud run function for alerts

```shell
# Create topic
gcloud pubsub topics create error-logs-topic

# Create sink
gcloud logging sinks create error-logs-sink \
    pubsub.googleapis.com/projects/blitzy-os-dev/topics/error-logs-topic \
    --log-filter='resource.type="cloud_run_revision" AND severity>=ERROR'

# Provide necessary permission
gcloud pubsub topics add-iam-policy-binding error-logs-topic \
    --member="serviceAccount:<EMAIL>" \
    --role="roles/pubsub.publisher"
```

## To Deploy on Dev

```shell
gcloud functions deploy captain-panic-job-reporter \
    --gen2 \
    --runtime=python312 \
    --region=us-east1 \
    --source=. \
    --entry-point=handle_log_alert \
    --trigger-topic=error-logs-topic \
    --env-vars-file=env/env-dev.yaml \
    --service-account=<EMAIL> \
    --project blitzy-os-dev
```

## To deploy on Prod

```shell
gcloud functions deploy captain-panic-reporter \
    --gen2 \
    --runtime=python312 \
    --region=us-east1 \
    --source=. \
    --entry-point=handle_log_alert \
    --trigger-topic=error-logs-topic \
    --env-vars-file=env/env-prod.yaml \
    --service-account=<EMAIL> \
    --project blitzy-platform-prod
```

# Cloud run job alert setup

## Commands used for deploying on dev

```shell
# Create topic.
gcloud pubsub topics create cloud-run-job-status 

# Create sink.
gcloud logging sinks create cloud-run-job-status \    
    pubsub.googleapis.com/projects/blitzy-os-dev/topics/cloud-run-job-status \           
    --log-filter='resource.type="cloud_run_job" AND severity>=ERROR'

# Grant permission to service account.
gcloud projects add-iam-policy-binding blitzy-os-dev \
    --member=serviceAccount:<EMAIL> \
    --role=roles/pubsub.publisher

# Deploy job.
gcloud functions deploy captain-panic-job-reporter \
    --gen2 \
    --runtime=python312 \
    --region=us-east1 \
    --source=. \
    --entry-point=handle_job_alert \
    --trigger-topic=cloud-run-job-status \
    --env-vars-file=env/env-dev.yaml \
    --service-account=<EMAIL> \
    --project blitzy-os-dev
```

## Commands used for deploying on prod

```shell
# Create topic.
gcloud pubsub topics create cloud-run-job-status --project blitzy-platform-prod

# Create sink.
gcloud logging sinks create cloud-run-job-status \    
    pubsub.googleapis.com/projects/blitzy-platform-prod/topics/cloud-run-job-status \           
    --log-filter='resource.type="cloud_run_job" AND severity>=ERROR' \

# After executing command given above you will get service account, use it to grant permission
# Grant permission to service account.
gcloud projects add-iam-policy-binding blitzy-platform-prod \
    --member=serviceAccount:<EMAIL> \
    --role=roles/pubsub.publisher --project blitzy-platform-prod

# Deploy job.
gcloud functions deploy captain-panic-job-reporter \
    --gen2 \
    --runtime=python312 \
    --region=us-east1 \
    --source=. \
    --entry-point=handle_job_alert \
    --trigger-topic=cloud-run-job-status \
    --env-vars-file=env/env-prod.yaml \
    --service-account=<EMAIL> \
    --project blitzy-platform-prod
```
