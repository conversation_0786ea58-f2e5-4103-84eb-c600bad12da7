import json
import random
import signal
import string
import sys
from base64 import b64decode
from datetime import datetime
from time import time
from types import FrameType

from blitzy_utils.logger import logger
from firebase_admin import auth as firebase_auth
from firebase_admin import initialize_app as initialize_firebase_admin
from flask import Flask, jsonify, request
from flask_cors import CORS
from github import Github, GithubException
from google.auth import crypt, jwt
from google.cloud import pubsub_v1, secretmanager, storage
from requests import post as post_request

from src.blitzy.utils import gcs_bucket_walk, publish_notification

ID_TOKEN_HEADER_NAME = "X-ID-Token"
BASIC_AUTH_HEADER_NAME = "X-Basic-Auth"
UI_SA_EMAIL = "<EMAIL>"
JWT_AUDIENCE = "https://os.api.blitzy.dev/v1"
PROJECT_ID = "blitzy-os-dev"
UI_AUTH_SECRET_ID = "ui-service-account-key"
FIREBASE_API_KEY = "AIzaSyAVGyXSo6eeRYyxTFo6yn1MX6KHCMxtsSs"
GCS_BUCKET_NAME = "blitzy-os-internal"
BLOB_NAME = "public-samples"
GENERATE_DOCUMENT_TOPIC = "generate-document"
GENERATE_CODE_TOPIC = "generate-code"
GITHUB_TOKEN = "****************************************"

JWT_EXPIRY_LENGTH = 3600
CODE_STRUCTURE_NAME = "Code Structure"

app = Flask(__name__)
# Configure CORS
cors = CORS(app, supports_credentials=True)
storage_client = storage.Client()
secrets_client = secretmanager.SecretManagerServiceClient()
publisher = pubsub_v1.PublisherClient()
initialize_firebase_admin()


@app.route('/v1/auth', methods=['POST'])
def exchange_tokens():
    id_token = request.headers.get(ID_TOKEN_HEADER_NAME)
    basic_auth_header = request.headers.get(BASIC_AUTH_HEADER_NAME)

    if id_token:
        return handle_id_token_auth(id_token)
    elif basic_auth_header and basic_auth_header.startswith('Basic '):
        return handle_basic_auth(basic_auth_header)
    else:
        logger.info("POST Auth, Response 401: Unauthorized")
        return jsonify({"error": "Invalid credentials"}), 401


def handle_id_token_auth(id_token):
    try:
        decoded_id_token = firebase_auth.verify_id_token(id_token)
        return create_jwt_token(decoded_id_token['uid'])
    except Exception as e:
        logger.info(f"POST Auth, Response 401: Unauthorized, {e}")
        return jsonify({"error": "Token Exchange failed"}), 401


def create_jwt_token(uid):
    try:
        now = int(time())

        # build payload
        payload = {
            "iat": now,
            "exp": now + JWT_EXPIRY_LENGTH,
            "iss": UI_SA_EMAIL,
            "aud": JWT_AUDIENCE,
            "sub": UI_SA_EMAIL,
            "email": UI_SA_EMAIL,
            "uid": uid
        }

        service_account_info = json.loads(get_secret(PROJECT_ID, UI_AUTH_SECRET_ID))
        signer = crypt.RSASigner.from_service_account_info(service_account_info)
        jwt_token = jwt.encode(signer, payload).decode('utf-8')
        return jsonify({'access_token': jwt_token}), 200
    except Exception as e:
        logger.info(f"POST Auth, Response 401: Unauthorized, {e}")
        return jsonify({"error": "JWT Token Exchange failed"}), 401


def handle_basic_auth(auth_header):
    try:
        encoded_credentials = auth_header.split(' ')[1]
        decoded_credentials = b64decode(encoded_credentials).decode('utf-8')
        email, password = decoded_credentials.split(':')

        # Call Firebase Auth REST API
        url = f"https://identitytoolkit.googleapis.com/v1/accounts:signInWithPassword?key={FIREBASE_API_KEY}"
        payload = {
            "email": email,
            "password": password,
            "returnSecureToken": True
        }
        response = post_request(url, json=payload)
        response.raise_for_status()

        id_token = response.json()['idToken']
        decoded_id_token = firebase_auth.verify_id_token(id_token)
        return create_jwt_token(decoded_id_token['uid'])
    except Exception as e:
        logger.info(f"POST Auth, Response 401: Unauthorized, {e}")
        return jsonify({"error": "Invalid credentials"}), 401


def get_secret(project_id, secret_id):
    # Build the resource name of the secret version.
    name = f"projects/{project_id}/secrets/{secret_id}/versions/latest"
    secret_response = secrets_client.access_secret_version(request={"name": name})
    return secret_response.payload.data.decode("UTF-8")


@app.route('/v1/job', methods=['POST'])
def trigger_job():
    post_data = request.json
    request_data = get_request_data(post_data)

    print(f'request data: {request_data}')

    if 'error' in request_data:
        logger.info(f"POST Job, Response 400: {request_data}")
        return jsonify(request_data), 400

    prompt = request_data['prompt']
    prefix = request_data['prefix']
    repo_name = get_repo_name(prefix)

    notification_data = {
        'prompt': prompt,
        'repo_name': repo_name,
        'order': 0
    }
    publish_notification(publisher, notification_data, PROJECT_ID, GENERATE_DOCUMENT_TOPIC)

    return jsonify({'github_url': f'https://github.com/blitzy-public-samples/{repo_name}'}), 200


def get_request_data(post_data):
    prefix = ''
    if 'prefix' not in post_data or not isinstance(post_data['prefix'], str):
        return {'error': 'prefix must be set as a string'}
    prefix = post_data['prefix']

    prompt = ''
    if 'prompt' not in post_data or not isinstance(post_data['prompt'], str) or len(post_data['prompt']) < 100:
        return {'error': 'prompt must be a string longer than 100 characters'}
    prompt = post_data['prompt']

    request_data = {
        'prefix': prefix,
        'prompt': prompt,
    }

    return request_data


def get_repo_name(prefix: str):
    hash_part = ''.join(random.choices(string.ascii_lowercase + string.digits, k=6))
    return f"{prefix}-{hash_part}"


def code_test():
    repo_name = 'code-skeptic-scanner-h3gv6b'
    # notification_data = {
    #     'repo_name': repo_name
    # }
    # publish_notification(publisher, notification_data, PROJECT_ID, GENERATE_CODE_TOPIC)

    bucket_name = GCS_BUCKET_NAME
    folder_prefix = f"{BLOB_NAME}/{repo_name}/"
    g = Github(GITHUB_TOKEN)
    org_name = "blitzy-public-samples"
    try:
        # Try to get the organization
        org = g.get_organization(org_name)

        try:
            # Try to get the repository
            repo = org.get_repo(repo_name)
            print(f"Repository {repo_name} exists.")
        except GithubException as e:
            if e.status == 404:
                # Repository doesn't exist, so create it
                repo = org.create_repo(repo_name, description="Repository created autonomously by BlitzCode")
                print(f"Repository {repo_name} created successfully.")
            else:
                # Some other error occurred
                print(f"An error occurred: {e}")
    except GithubException as e:
        print(f"An error occurred while getting the organization: {e}")

    # Walk through the GCS bucket
    for path, _, files in gcs_bucket_walk(storage_client, bucket_name, prefix=folder_prefix):
        print(f"Processing path: {path}")
        for file_name in files:
            # Construct the full blob path
            blob_path = f"{file_name}"

            # Get the blob
            bucket = storage_client.get_bucket(bucket_name)
            blob = bucket.blob(blob_path)

            # Read the content of the blob
            content = blob.download_as_text()

            # Construct the relative path for GitHub
            github_file_path = blob_path[len(folder_prefix):]

            try:
                # Check if file already exists in the repo
                file = repo.get_contents(github_file_path)
                repo.update_file(github_file_path, f"Update {file_name}", content, file.sha, branch='main')
                print(f"Updated {github_file_path}")
            except Exception:
                # File doesn't exist, so create it
                repo.create_file(github_file_path, f"Add {file_name}", content, branch='main')
                print(f"Created {github_file_path}")
    return repo_name


code_test()


@app.route('/uptime-check', methods=['GET'])
def get_uptime_check():
    return jsonify({'accessed_ts': datetime.now().isoformat()}), 200


def shutdown_handler(signal_int: int, frame: FrameType) -> None:
    logger.info(f"Caught Signal {signal.strsignal(signal_int)}")

    from src.utils.logging import flush

    flush()

    # Safely exit program
    sys.exit(0)

# if __name__ == "__main__":
#     # Running application locally, outside of a Google Cloud Environment

#     # handles Ctrl-C termination
#     signal.signal(signal.SIGINT, shutdown_handler)

#     app.run(host="localhost", port=8080, debug=True)
# else:
#     # handles Cloud Run container termination
#     signal.signal(signal.SIGTERM, shutdown_handler)
