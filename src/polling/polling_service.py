from datetime import datetime
from typing import Dict, List, Optional

from blitzy_utils.logger import logger
from common_models.db_client import get_db_session
from common_models.models import CloudRunJobTracker, CloudRunJobTrackerStatus
from google.cloud import run_v2
from sqlalchemy import and_, not_


class CloudRunJobTrackingService:
    """
    Service responsible for tracking Cloud Run jobs status and updating the database.
    Uses a centralized polling approach with a single scheduler.
    """

    def __init__(self):
        """Initialize the tracking service with clients and scheduler."""
        self.jobs_client = run_v2.JobsClient()
        self.executions_client = run_v2.ExecutionsClient()
        self.is_running = False

    def _tracking_cycle(self) -> int:
        """
        Tracks the lifecycle of active Cloud Run jobs.

        Returns:
            Count of processed jobs
        """
        processed_count = 0
        try:
            # Get detached job data
            active_job_data = self._fetch_active_jobs()
            logger.info(f"Tracking {len(active_job_data)} active Cloud Run jobs")

            # Process each job
            for job_data in active_job_data:
                try:
                    self._check_job_status(job_data)
                    processed_count += 1
                except Exception as job_error:
                    logger.error(f"Error processing job {job_data['id']}: {str(job_error)}", exc_info=True)

        except Exception as e:
            logger.error(f"Error in tracking cycle: {str(e)}", exc_info=True)

        return processed_count

    def _fetch_active_jobs(self) -> List[CloudRunJobTracker]:
        """
        Fetches active jobs from the database.

        This method retrieves all jobs from the database that are not in a terminal
        state such as FAILED, CANCELLED, or SUCCESS. It detaches the jobs from the
        session to avoid session-related issues when working with the returned list.

        :returns: A list of active `CloudRunJobTracker` objects.
        :rtype: List[CloudRunJobTracker]
        """
        with get_db_session() as session:
            active_jobs = session.query(CloudRunJobTracker).filter(
                and_(
                    not_(CloudRunJobTracker.job_status.in_([
                        CloudRunJobTrackerStatus.FAILED,
                        CloudRunJobTrackerStatus.CANCELLED,
                        CloudRunJobTrackerStatus.SUCCESS
                    ]))
                )
            ).all()

            return [job for job in active_jobs]

    def _check_job_status(self, job: CloudRunJobTracker) -> None:
        """
        Checks and updates the status of a CloudRun job.

        :param job: The CloudRunJobTracker object representing the job to check.
        :type job: CloudRunJobTracker.
        :return: None.
        """
        logger.info(f"Checking job {job.job_id} status")
        execution_data = self._get_execution_info(job)
        if not execution_data:
            return

        cloud_run_execution = self._retrieve_execution(execution_data)
        new_status = self._determine_status(cloud_run_execution)
        self._save_status_update(job.id, new_status, cloud_run_execution)

    def _get_execution_info(self, job: CloudRunJobTracker) -> Optional[Dict]:
        """
        Retrieve execution information for a Cloud Run job.

        The method processes the job's metadata to extract or construct the `execution_id`,
        `job_name`, and `full_job_name`.

        :param job: A CloudRunJobTracker object representing the job.
        :return: A dictionary containing `execution_id`, `job_name`, and `full_job_name`,
            or None if `execution_id` is unavailable.
        """
        metadata = job.job_submission_metadata or {}
        execution_id = metadata.get('executionId')

        if not execution_id:
            logger.warning(f"Job {job.job_name} (id: {job.id}) has no execution_id")
            return None

        full_job_name = None

        if metadata.get('jobName') and metadata['jobName'].startswith('projects/'):
            full_job_name = metadata['jobName']

        elif metadata.get('executionName') and metadata['executionName'].startswith('projects/'):
            execution_parts = metadata['executionName'].split('/executions/')
            if len(execution_parts) > 1:
                full_job_name = execution_parts[0]

        if not full_job_name:
            project_id = metadata.get('project_id', 'blitzy-os-dev')
            location = metadata.get('location', 'us-central1')
            simple_job_name = job.job_name

            # If job_name already has the full path, extract the simple name
            if job.job_name and job.job_name.startswith('projects/'):
                path_parts = job.job_name.split('/')
                simple_job_name = path_parts[-1]

            full_job_name = f"projects/{project_id}/locations/{location}/jobs/{simple_job_name}"

        # Extract the simple job name from the full path
        simple_job_name = full_job_name.split('/')[-1]

        return {
            'execution_id': execution_id,
            'job_name': simple_job_name,
            'full_job_name': full_job_name
        }

    def _retrieve_execution(self, execution_data: Dict[str, str]) -> Optional[run_v2.Execution]:

        """
        Retrieve execution details using provided execution data.

        This method constructs the full execution name and retrieves the execution
        details using the executions client. If an error occurs during retrieval,
        it logs the error and returns None.

        :param execution_data: A dictionary containing information necessary to
            retrieve the execution, including `full_job_name` and `execution_id`.
        :return: An Execution object if retrieval is successful, otherwise None.
        """
        try:
            execution_name = f"{execution_data['full_job_name']}/executions/{execution_data['execution_id']}"
            execution = self.executions_client.get_execution(name=execution_name)
            return execution
        except Exception as e:
            logger.warning(
                f"Error retrieving execution {execution_data['execution_id']} for job"
                f" {execution_data['job_name']}: {str(e)}",
                exc_info=True)
            return None

    def _determine_status(self, execution: run_v2.types.Execution) -> CloudRunJobTrackerStatus:
        """
        Determines the status of a Cloud Run job execution based on execution attributes.

        :param execution: An `Execution` instance containing job execution details.
        :type execution: run_v2.types.Execution
        :return: The status of the job execution based on its current state.
        :rtype: CloudRunJobTrackerStatus
        """
        if not execution:
            return CloudRunJobTrackerStatus.DELETED

        # For single-task jobs, we can simply check the count fields
        if hasattr(execution, 'succeeded_count') and execution.succeeded_count > 0:
            return CloudRunJobTrackerStatus.SUCCESS

        if hasattr(execution, 'failed_count') and execution.failed_count > 0:
            return CloudRunJobTrackerStatus.FAILED

        if hasattr(execution, 'cancelled_count') and execution.cancelled_count > 0:
            return CloudRunJobTrackerStatus.CANCELLED

        # If no terminal state is reached yet, check if the job has started
        if hasattr(execution, 'start_time') and execution.start_time:
            return CloudRunJobTrackerStatus.RUNNING

        # Default to QUEUED if no other status can be determined
        return CloudRunJobTrackerStatus.QUEUED

    def _save_status_update(
            self,
            job_id: str,
            new_status: CloudRunJobTrackerStatus,
            execution: run_v2.Execution
    ) -> None:
        """
        Saves and updates the status of a job in the database. This function handles
        the database query, checks for job existence, status changes, and updates
        metadata with timestamps.

        :param job_id: Identifier of the job to update.
        :type job_id: str
        :param new_status: The new status to be updated for the job.
        :type new_status: CloudRunJobTrackerStatus
        :param execution: Execution object containing run specific metadata.
        :type execution: run_v2.Execution
        :return: None
        """
        with get_db_session() as session:
            job = session.query(CloudRunJobTracker).filter(CloudRunJobTracker.id == job_id).first()

            if not job:
                logger.warning(f"Job with id {job_id} not found in database")
                return

            if job.job_status == new_status:
                return

            job.job_status = new_status
            metadata = job.job_submission_metadata or {}
            if not metadata.get('timestamps'):
                metadata['timestamps'] = {}

            if execution:
                if execution.start_time and not metadata['timestamps'].get('start_time'):
                    metadata['timestamps']['start_time'] = execution.start_time.isoformat()

                if execution.completion_time and not metadata['timestamps'].get('completion_time'):
                    metadata['timestamps']['completion_time'] = execution.completion_time.isoformat()

            job.job_submission_metadata = metadata
            job.updated_at = datetime.utcnow()

            session.commit()

            logger.info(f"Updated job {job.job_name} (id: {job_id}) status to {new_status.value}")

    def check_specific_job(self, job_id: str) -> Optional[CloudRunJobTrackerStatus]:
        """
        Check the specific job status from the database and update it if necessary.

        :param job_id: The unique identifier of the job to check.
        :type job_id: str
        :return: The updated job status if the job exists, otherwise the current status or None.
        :rtype: Optional[CloudRunJobTrackerStatus]
        """
        with get_db_session() as session:
            job = session.query(CloudRunJobTracker).filter(CloudRunJobTracker.id == job_id).first()

            if not job:
                logger.warning(f"Job with id {job_id} not found in database")
                return None

            current_status = job.job_status

        self._check_job_status(job)

        with get_db_session() as session:
            updated_job = session.query(CloudRunJobTracker).filter(CloudRunJobTracker.id == job_id).first()
            return updated_job.job_status if updated_job else current_status


_tracking_service = None


def get_tracking_service() -> CloudRunJobTrackingService:
    """
    Provides the implementation to retrieve or create a CloudRunJobTrackingService instance.

    This function checks the global `_tracking_service` variable. If it is not instantiated,
    it initializes `_tracking_service` as a `CloudRunJobTrackingService` object and returns it.

    :raise None: This function does not raise any exceptions.
    :return: An instance of CloudRunJobTrackingService.
    """
    global _tracking_service
    if _tracking_service is None:
        _tracking_service = CloudRunJobTrackingService()
    return _tracking_service
