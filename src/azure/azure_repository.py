import base64
from typing import Any, Dict

import requests
from blitzy_utils.logger import logger
from flask import json

from azure.devops.v7_1.git.git_client import GitClient
from azure.devops.v7_1.git.models import GitVersionDescriptor
from src.error.errors import AzureBaseError, FileContentNotFoundError
from src.scm_base.base_classes import BaseRepository
from src.scm_base.base_objects import BlitzyFileContent

AZURE_ENCODING_MAP = {
    65001: "utf-8",
    1200: "utf-16",
    1201: "utf-16-be",
    # Add more as needed
}


class AzureRepository(BaseRepository):

    def __init__(self, azure_devops_connection, project_name, repo_id=None, repo_name=None, tenant_id=None):
        self.azure_devops_connection = azure_devops_connection
        self.project_name = project_name
        self.repo_id = repo_id
        self.repo_name = repo_name
        self.tenant_id = tenant_id

        if not repo_id and not repo_name:
            raise ValueError("Either repo_id or repo_name must be provided.")

        if self.azure_devops_connection is not None:
            self.git_client: GitClient = self.azure_devops_connection.get_client(
                'azure.devops.v7_1.git.git_client.GitClient'
            )
        """
        Initialize Azure DevOps repository instance.
        Args:
            azure_devops_connection: Azure DevOps connection instance
            project_name: Name of the Azure DevOps project
            repo_id: Repository ID (optional)
            repo_name: Repository name (optional)
        """
        self.connection = azure_devops_connection
        self.project_name = project_name
        self.repo_id = repo_id
        self.repo_name = repo_name

        logger.debug(
            f"Initialized AzureRepository for project {project_name}, repo {repo_name or repo_id}"
        )

    def create_branch(
        self, branch_name: str, base_branch: str = "main"
    ) -> Dict[str, Any]:
        """
        Create a new branch.

        :param branch_name: Name of the new branch
        :param base_branch: Base branch to create from
        :return: Dictionary with operation result
        """
        try:
            import requests

            logger.info(
                f"Creating branch '{branch_name}' from '{base_branch}' in repo {self.repo_name or self.repo_id}"
            )

            if not self.connection:
                raise Exception("Azure DevOps connection not available")

            # Get access token from connection
            access_token = self.connection.access_token
            if not access_token:
                raise Exception("Access token not available from connection")

            # First, get the base branch commit SHA
            base_branch_ref = f"refs/heads/{base_branch}"
            refs_url = f"https://dev.azure.com/{self.project_name}/" \
                       f"_apis/git/repositories/{self.repo_id or self.repo_name}/" \
                       f"refs?filter=heads/{base_branch}&api-version=6.0"

            headers = {
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json",
            }

            # Get base branch commit SHA
            refs_response = requests.get(refs_url, headers=headers, timeout=30)
            refs_response.raise_for_status()

            refs_data = refs_response.json()
            base_commit_sha = None

            for ref in refs_data.get("value", []):
                if ref.get("name") == base_branch_ref:
                    base_commit_sha = ref.get("objectId")
                    break

            if not base_commit_sha:
                raise Exception(f"Base branch '{base_branch}' not found")

            # Create new branch reference
            new_branch_ref = f"refs/heads/{branch_name}"
            create_ref_url = f"https://dev.azure.com/{self.project_name}/" \
                             f"_apis/git/repositories/{self.repo_id or self.repo_name}/refs?api-version=6.0"

            ref_data = [
                {
                    "name": new_branch_ref,
                    "oldObjectId": "0000000000000000000000000000000000000000",
                    "newObjectId": base_commit_sha,
                }
            ]

            # Create the branch
            create_response = requests.post(
                create_ref_url, headers=headers, json=ref_data, timeout=30
            )
            create_response.raise_for_status()

            logger.info(
                f"Successfully created branch '{branch_name}' from '{base_branch}'"
            )

            return {
                "success": True,
                "branch_name": branch_name,
                "base_branch": base_branch,
                "commit_sha": base_commit_sha,
                "ref": new_branch_ref,
                "message": f"Branch '{branch_name}' created successfully",
            }

        except Exception as e:
            error_msg = f"Failed to create branch '{branch_name}': {str(e)}"
            logger.error(error_msg)
            return {
                "success": False,
                "error": error_msg,
                "branch_name": branch_name,
                "base_branch": base_branch,
            }

    def manage_pull_request(
        self, user_id: str, pr_id: int, action: str, merge_type: str = "merge"
    ):
        """
        Manages a pull request in a specified Azure DevOps repository. The method
        allows actions such as merging or abandoning a pull request. When merging,
        it checks if the pull request can be merged and attempts to do so using
        the specified merge type.

        Uses Azure DevOps credentials to authenticate and perform operations on
        a repository. Actions are performed based on the input parameters
        defining the operation and target pull request.

        :param user_id: ID of the user who initiated the operation.
        :param pr_id: ID of the pull request to manage.
        :param action: Specifies the action to perform on the pull request.
          Supported actions are "merge" or "abandon".
        :param merge_type: The type of merge to use for the pull request.
          Required if action is "merge". Possible values are "merge", "squash", or "rebase".
        :return: A dictionary containing the success status of the operation, an explanatory
          message, and optionally additional details (e.g., merge status, commit ID).
        :rtype: dict
        """
        try:
            logger.info(
                f"Managing pull request {pr_id} with action '{action}' for user {user_id}"
            )

            # Validate action parameter
            valid_actions = ["merge", "abandon", "complete"]
            if action not in valid_actions:
                raise ValueError(
                    f"Invalid action '{action}'. Valid actions: {valid_actions}"
                )

            if not self.connection:
                raise Exception("Azure DevOps connection not available")

            # Get access token from connection
            access_token = self.connection.access_token
            if not access_token:
                raise Exception("Access token not available from connection")

            headers = {
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json",
            }

            # First, get pull request details to check current status
            pr_url = f"https://dev.azure.com/{self.project_name}/_apis/git/repositories/" \
                     f"{self.repo_id or self.repo_name}/pullrequests/{pr_id}?api-version=6.0"

            pr_response = requests.get(pr_url, headers=headers, timeout=30)
            pr_response.raise_for_status()

            pr_data = pr_response.json()
            current_status = pr_data.get("status")

            logger.info(f"Pull request {pr_id} current status: {current_status}")

            # Prepare update payload based on action
            update_payload = {}

            if action == "merge" or action == "complete":
                # Check if PR can be merged
                if current_status != "active":
                    raise Exception(
                        f"Pull request is not active (status: {current_status})"
                    )

                # Map merge types to Azure DevOps merge strategy
                merge_strategy_map = {
                    "merge": "noFastForward",  # Creates merge commit
                    "squash": "squash",  # Squash commits
                    "rebase": "rebase",  # Rebase and merge
                }

                merge_strategy = merge_strategy_map.get(merge_type, "noFastForward")

                update_payload = {
                    "status": "completed",
                    "lastMergeSourceCommit": pr_data.get("lastMergeSourceCommit"),
                    "completionOptions": {
                        "mergeStrategy": merge_strategy,
                        "deleteSourceBranch": False,  # TODO: Make this configurable
                    },
                }

            elif action == "abandon":
                update_payload = {"status": "abandoned"}

            # Update the pull request
            update_response = requests.patch(
                pr_url, headers=headers, json=update_payload, timeout=30
            )
            update_response.raise_for_status()

            updated_pr = update_response.json()
            final_status = updated_pr.get("status")

            logger.info(
                f"Successfully {action}ed pull request {pr_id}. Final status: {final_status}"
            )

            result = {
                "success": True,
                "pr_id": pr_id,
                "action": action,
                "status": final_status,
                "message": f"Pull request {pr_id} {action}ed successfully",
            }

            # Add merge-specific details
            if action in ["merge", "complete"] and final_status == "completed":
                result.update(
                    {
                        "merge_type": merge_type,
                        "merge_commit_id": updated_pr.get("lastMergeCommit", {}).get(
                            "commitId"
                        ),
                        "source_branch": pr_data.get("sourceRefName"),
                        "target_branch": pr_data.get("targetRefName"),
                    }
                )

            return result

        except Exception as e:
            error_msg = f"Failed to {action} pull request {pr_id}: {str(e)}"
            logger.error(error_msg)
            return {
                "success": False,
                "error": error_msg,
                "pr_id": pr_id,
                "action": action,
            }

    def get_file_content(self, path: str, ref: str | None = None) -> BlitzyFileContent:
        """
        Retrieves the content of a file in the Azure DevOps repository
        by its path and reference (branch, commit SHA, or tag).

        :param path: The path to the file in the repository.
        :param ref: The reference (branch name, commit SHA, or tag) to fetch the file from. Defaults to default branch.
        :return: A dictionary containing the file content and metadata.
        :raises FileContentNotFoundError: If the file is not found at the specified path and reference.
        """
        try:
            used_ref = ref or getattr(self, 'default_branch', 'main')
            logger.info(f"Fetching file '{path}' from Azure repo "
                        f"'{self.repo_name or self.repo_id}' at ref '{used_ref}'")
            logger.debug(f"Preparing GitVersionDescriptor for ref '{used_ref}'")

            # Create version descriptor once
            version_descriptor = GitVersionDescriptor(
                version=used_ref
            ) if used_ref else None

            logger.debug(f"Getting file metadata for '{path}' with version_descriptor: {version_descriptor}")
            # Get the file item (metadata)
            azure_file = self.git_client.get_item(
                repository_id=self.repo_id,
                path=path,
                version_descriptor=version_descriptor,  # Use the object, not dict
                include_content=False,
                include_content_metadata=True,
                latest_processed_change=True
            )

            logger.debug(f"File metadata for '{path}': {azure_file}")

            # Get the actual content as Iterator[Byte]
            logger.debug(f"Getting file content for '{path}'")
            content_iterator = self.git_client.get_item_content(
                repository_id=self.repo_id,
                path=path,
                version_descriptor=version_descriptor
            )

            # Convert Iterator[Byte] to bytes
            content_bytes: bytes = b''.join(content_iterator)
            logger.debug(f"Fetched {len(content_bytes)} bytes for '{path}'")

            is_binary = False
            size = len(content_bytes)  # Default to calculated size

            if hasattr(azure_file, 'content_metadata') and azure_file.content_metadata:
                metadata = azure_file.content_metadata
                encoding_from_azure = getattr(metadata, 'encoding', 'utf-8')
                is_binary = getattr(metadata, 'is_binary', False)
                size = getattr(metadata, 'size', len(content_bytes))
                logger.debug(
                    f"File '{path}' metadata: encoding={encoding_from_azure} "
                    f"(provided by Azure), is_binary={is_binary}, size={size}"
                )
            else:
                metadata = None
                encoding_from_azure = 'utf-8'
                logger.debug(
                    f"No content_metadata for '{path}', defaulting encoding to '{encoding_from_azure}'"
                )

            # Extract encoding and metadata
            encoding_from_azure = getattr(metadata, 'encoding', 'utf-8') if metadata else 'utf-8'
            encoding_was_provided = False
            if isinstance(encoding_from_azure, int):
                encoding_from_azure = AZURE_ENCODING_MAP.get(encoding_from_azure, 'utf-8')
                encoding_was_provided = True
            elif metadata and hasattr(metadata, 'encoding'):
                encoding_was_provided = True

            # Ensure content_bytes is a bytes object
            if not isinstance(content_bytes, bytes):
                content_bytes = bytes(content_bytes)

            # Handle content based on whether it's binary
            if is_binary:
                logger.debug(
                    f"File '{path}' is binary, encoding content as base64 "
                    f"(original encoding: '{encoding_from_azure}', provided: {encoding_was_provided})"
                )
                # Binary files - always base64
                content = base64.b64encode(content_bytes).decode(encoding_from_azure)
                final_encoding = "base64"
            else:
                # Text files - try to keep as text if possible
                try:
                    content = content_bytes.decode(encoding_from_azure)
                    final_encoding = encoding_from_azure
                    logger.debug(
                        f"File '{path}' decoded as text with encoding '{final_encoding}' "
                        f"(provided: {encoding_was_provided})"
                    )
                except (UnicodeDecodeError, LookupError) as decode_err:
                    logger.warning(
                        f"Failed to decode file '{path}' as text with encoding '{encoding_from_azure}'"
                        f" (provided: {encoding_was_provided}): {decode_err}. Falling back to base64."
                    )
                    # Fall back to base64 if decoding fails
                    content = base64.b64encode(content_bytes).decode('utf-8')
                    final_encoding = "base64"

            # Extract author and commit info
            author = None
            commit_message = None
            last_modified = None
            if hasattr(azure_file, 'latest_processed_change') and azure_file.latest_processed_change:
                change = azure_file.latest_processed_change
                if hasattr(change, 'author') and change.author:
                    author = getattr(change.author, 'name', None)
                    last_modified = getattr(change.author, 'date', None)
                commit_message = getattr(change, 'comment', None)
                logger.debug(f"File '{path}' last change: author={author}, "
                             f"last_modified={last_modified}, commit_message={commit_message}")

            if not isinstance(content, (str, bytes, dict)):
                logger.error(f"Content for '{path}' is not string, bytes, or dict. Actual type: {type(content)}")
                raise FileContentNotFoundError(
                    f"Content for file '{path}' in repository {self.repo_name or self.repo_id} \
                    at ref '{ref}' is not a valid type."
                )
            # Ensure content is a string, bytes, or dict
            if isinstance(content, dict):
                content = json.dumps(content)

            if getattr(azure_file, 'git_object_type', None):
                git_object_type = str(getattr(azure_file, 'git_object_type', None))
            else:
                git_object_type = None

            logger.info(f"Successfully fetched file '{path}' from ref '{used_ref}'")
            # Create BlitzyFileContent, handle missing optional params gracefully
            return BlitzyFileContent(
                path=getattr(azure_file, 'path', path),
                content=content,
                sha=getattr(azure_file, 'object_id', None),
                size=size,
                encoding=final_encoding,
                ref=getattr(azure_file, 'commit_id', used_ref),
                author=author if author is not None else None,
                commit_message=commit_message if commit_message is not None else None,
                last_modified=last_modified if last_modified is not None else None,
                platform_data={
                    'url': getattr(azure_file, 'url', None),
                    'commit_id': getattr(azure_file, 'commit_id', None),
                    'original_object_id': getattr(azure_file, 'original_object_id', None),
                    'git_object_type': git_object_type,
                    'is_folder': getattr(azure_file, 'is_folder', False),
                    'is_sym_link': getattr(azure_file, 'is_sym_link', False),
                    'is_binary': is_binary,
                    'original_encoding': encoding_from_azure,
                    '_links': getattr(getattr(azure_file, '_links', None), '__dict__', None)
                }
            )
        except AzureBaseError as e:
            logger.error(f"Azure error fetching file content: {str(e)}")
            raise FileContentNotFoundError(f"Azure error retrieving file '{path}' \
                                           from repository {self.repo_name or self.repo_id} \
                                           at ref '{ref}': {str(e)}")
        except Exception as e:
            logger.error(f"Error fetching file content: {str(e)}")
            raise FileContentNotFoundError(
                f"Error retrieving file '{path}' \
                from repository {self.repo_name or self.repo_id} \
                at ref '{ref}': {str(e)}"
            )

    def get_files(self, ref: str | None = None) -> Dict[str, Any]:
        """
        Retrieves the contents of all files in the Azure DevOps repository
        at the specified reference (branch, commit SHA, or tag).

        :param ref: The reference (branch name, commit SHA, or tag) to fetch the files from. Defaults to default branch.
        :return: A dictionary with file paths as keys and file content/metadata as values.
        :raises FileContentNotFoundError: If the files cannot be retrieved at the specified reference.
        """
        try:
            used_ref = ref or getattr(self, 'default_branch', 'main')
            logger.info(f"Fetching all files from Azure repo '{self.repo_name or self.repo_id}' \
                        at ref '{used_ref}'")

            version_descriptor = GitVersionDescriptor(version=used_ref)

            # Get all items (files) in the repo at the given ref
            items = self.git_client.get_items(
                repository_id=self.repo_id,
                project=self.project_name,
                recursion_level="Full",
                version_descriptor=version_descriptor
            )

            files = {}
            for item in items:
                if getattr(item, 'is_folder', False):
                    continue  # skip folders
                file_path = getattr(item, 'path', None)
                if not file_path:
                    continue
                try:
                    file_content_bytes = self.git_client.get_item_content(
                        repository_id=self.repo_id,
                        project=self.project_name,
                        path=file_path,
                        version_descriptor=version_descriptor
                    )
                    content = b''.join(file_content_bytes)
                    try:
                        decoded_content = content.decode("utf-8")
                    except UnicodeDecodeError as e:
                        logger.error(f"Error decoding file content for '{file_path}': {str(e)}")
                        decoded_content = None
                    files[file_path] = {
                        "path": file_path,
                        "content": decoded_content,
                        "sha": None,
                        "type": "file",
                        "size": len(content) if content else 0,
                        "encoding": "utf-8" if decoded_content is not None else None
                    }
                except Exception as e:
                    logger.error(f"Error fetching file content for '{file_path}': {str(e)}")
                    files[file_path] = {
                        "path": file_path,
                        "content": None,
                        "sha": None,
                        "type": "file",
                        "size": 0,
                        "encoding": None,
                        "error": str(e)
                    }
            return files
        except AzureBaseError as e:
            logger.error(f"Azure error fetching files: {str(e)}")
            raise FileContentNotFoundError(f"Azure error retrieving files \
                                           from repository {self.repo_name or self.repo_id} \
                                           at ref '{ref}': {str(e)}")
        except Exception as e:
            logger.error(f"Error fetching files: {str(e)}")
            raise FileContentNotFoundError(
                f"Error retrieving files from repository {self.repo_name or self.repo_id} at ref '{ref}': {str(e)}"
            )
