from typing import Any, Dict, Optional

from blitzy_utils.logger import logger
from common_models.db_client import get_db_session
from common_models.models import (BlitzyCommit, BlitzyCommitStatus,
                                  CodeGeneration)
from flask import Blueprint
from flask_utils.decorators import flask_pydantic_response, validate_request
from flask_utils.models_config.model_utils import map_to_model
from sqlalchemy.orm import Session

from src.api.models import CodeGeneration as CodeGenerationModel
from src.api.models import (CodeGenGithubCommitOutput,
                            GetCodeGenJobStatusOutput, PRActionInput,
                            PRStatusOutput, Status200)
from src.api.utils.github_utils import (
    get_pull_request_status_from_github_handler,
    manage_pull_request_from_github_handler)
from src.consts import GITHUB_REPO_PR_ACTION
from src.error.errors import (CodeGenInformationNotFoundError,
                              ProjectNotFoundError, PullRequestConflictError)
from src.middleware.decorators import get_user_info
from src.service.blitzy_commit_service import (
    get_blitzy_commit_by_code_gen_id, update_blitzy_commit)
from src.service.code_gen_service import (
    get_latest_code_gen_by_project_id,
    get_tech_spec_by_project_id_and_code_gen_id)
from src.service.project_run_service import get_project_run_by_code_gen_id
from src.service.project_service import (get_project_by_user_id,
                                         update_project_timeline)
from src.service.tech_spec_service import get_tech_spec_by_id

code_gen_bp = Blueprint("code_gen", __name__)


@code_gen_bp.route("/<project_id>/code-gen", methods=["GET"])
@get_user_info
@flask_pydantic_response
def get_code_gen(user_info: Dict[str, Any], project_id: str):
    project_info = get_project_by_user_id(user_info["id"], project_id)
    if not project_info:
        raise ProjectNotFoundError(f"Failed to find project with ID {project_id}")
    with (get_db_session() as session):
        code_generation = fetch_latest_code_gen_by_project_id(project_id, session)
        code_generation_model = map_to_model(code_generation, CodeGenerationModel)
        return code_generation_model, 200


@code_gen_bp.route("/<project_id>/code-gen/run/status", methods=["GET"])
@get_user_info
@flask_pydantic_response
def get_code_gen_job_status(user_info: Dict[str, Any], project_id: str):
    project_info = get_project_by_user_id(user_info["id"], project_id)
    if not project_info:
        raise ProjectNotFoundError(f"Failed to find project with ID {project_id}")

    code_generation = fetch_latest_code_gen_by_project_id(project_id)
    project_run = get_project_run_by_code_gen_id(code_generation.id)
    if not project_run:
        raise CodeGenInformationNotFoundError(message="Code generation job information is not available yet.")

    job_model = map_to_model(project_run, GetCodeGenJobStatusOutput)
    return job_model, 200


@code_gen_bp.route("/<project_id>/code-gen/<code_gen_id>/github/commit", methods=["GET"])
@get_user_info
@flask_pydantic_response
def get_code_gen_github_commit(user_info: Dict[str, Any], project_id: str, code_gen_id: str):
    code_gen = get_tech_spec_by_project_id_and_code_gen_id(project_id, code_gen_id)
    if not code_gen:
        raise CodeGenInformationNotFoundError(message="Code generation is not available yet.")

    blitzy_commit = get_blitzy_commit_by_code_gen_id(code_gen.id)
    if not blitzy_commit:
        raise CodeGenInformationNotFoundError(message=f"Blitzy commit not found for {code_gen_id}.")

    code_gen_model = map_to_model(blitzy_commit, CodeGenGithubCommitOutput)
    return code_gen_model, 200


@code_gen_bp.route("/<project_id>/code-gen/<code_gen_id>/github/pr/status", methods=["GET"])
@get_user_info
@flask_pydantic_response
def get_code_gen_github_pr_status(user_info: Dict[str, Any], project_id: str, code_gen_id: str):
    code_gen = get_tech_spec_by_project_id_and_code_gen_id(project_id, code_gen_id)
    if not code_gen:
        raise CodeGenInformationNotFoundError(message=f"Code generation information is not available for"
                                                      f" {code_gen_id} for project {project_id}.")

    blitzy_commit = get_blitzy_commit_by_code_gen_id(code_gen.id)
    if not blitzy_commit:
        raise CodeGenInformationNotFoundError(message=f"Blitzy commit not found for {code_gen_id}.")

    # Get PR status from GitHub handler
    try:
        pr_status_response = get_pull_request_status_from_github_handler(
            user_info["id"],
            blitzy_commit.org_name,
            blitzy_commit.repo_id,
            str(blitzy_commit.pr_number)
        )

        status_model = PRStatusOutput(status=pr_status_response.get("status"))
        return status_model, 200

    except Exception as e:
        logger.warning(f"Failed to get PR status from GitHub handler for PR {blitzy_commit.pr_number}: {str(e)}")


@code_gen_bp.route("/<project_id>/code-gen/<code_gen_id>/github/pr/action", methods=["POST"])
@validate_request(PRActionInput)
@get_user_info
@flask_pydantic_response
def post_code_gen_github_pr_action(user_info: Dict[str, Any], project_id: str, code_gen_id: str,
                                   payload: PRActionInput):
    code_gen = get_tech_spec_by_project_id_and_code_gen_id(project_id, code_gen_id)
    if not code_gen:
        raise CodeGenInformationNotFoundError(message=f"Code generation information is not available for"
                                                      f" {code_gen_id} for project {project_id}.")

    blitzy_commit = get_blitzy_commit_by_code_gen_id(code_gen.id)
    if not blitzy_commit:
        raise CodeGenInformationNotFoundError(message=f"Blitzy commit not found for {code_gen_id}.")

    process_github_pr_action(user_info, code_gen, blitzy_commit, payload, project_id)
    return Status200(message="Action processed successfully."), 200


def fetch_latest_code_gen_by_project_id(project_id: str, session: Optional[Session] = None) -> CodeGeneration:
    """
    Fetch the latest code generation associated with a given project ID.

    This function uses the provided project ID and optional session object to
    retrieve the most recent code generation record from the underlying data source.
    If no code generation is found for the given project ID, it raises a specific
    exception indicating the absence of such information.

    :param project_id: The unique identifier of the project for which the latest
        code generation needs to be retrieved.
    :param session: Optional session object to be used for querying the data
        source. The session helps maintain database interactions if required.
        Defaults to None.
    :return: The most recent code generation instance for the specified project ID.
    """
    code_generation = get_latest_code_gen_by_project_id(project_id, session)
    if not code_generation:
        raise CodeGenInformationNotFoundError(message="Code generation is not available yet.")
    return code_generation


def process_github_pr_action(user_info: Dict[str, Any], code_gen: CodeGeneration, blitzy_commit: BlitzyCommit,
                             payload: PRActionInput, project_id: str):
    result = manage_pull_request_from_github_handler(user_info["id"], blitzy_commit.org_name, blitzy_commit.repo_id,
                                                     str(blitzy_commit.pr_number), payload.action.value)
    if not result["success"]:
        raise PullRequestConflictError(message=result["message"])

    # Save action.
    commit_status = GITHUB_REPO_PR_ACTION[payload.action]
    update_payload = {
        BlitzyCommit.status: commit_status
    }
    with get_db_session() as session:
        update_blitzy_commit(update_payload, code_gen.id, session)

        if commit_status == BlitzyCommitStatus.CLOSED:
            logger.info(f"Blitzy commit {code_gen.id} is closed. Soft deleting tech spec.")
            tech_spec = get_tech_spec_by_id(code_gen.tech_spec_id, session)
            tech_spec.soft_delete()

        update_project_timeline(project_id, session)
        session.commit()
