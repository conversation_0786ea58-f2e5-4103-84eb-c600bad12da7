from flask import Blueprint, jsonify, request

from src.auth.auth import generate_token, verify_credentials

auth_bp = Blueprint("auth", __name__, url_prefix="/v1/auth")


@auth_bp.route("/login", methods=["POST"])
def login():
    """Handle user login and generate JWT token."""
    data = request.get_json()

    if not data:
        return jsonify({"message": "Missing request body"}), 400

    email = data.get('email')
    password = data.get('password')

    if not email or not password:
        return jsonify({"message": "Email and password are required"}), 400

    if verify_credentials(email, password):
        token = generate_token(email)
        if token:
            return jsonify({
                "message": "Login successful",
                "token": token,
                "email": email
            }), 200
        else:
            return jsonify({"message": "Error generating authentication token"}), 500

    return jsonify({"message": "Invalid credentials"}), 401
