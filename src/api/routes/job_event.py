import base64
import json

from blitzy_utils.logger import logger
from flask import Blueprint, jsonify, request

from src.api.event_handlers.custom_event_handler import custom_event_handler

job_event_bp = Blueprint("job_event", __name__, url_prefix="/v1/events")


@job_event_bp.route("", methods=["POST"])
def handle_events():
    logger.info("Received event request at /v1/events")
    logger.info(f"Content-Type: {request.headers.get('Content-Type')}")

    cloud_event_type = request.headers.get('Ce-Type')

    if cloud_event_type:
        logger.info("=== DIRECT EVENTARC EVENT ===")
        logger.info(f"Event Type: {cloud_event_type}")

        logger.info("Event Headers:")
        headers_dict = dict(request.headers)
        logger.info(json.dumps(headers_dict, indent=2))

        try:
            event_data = request.get_json()
            logger.info("Event Data:")
            logger.info(json.dumps(event_data, indent=2))
        except Exception as e:
            logger.error(f"Error parsing event data: {e}")
            logger.info(f"Raw body: {request.data.decode('utf-8', errors='replace')}")

    else:
        logger.info("=== PUB/SUB EVENT ===")
        envelope = request.get_json()

        if not envelope:
            logger.error("No Pub/Sub message received or invalid JSON")
            return jsonify(success=False, error="Invalid request"), 400

        logger.info("Pub/Sub Envelope:")
        logger.info(json.dumps(envelope, indent=2))

        if isinstance(envelope, dict) and 'message' in envelope:
            pubsub_message = envelope['message']

            attributes = pubsub_message.get('attributes', {})
            logger.info("Message Attributes:")
            logger.info(json.dumps(attributes, indent=2))

            if 'data' in pubsub_message:
                data_encoded = pubsub_message.get('data', '')
                try:
                    data_decoded = base64.b64decode(data_encoded).decode('utf-8')
                    logger.info("Decoded data:")
                    data_json = json.loads(data_decoded)
                    logger.info(json.dumps(data_json, indent=2))
                    custom_event_handler(data_json)
                except Exception as e:
                    logger.error(f"Error decoding data: {e}")

    return jsonify(success=True), 200
