from datetime import datetime
from typing import Dict, Union

from blitzy_utils.common import publish_notification
from blitzy_utils.logger import logger
from common_models.db_client import get_db_session
from common_models.models import CloudRunJobTracker, CloudRunJobTrackerStatus
from flask import Blueprint, jsonify, request
from flask_utils.decorators import flask_pydantic_response
from flask_utils.models_config.model_utils import map_to_model
from sqlalchemy.exc import InvalidRequestError
from sqlalchemy.orm import joinedload

from src.api.models import CloudRunJobDetailOutput, Status200
from src.consts import PROJECT_ID, TRIGGER_TOPICS, publisher
from src.error.error import InvalidPayloadError, NotFoundError
from src.polling.polling_service import (CloudRunJobTrackingService,
                                         get_tracking_service)
from src.service.cloud_run_job_tracker_service import (
    get_cloud_run_job_tracker_by_id, get_paginated_jobs)

job_api_bp = Blueprint("job_api", __name__, url_prefix="/v1/job")

job_custom_mapper = {
    "projectName": "project.name",
    "userEmail": "user.email"
}


@job_api_bp.route("", methods=["GET"])
def get_jobs():
    """
    Get a paginated list of Cloud Run jobs with filtering, sorting, searching, and job type filtering.

    Query Parameters:
    - page: Page number (default: 1)
    - limit: Items per page (default: 20, max: 100)
    - status: Filter by job status
    - sortBy: Field to sort by (jobName, createdAt, jobStatus, jobPhase)
    - sortOrder: Sort order (asc, desc)
    - search: Search term for project name, user email, or execution ID
    - jobType: Filter by job type (extracted from executionId, e.g., for "code-downloader-qh4wp" the job type is
    "code-downloader")
    - planType: Filter by user subscription plan type (FREE, PRO, ENTERPRISE, TEAMS)
    - duration: Filter running jobs by duration (1, 2, 3, 3+). Only applies when status is RUNNING.

    Returns:
    - 200 OK: List of jobs with pagination info
    - 400 Bad Request: Invalid parameters
    """
    try:
        # Parse query parameters
        page = max(1, int(request.args.get("page", 1)))
        limit = min(100, max(1, int(request.args.get("limit", 20))))
        status = request.args.get("status")
        sort_by = request.args.get("sortBy", "createdAt")
        sort_order = request.args.get("sortOrder", "desc")
        search_term = request.args.get("search", "")
        job_type = request.args.get("jobType")
        plan_type = request.args.get("planType")
        duration = request.args.get("duration")

        # Validate sort parameters
        valid_sort_fields = {
            "jobName": CloudRunJobTracker.job_name,
            "createdAt": CloudRunJobTracker.created_at,
            "jobStatus": CloudRunJobTracker.job_status,
            "jobPhase": CloudRunJobTracker.job_phase
        }

        if sort_by not in valid_sort_fields:
            raise InvalidPayloadError(f"Invalid sortBy parameter. Valid values are: "
                                      f"{', '.join(valid_sort_fields.keys())}")

        if sort_order not in ["asc", "desc"]:
            raise InvalidPayloadError("Invalid sortOrder parameter. Valid values are: asc, desc")

        # Validate duration parameter
        if duration:
            if status != "RUNNING":
                raise InvalidPayloadError("Duration parameter can only be used when status is RUNNING")
            if duration not in ["1", "2", "3", "3+"]:
                raise InvalidPayloadError("Invalid duration parameter. Valid values are: 1, 2, 3, 3+")

        # Execute database query
        with get_db_session() as session:
            try:
                jobs, total = get_paginated_jobs(
                    session=session,
                    page=page,
                    limit=limit,
                    status=status,
                    sort_by=valid_sort_fields[sort_by],
                    sort_order=sort_order,
                    search_term=search_term,
                    job_type=job_type,
                    plan_type=plan_type,
                    duration=duration
                )

                # Transform jobs to output format
                result = {
                    "items": [format_job(job) for job in jobs],
                    "pagination": {
                        "total": total,
                        "pages": (total + limit - 1) // limit,  # Ceiling division
                        "page": page,
                        "limit": limit
                    }
                }

                return jsonify(result), 200

            except ValueError as e:
                logger.error(f"Invalid parameter in request: {str(e)}")
                raise InvalidPayloadError("Invalid request parameter")

    except Exception as e:
        logger.error(f"Error retrieving jobs: {str(e)}", exc_info=True)
        return jsonify({"message": "Internal server error"}), 500


def format_job(job: CloudRunJobTracker) -> Dict[str, Union[str, bool, Dict]]:
    """
    Formats a CloudRunJobTracker instance into a dictionary with specific fields.

    :param job: CloudRunJobTracker instance to be formatted.
    :return: Dictionary containing formatted job details.
    """
    result = {
        "id": str(job.id),
        "projectId": job.project_id,
        "projectName": job.project.name if job.project else None,
        "jobId": job.job_id,
        "jobPhase": job.job_phase.value if job.job_phase else None,
        "jobStatus": job.job_status.value if job.job_status else None,
        "jobName": job.job_name,
        "userId": job.user_id,
        "userEmail": job.user.email if job.user else None,
        "userFirstName": job.user.first_name if job.user else None,
        "userLastName": job.user.last_name if job.user else None,
        "isTriggered": job.is_triggered,
        "createdAt": job.created_at.isoformat() if job.created_at else None,
        "updatedAt": job.updated_at.isoformat() if job.updated_at else None,
        "eventData": job.event_data
    }

    # Add planName if available
    plan_name = None
    if job.user and hasattr(job.user, "subscription") and job.user.subscription:
        plan_name = job.user.subscription.plan_name.value if job.user.subscription.plan_name else None
    result["planName"] = plan_name

    # Extract executionId from metadata
    if job.job_submission_metadata and "executionId" in job.job_submission_metadata:
        result["executionId"] = job.job_submission_metadata["executionId"]
    else:
        result["executionId"] = None

    return result


@job_api_bp.route("/<job_id>", methods=["GET"])
@flask_pydantic_response
def get_job(job_id):
    job = get_cloud_run_job_tracker_by_id(job_id)

    if not job:
        raise NotFoundError(f"Job with ID {job_id} not found")

    response = map_to_model(job, CloudRunJobDetailOutput, custom_mapping=job_custom_mapper)

    return response, 200


@job_api_bp.route("/<job_id>/refresh", methods=["POST"])
@flask_pydantic_response
def refresh_job_status(job_id):
    # Get tracking service
    tracking_service = get_tracking_service()

    # Try to refresh job status
    updated_status = tracking_service.check_specific_job(job_id)

    if updated_status is None:
        raise NotFoundError(f"Job with ID {job_id} not found")

    # Get updated job details
    with get_db_session() as session:
        job = session.query(CloudRunJobTracker).options(
            joinedload(CloudRunJobTracker.user)
        ).filter(
            CloudRunJobTracker.id == job_id
        ).first()

        if not job:
            raise NotFoundError(f"Job with ID {job_id} not found")

        response = map_to_model(job, CloudRunJobDetailOutput)

        return response, 200


@job_api_bp.route("/<job_id>/trigger", methods=["POST"])
@flask_pydantic_response
def trigger_job(job_id):
    with get_db_session() as session:
        job = session.query(CloudRunJobTracker).filter(
            CloudRunJobTracker.id == job_id
        ).first()

        if not job:
            raise NotFoundError(f"Job with ID {job_id} not found")

        # Check if the job has a trigger topic
        trigger_topic = job.trigger_topic
        if not trigger_topic:
            raise InvalidRequestError(f"Job with ID {job_id} does not have a trigger topic")

        if job.is_triggered:
            raise InvalidRequestError(f"Job with ID {job_id} has already been triggered")

        publish_to_topic(job)

        job.is_triggered = True
        job.job_status = CloudRunJobTrackerStatus.RE_TRIGGERED
        session.commit()

        return Status200(message="success"), 200


def publish_to_topic(job: CloudRunJobTracker) -> str | None:
    """
    Publish a notification to a given Cloud Pub/Sub topic.

    :param job: Instance of CloudRunJobTracker containing event data and topic details.
    :type job: CloudRunJobTracker
    :return: Confirmation string or None if no notification is published.
    :rtype: str or None
    """
    payload = job.event_data
    trigger_topic = job.trigger_topic
    publish_notification(publisher, payload, PROJECT_ID, trigger_topic)


@job_api_bp.route("/tracking/run", methods=["POST"])
def run_tracking_cycle():
    """
    Manually run a tracking cycle to update job statuses.
    Can be triggered by Cloud Scheduler.

    Returns:
        200 OK: Tracking cycle completed successfully
        500 Internal Server Error: Error during tracking cycle
    """
    try:
        # Create a fresh tracking service (no scheduler)
        tracking_service = CloudRunJobTrackingService()

        # Run a single tracking cycle
        processed_count = tracking_service._tracking_cycle()

        return jsonify({
            "message": "Tracking cycle completed successfully",
            "processed_jobs": processed_count,
            "timestamp": datetime.utcnow().isoformat()
        }), 200

    except Exception as e:
        logger.error(f"Error in manual tracking cycle: {str(e)}", exc_info=True)
        return jsonify({
            "message": f"Error in tracking cycle: {str(e)}",
            "timestamp": datetime.utcnow().isoformat()
        }), 500


# API for triggering custom jobs
@job_api_bp.route("/trigger/custom/job", methods=["POST"])
def trigger_custom_job():
    """
    Trigger a custom job by publishing the provided payload to the specified trigger topic.
    Expects JSON body with at least 'trigger_topic' and the event data (payload).
    Saves the job to the database using get_cloud_run_job_tracker_model.
    """
    try:
        data = request.get_json()
        if not data:
            raise InvalidPayloadError("Missing JSON body")

        trigger_topic = data.get("trigger_topic")
        if not trigger_topic:
            raise InvalidPayloadError("Missing 'trigger_topic' in request body")

        # Remove trigger_topic from data to get the payload
        payload = {k: v for k, v in data.items() if k != "trigger_topic"}
        if not payload:
            raise InvalidPayloadError("Missing payload/event data in request body")

        publish_notification(publisher, payload, PROJECT_ID, trigger_topic)

        return jsonify({"message": "success", "job_id": data.get("job_id")}), 200
    except Exception as e:
        logger.error(f"Error triggering custom job: {str(e)}", exc_info=True)
        return jsonify({"message": "An internal error occurred while triggering the custom job."}), 500


@job_api_bp.route("/topics", methods=["GET"])
def get_topics():
    """
    Get the list of available trigger topics.

    Returns:
    - 200 OK: List of available topics
    """
    return jsonify({
        "topics": TRIGGER_TOPICS
    }), 200
