from blitzy_utils.logger import logger
from common_models.db_client import get_db_session
from common_models.models import (CloudRunJobTracker, CloudRunJobTrackerPhase,
                                  CloudRunJobTrackerStatus)

from src.service.cloud_run_job_tracker_service import \
    get_cloud_run_job_by_job_id

CUSTOM_JOB_SUBMISSION = "CUSTOM_JOB_SUBMISSION"

STATUS_MAPPER = {
    "QUEUED": CloudRunJobTrackerStatus.QUEUED,
    "RUNNING": CloudRunJobTrackerStatus.RUNNING,
    "SUCCESS": CloudRunJobTrackerStatus.SUCCESS,
    "FAILED": CloudRunJobTrackerStatus.FAILED,
    "CANCELLED": CloudRunJobTrackerStatus.CANCELLED,
    "IN_PROGRESS": CloudRunJobTrackerStatus.IN_PROGRESS
}


def custom_event_handler(event: dict):
    event_type = event.get("eventType")
    logger.info(f"Attempting to process event with type: {event_type}")

    if event_type != CUSTOM_JOB_SUBMISSION:
        logger.warning(f"Send event with unknown type: {event_type}. Skipping.")
        return

    event_data = event.get("eventData")
    if not event_data:
        logger.warning("Event data not found in event. Skipping.")
        return

    execution_id = event.get("jobSubmissionMetadata", {}).get("executionId")
    if not execution_id:
        logger.warning("Execution ID not found in event. Skipping.")
        return

    # Check if this is a retrigger of an existing job
    job_id = event_data.get("job_id")
    if job_id:
        with get_db_session() as session:
            # Find all previous instances of this job that are in FAILED status
            previous_failed_jobs = session.query(CloudRunJobTracker).filter(
                CloudRunJobTracker.job_id == job_id,
                CloudRunJobTracker.job_status == CloudRunJobTrackerStatus.FAILED
            ).all()

            # Update their status to RE_TRIGGERED
            for job in previous_failed_jobs:
                job.job_status = CloudRunJobTrackerStatus.RE_TRIGGERED
                job.is_triggered = True
                logger.info(f"Updated previous failed job {job.id} to RE_TRIGGERED status")
                session.add(job)
            
            session.commit()

    existing_job = get_cloud_run_job_by_job_id(execution_id)
    if existing_job:
        logger.info(f"Job {execution_id} already exists in database. Skipping.")
        return

    cloud_run_job_tracker = get_cloud_run_job_tracker_model(event)
    with get_db_session() as session:
        session.add(cloud_run_job_tracker)
        session.commit()
        logger.info(f"Saved job tracker for job {cloud_run_job_tracker.job_name} (id: {cloud_run_job_tracker.id})")


def get_cloud_run_job_tracker_model(event: dict) -> CloudRunJobTracker:
    event_data = event.get("eventData")
    user_id = event_data.get("user_id")
    tech_spec_id = event_data.get("tech_spec_id")
    project_id = event_data.get("project_id")
    code_gen_id = event_data.get("code_gen_id")
    trigger_topic = event.get("triggerTopic")
    job_name = event_data.get("jobName")

    job_submission_metadata = event.get("jobSubmissionMetadata")
    job_id = job_submission_metadata.get("executionId")
    status = job_submission_metadata.get("status")
    job_phase = get_job_phase(job_id)

    cloud_run_job_tracker = CloudRunJobTracker(
        user_id=user_id,
        trigger_topic=trigger_topic,
        job_id=job_id,
        job_name=job_name,
        event_data=event_data,
        job_submission_metadata=job_submission_metadata,
        job_status=STATUS_MAPPER[status],
        job_phase=job_phase,
        is_triggered=False
    )

    if project_id:
        cloud_run_job_tracker.project_id = project_id

    if tech_spec_id:
        cloud_run_job_tracker.tech_spec_id = tech_spec_id

    if code_gen_id:
        cloud_run_job_tracker.code_gen_id = code_gen_id
    return cloud_run_job_tracker


def get_job_phase(job_name) -> CloudRunJobTrackerPhase:
    if "code" in job_name:
        return CloudRunJobTrackerPhase.CODE_GENERATION

    if "document" in job_name:
        return CloudRunJobTrackerPhase.TECHNICAL_SPEC

    return CloudRunJobTrackerPhase.NOT_AVAILABLE
