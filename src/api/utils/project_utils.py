from typing import Any, Dict, Optional

from blitzy_utils.common import generate_core_documentation_path
from blitzy_utils.consts import (DEFAULT_NAME, DOCUMENT_PROMPT_FILE_NAME,
                                 PROMPT_FILE_NAME)
from blitzy_utils.logger import logger
from common_models.db_client import get_db_session
from common_models.models import (Project, ProjectInitialType, Status,
                                  SubscriptionType, UsageType)
from requests import Session

from src.api.models import DocumentPromptInput
from src.api.models import ProjectInitialType as ProjectInitialTypeModel
from src.api.models import UpdateProjectInput
from src.api.utils.gcs_utils import upload_to_gcs
from src.blitzy.utils import publish_notification
from src.consts import (BLOB_NAME, FREE_USER_DOCUMENT_SUBMISSION_LIMIT,
                        GENERATE_DOCUMENT_TOPIC, PROJECT_ID, publisher)
from src.error.errors import FailedToUpload<PERSON>ileError, Status404<PERSON>rror, ResourceNotFound
from src.service.github_branch_pattern_project import \
    get_branch_pattern_by_project_id
from src.service.github_project_repo_service import \
    get_target_github_project_repo_by_id
from src.service.project_service import get_submission_count_last_24h
from src.service.subscription_service import get_user_plan_by_id
from src.service.team_member_service import get_team_member_by_user_id

IN_PROGRESS_JOB_STATUS = [Status.IN_PROGRESS, Status.QUEUED]

PRODUCT_INITIAL_TYPE_MAPPER = {
    ProjectInitialTypeModel.NEW_PRODUCT: ProjectInitialType.NEW_PRODUCT,
    ProjectInitialTypeModel.EXISTING_PRODUCT: ProjectInitialType.EXISTING_PRODUCT
}


def trigger_document_generation(user_id, prompt, repo_url, order, project_id, job_id, tech_spec_id: str):
    """
    Trigger document generation. This function sends notification to the pubsub topic which sends events to initiate
    document generation. This function can be used for software requirements and technical specifications as their
    payloads are simillar.

    Here,
    Order: 0 -> Generate software req document.

    :param user_id: User ID.
    :param prompt: Prompt.
    :param repo_url: Repository URL where document should be uploaded.
    :param order: Order of the document generation.
    :param project_id: Project ID.
    :param job_id:  Job ID.
    :param tech_spec_id: Tech Spec ID.
    """

    github_repo = get_target_github_project_repo_by_id(project_id)
    if not github_repo:
        logger.warning(f"Error fetching github repo for project {project_id}")
        raise ResourceNotFound(f"Error fetching github repo for project {project_id}")

    # Submit project job to the core engine.
    notification_data = {
        "user_id": user_id,
        "repo_name": repo_url,
        "repo_id": github_repo.repo_id if github_repo else None,
        "order": order,
        "project_id": project_id,
        "job_id": job_id,
        "tech_spec_id": tech_spec_id,
        "git_project_repo_id": github_repo.id,
        "propagate": False
    }
    publish_notification(publisher, notification_data, PROJECT_ID, GENERATE_DOCUMENT_TOPIC)
    return notification_data


def validate_document_submission(user_id: str) -> bool:
    user_plan = get_user_plan_by_id(user_id)

    if not user_plan:
        return False

    if user_plan != SubscriptionType.FREE:
        return True

    count = get_submission_count_last_24h(user_id)
    if count >= FREE_USER_DOCUMENT_SUBMISSION_LIMIT:
        return False

    return True


def generate_input_prompt_document_url(repo_url: str):
    """
    Generates input prompt document URL.
    :param repo_url: Repo URL.
    :return: Generated URL.
    """
    file_path = f"{BLOB_NAME}/{repo_url}/blitzy/documentation/{PROMPT_FILE_NAME}.md"
    return file_path


def upload_prd_to_gcs(project: Project, payload: UpdateProjectInput, repo_url: str):
    file_url = generate_input_prompt_document_url(repo_url)
    result = upload_to_gcs(payload.prompt, file_url)
    if not result["success"]:
        raise Exception(f"Failed to upload prompt file for project {project.id} with error {result['message']}")


def upload_document_prompt_to_gcs(project_info: Project, payload: DocumentPromptInput, repo_url: str,
                                  user_info: Dict[str, Any], session: Optional[Session] = None):
    with get_db_session(session) as session:
        branch_pattern = get_branch_pattern_by_project_id(project_info.id, UsageType.SOURCE, session)

        if not branch_pattern:
            raise Status404Error(
                message=f"Branch pattern does not exist for project {project_info.id}"
            )

        user_team = get_team_member_by_user_id(user_info["id"], session)
        company_id = user_info["company_id"] if user_info.get("company_id") else DEFAULT_NAME
        team_id = user_team.team_id if user_team and user_team.team_id else DEFAULT_NAME

        tech_spec_metadata = {
            "company_id": company_id,
            "team_id": team_id,
            "user_id": user_info["id"],
            "repo_name": branch_pattern.pattern.repo_name,
            "repo_id": branch_pattern.pattern.repo_id,
            "branch_id": branch_pattern.pattern.id
        }

        prefix_path = generate_core_documentation_path(tech_spec_metadata)

        file_url = f"{prefix_path}/{DOCUMENT_PROMPT_FILE_NAME}.md"

    result = upload_to_gcs(payload.prompt, file_url)
    if not result["success"]:
        error_message = result.get("message", "Unknown error")
        raise FailedToUploadFileError(
            message=f"Failed to upload prompt file for project {project_info.id}. Error: {error_message}"
        )
