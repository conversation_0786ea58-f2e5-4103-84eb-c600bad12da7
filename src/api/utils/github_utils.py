from blitzy_utils.logger import logger
from blitzy_utils.service_client import ServiceClient

from src.error.errors import PRMergeError, check_service_error


def get_branch_head_commit_from_github_handler(user_id, org_name, repo_id, branch_name):
    request_url = (
        f"/v1/users/{user_id}/organizations/{org_name}/repositories/"
        f"{repo_id}/branch/{branch_name}/head/commit"
    )
    with ServiceClient() as client:
        response = client.get("github", request_url)
        check_service_error(response)
        return response.json()


def manage_pull_request_from_github_handler(
    user_id: str, org_name: str, repo_id: str, pr_number: str, action: str
):
    logger.info(
        f"Managing pull request {pr_number} in {org_name}/{repo_id} from github handler"
    )
    with ServiceClient() as client:
        endpoint = (
            f"/v1/users/{user_id}/organizations/{org_name}/repositories"
            f"/{repo_id}/pr/{pr_number}/action"
        )
        payload = {
            "action": action,
        }
        response = client.post("github", endpoint, json=payload)
        output = response.json()
        if response.status_code != 200:
            logger.warning(
                f"Error managing pull request {pr_number} in {org_name}/{repo_id}: {response.text}"
            )
            raise PRMergeError(output["error"], response.status_code)

        return output


def get_default_branch_from_github_handler(
    user_id: str, owner_name: str, repo_id: str
) -> str:
    logger.info(
        f"Getting default branch for {owner_name}/{repo_id} from github handler"
    )
    with ServiceClient() as client:
        endpoint = (
            f"/v1/users/{user_id}/organizations/{owner_name}/repositories/"
            f"{repo_id}/default/branch"
        )
        response = client.get("github", endpoint)
        check_service_error(response)
        default_branch = response.json().get("branch")
        return default_branch


def get_pull_request_status_from_github_handler(
    user_id: str, org_name: str, repo_id: str, pr_number: str
):
    """
    Get pull request status from GitHub handler.

    :param user_id: User ID
    :param org_name: Organization name
    :param repo_id: Repository ID
    :param pr_number: Pull request number
    :return: Pull request status response from GitHub handler
    """
    logger.info(
        f"Getting pull request {pr_number} status in {org_name}/{repo_id} from github handler"
    )
    with ServiceClient() as client:
        endpoint = (
            f"/v1/users/{user_id}/organizations/{org_name}/repositories/"
            f"{repo_id}/pr/{pr_number}/status"
        )
        response = client.get("github", endpoint)
        check_service_error(response)
        return response.json()
