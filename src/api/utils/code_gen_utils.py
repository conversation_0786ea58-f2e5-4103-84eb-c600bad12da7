import dataclasses
from datetime import datetime
from typing import Any, Dict, Optional

from blitzy_utils.consts import DEFAULT_NAME, GENERATE_REVERSE_FILE_MAP_TOPIC
from blitzy_utils.logger import logger
from common_models.models import (CodeGeneration, Project, ProjectRun,
                                  ProjectRunStage, ProjectRunStatus,
                                  ProjectRunType, TechnicalSpec, UsageType)
from sqlalchemy.orm import Session

from src.blitzy.utils import publish_notification
from src.consts import IN_PROGRESS_STATUS, PROJECT_ID, publisher
from src.error.errors import JobAlreadyRunningError
from src.service.code_gen_service import get_latest_code_gen_by_project_id
from src.service.github_branch_pattern_project import \
    get_branch_pattern_by_project_id
from src.service.github_project_repo_service import (
    get_source_github_project_repo_by_id, get_target_github_project_repo_by_id)
from src.service.project_run_service import save_project_run
from src.service.team_member_service import get_team_member_by_user_id


@dataclasses.dataclass
class CodeGenNotificationPayload:
    code_gen_id: str
    tech_spec_id: str
    repo_name: str
    repo_id: str
    branch_id: str
    branch_name: str
    company_id: str
    user_id: str
    team_id: str
    job_id: str
    propagate: bool
    project_id: str
    head_commit_hash: str
    dest_repo_name: str
    dest_repo_id: str
    dest_branch_id: str
    dest_branch_name: str
    is_new_dest_repo: bool
    change_mode: str
    job_type: str


def create_project_run_for_code_gen(project_id: str, run_type: ProjectRunType, code_gen_id: str,
                                    session: Optional[Session] = None) -> ProjectRun:
    """
    Creates a project run job for a technical specification.
    :param project_id: Project ID.
    :param code_gen_id: Code generation ID.
    :param run_type: Run type.
    :param session: Client session.
    :return: Project run object.
    """
    project_run = ProjectRun(
        status=ProjectRunStatus.QUEUED,
        project_id=project_id,
        stage=ProjectRunStage.CODEGEN,
        run_type=run_type,
        code_gen_id=code_gen_id,
        start_at=datetime.utcnow()
    )

    project_run = save_project_run(project_run, session)
    return project_run


def send_add_feature_generate_reverse_file_map_notification(project_info: Project, job: ProjectRun,
                                                            user_info: Dict[str, Any],
                                                            code_gen: CodeGeneration, tech_spec: TechnicalSpec,
                                                            propagate: bool,
                                                            change_mode: str,
                                                            is_new_dest_repo: bool, session: Session):
    """
    Send a notification for adding feature and generating the reverse file map. This method encapsulates
    the logic for creating the payload, formulating the notification, and publishing it to the appropriate
    topic. Logs the activity upon successfully publishing the notification.

    :param project_info: Information about the current project.
    :param job: Details of the project run.
    :param user_info: Dictionary containing user-related information.
    :param code_gen: Object representing code generation details.
    :param tech_spec: Technical specifications related to the task.
    :param propagate: Boolean indicating whether the changes should propagate.\
    :param change_mode: Specifies the mode of change for the operation.
    :param is_new_dest_repo: Boolean flag representing if the destination repository is new.
    :param session: Active session used during processing.
    :return: None.
    """
    code_gen_payload = generate_code_gen_notification_object(project_info, job, user_info, tech_spec, code_gen,
                                                             propagate, change_mode, is_new_dest_repo, session)
    notification_payload = generate_code_gen_notification_payload(code_gen_payload)

    publish_notification(publisher, notification_payload, PROJECT_ID, GENERATE_REVERSE_FILE_MAP_TOPIC)
    code_gen.job_metadata = notification_payload
    logger.info(f"Published notification code gen add feature notification for project {project_info.id}")


def send_refactor_code_generate_reverse_file_map_notification(project_info: Project, job: ProjectRun,
                                                              user_info: Dict[str, Any],
                                                              code_gen: CodeGeneration, tech_spec: TechnicalSpec,
                                                              propagate: bool,
                                                              change_mode: str,
                                                              is_new_dest_repo: bool, session: Session):
    """
    Sends a notification related to the reverse file map generation during code refactoring. This operation
    encapsulates the process of creating the necessary payload and publishing the notification to the
    relevant messaging topic. Additionally, it logs the event for tracking purposes.

    :param project_info: The project information relating to the specific operation or execution. The
        details encapsulate metadata necessary for processing.
    :param job: Specifies the project run or execution instance that is associated with the
        notification. Contains details about the specific execution context.
    :param user_info: Dictionary containing information about the user initiating or affected
        by the action triggering the notification.
    :param code_gen: Represents the code generation details or configuration settings
        necessary for refactoring the code and mapping files.
    :param tech_spec: The technical specifications or requirements guiding the reverse file map
        generation operation.
    :param propagate: Boolean flag indicating whether the changes should be propagated to other
        components, systems, or connected repositories.
    :param change_mode: String specifying the mode or type of change being conducted (e.g., minor,
        major, or other categorizations) during file mapping.
    :param is_new_dest_repo: Boolean indicator of whether the refactored code requires the creation and
        usage of a new destination repository.
    :param session: The active session object managing the transaction, scope, or persistence layer
        during the notification operation.
    :return: No explicit return value, but side effects include sending a notification message and logging
        the outcome.
    """
    code_gen_payload = generate_code_gen_notification_object(project_info, job, user_info, tech_spec, code_gen,
                                                             propagate, change_mode, is_new_dest_repo, session)
    populate_refactor_code_destination_repo(project_info, code_gen_payload, session)
    notification_payload = generate_code_gen_notification_payload(code_gen_payload)

    publish_notification(publisher, notification_payload, PROJECT_ID, GENERATE_REVERSE_FILE_MAP_TOPIC)
    code_gen.job_metadata = notification_payload
    logger.info(f"Published notification code gen refactor code notification for project {project_info.id}")


def generate_code_gen_notification_object(project_info: Project, job: ProjectRun, user_info: Dict[str, Any],
                                          tech_spec: TechnicalSpec, code_gen: CodeGeneration, propagate: bool,
                                          change_mode: str, is_new_dest_repo: bool = False,
                                          session: Session = None) -> CodeGenNotificationPayload:
    """
    Generates a notification payload object for the code generation process. This
    payload encapsulates necessary metadata related to the project, job, user,
    code generation details, and repository information required for processing
    notifications within the system. It is utilized to forward relevant data
    to downstream processes or services enabling seamless operation and support
    of critical tasks.

    :param project_info: Object containing metadata and identifiers for the project.
    :param job: Representation of the specific job run associated with the project.
    :param user_info: Dictionary storing user-specific information like user ID
        and company associations.
    :param tech_spec: Technical Specification.
    :param code_gen: Object with details about the code generation process.
    :param propagate: Boolean flag indicating if changes should be propagated to
        downstream systems or repositories.
    :param change_mode: Specifies the type or mode of changes applied (e.g., 'edit',
        'overwrite').
    :param is_new_dest_repo: Boolean flag signaling whether the destination repository
        is newly created (defaults to False).
    :param session: Database session object enabling transactional operations
        during the process execution (optional).
    :return: A `CodeGenNotificationPayload` object containing all required
        metadata for generating code generation notifications.
    :rtype: CodeGenNotificationPayload
    """
    source_github_repo_info = get_source_github_project_repo_by_id(project_info.id, session)
    source_branch_pattern = get_branch_pattern_by_project_id(project_info.id, UsageType.SOURCE, session)
    user_team = get_team_member_by_user_id(user_info["id"], session)

    company_id = user_info["company_id"] if user_info.get("company_id") else DEFAULT_NAME
    team_id = user_team.team_id if user_team and user_team.team_id else DEFAULT_NAME

    code_gen_notification_payload = CodeGenNotificationPayload(
        code_gen_id=code_gen.id,
        tech_spec_id=tech_spec.id,
        repo_name=source_branch_pattern.pattern.repo_name,
        repo_id=source_branch_pattern.pattern.repo_id,
        branch_id=source_branch_pattern.pattern.id,
        branch_name=source_branch_pattern.pattern.branch_name,
        company_id=company_id,
        user_id=user_info["id"],
        team_id=team_id,
        job_id=job.id,
        propagate=propagate,
        project_id=project_info.id,
        head_commit_hash=source_github_repo_info.current_commit_hash,
        dest_repo_name=source_branch_pattern.pattern.repo_name,
        dest_repo_id=source_branch_pattern.pattern.repo_id,
        dest_branch_id=source_branch_pattern.pattern.id,
        dest_branch_name=f"blitzy-{code_gen.id}",
        is_new_dest_repo=is_new_dest_repo,
        change_mode=change_mode,
        job_type=tech_spec.job_type.value
    )

    return code_gen_notification_payload


def generate_code_gen_notification_payload(code_gen_notification: CodeGenNotificationPayload) -> Dict[str, Any]:
    """
    Generate a payload dictionary for code generation notification.

    This function takes an instance of 'CodeGenNotificationPayload' and generates
    a dictionary containing all the attributes of the instance. The payload is
    formatted as key-value pairs where the keys correspond to attribute names and
    the values correspond to attribute values. This is typically used for
    serializing or transmitting the data in a consistent structure.

    :param code_gen_notification: Instance of CodeGenNotificationPayload whose
        attributes will be used to generate the payload dictionary
    :return: A dictionary containing all the attributes of the given
        CodeGenNotificationPayload instance as key-value pairs
    """
    payload = {
        "code_gen_id": code_gen_notification.code_gen_id,
        "tech_spec_id": code_gen_notification.tech_spec_id,
        "repo_name": code_gen_notification.repo_name,
        "repo_id": code_gen_notification.repo_id,
        "branch_id": code_gen_notification.branch_id,
        "branch_name": code_gen_notification.branch_name,
        "company_id": code_gen_notification.company_id,
        "user_id": code_gen_notification.user_id,
        "team_id": code_gen_notification.team_id,
        "job_id": code_gen_notification.job_id,
        "propagate": code_gen_notification.propagate,
        "project_id": code_gen_notification.project_id,
        "head_commit_hash": code_gen_notification.head_commit_hash,
        "dest_repo_name": code_gen_notification.dest_repo_name,
        "dest_repo_id": code_gen_notification.dest_repo_id,
        "dest_branch_id": code_gen_notification.dest_branch_id,
        "dest_branch_name": code_gen_notification.dest_branch_name,
        "is_new_dest_repo": code_gen_notification.is_new_dest_repo,
        "change_mode": code_gen_notification.change_mode,
        "job_type": code_gen_notification.job_type
    }
    return payload


def populate_refactor_code_destination_repo(project_info: Project, code_gen_notification: CodeGenNotificationPayload,
                                            session: Session = None):
    """
    Populates the destination repository details for refactored code based on the given project information
    and a specific usage type. Updates the provided CodeGenNotificationPayload with the relevant destination
    repository and branch details, and flags it as a new destination repository.

    :param project_info: An object representing the project containing information such as project ID.
    :param code_gen_notification: Payload object that will be updated with destination repository information.
    :param session: Optional database session object, used to retrieve the branch pattern for the project.
    :return: None
    """
    target_github_repo_info = get_target_github_project_repo_by_id(project_info.id, session)
    target_branch_pattern = get_branch_pattern_by_project_id(project_info.id, UsageType.TARGET, session)

    code_gen_notification.dest_repo_name = target_branch_pattern.pattern.repo_name
    code_gen_notification.dest_repo_id = target_branch_pattern.pattern.repo_id
    code_gen_notification.dest_branch_id = target_branch_pattern.pattern.id
    code_gen_notification.is_new_dest_repo = target_github_repo_info.create_repo


def validate_code_gen_submit(project_info: Project, session: Session = None):
    """
    Validates the ability to submit a code generation task for a project. This function
    ensures that no currently in-progress code generation task exists for the specified
    project. If no prior code generation task is found, it logs the information and exits.
    Otherwise, it checks the status of the latest code generation task. If the status
    indicates that a task is already running, an exception is raised.

    :param project_info: The project data object containing the required project
        information for code generation validation.
    :param session: Optional database session instance to query the latest code
        generation record. Defaults to None.
    :return: None
    """
    code_gen = get_latest_code_gen_by_project_id(project_id=project_info.id, session=session)
    if not code_gen:
        logger.info("No previous code gen found for project.")
        return

    status = code_gen.status

    if status in IN_PROGRESS_STATUS:
        raise JobAlreadyRunningError(f"Code generation for project {project_info.id} is already in progress.")
