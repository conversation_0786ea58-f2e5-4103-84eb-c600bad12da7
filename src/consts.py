import os

from google.cloud import pubsub_v1

publisher = pubsub_v1.PublisherClient()

PROJECT_ID = os.environ["PROJECT_ID"]

# List of available job topics
TRIGGER_TOPICS = [
    "generate-reverse-code",
    "generate-reverse-document",
    "graph-code",
    "generate-document",
    "generate-reverse-file-map",
    "generate-code-spec",
    "generate-repo-structure",
    "generate-code",
    "download-code",
    "upload-code"
]
