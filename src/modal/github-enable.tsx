import {useCallback} from 'react';
import {useNavigate} from 'react-router-dom';
import iconGithub from '../panel/workspace/icons/github.svg';
import {XClose} from '../modal/icons/x-close.tsx';
import timeIcone from '../modal/icons/time-five.svg';
import EclipseIcon from '../panel/workspace/icons/ecllipse.svg';
import logo from '/images/logo-blitzy.svg';
import {useGithubInstallation} from '../panel/workspace/hooks/github-status-hook';

interface Props {
    handleClose: () => void;
}

export function GithubEnablePane({handleClose}: Props) {
    const navigate = useNavigate();
    const {status} = useGithubInstallation();
    const handleCheckStatus = useCallback(() => {
        navigate('/workspace/settings/integrations', {replace: true});
    }, [navigate]);
    const handleContinue = useCallback(() => {
        navigate('/workspace', {replace: true});
    }, [navigate]);

    return (
        <div className="fixed bg-black/40 inset-0  flex flex-col items-center justify-center z-50">
            <div className="bg-gray-50 h-[100dvh] sm:h-auto px-6 pt-16 sm:p-12 pb-9 flex flex-col w-full sm:m-0 sm:w-[500px] rounded-none sm:rounded-3xl relative shadow-[0px_20px_24px_-4px_rgba(16,24,40,0.10),0px_8px_8px_-4px_rgba(16,24,40,0.04)]">
                {/* Close Icon */}
                <button
                    onClick={handleClose}
                    className="focus:outline-none focus-visible:ring-0 focus:ring-0 active:outline-none bg-transparent border-none p-3 rounded-lg absolute right-3 sm:right-3 sm:left-auto top-3 flex justify-center items-center">
                    <XClose />
                </button>

                {/* Content Container */}
                <div className="flex flex-col items-center justify-between flex-grow gap-6 min-h-[calc(100dvh-6rem)] sm:min-h-0">
                    <div className="flex flex-col items-center justify-center flex-grow gap-6 w-full">
                        {/* Icons Container */}
                        <div className="flex items-center gap-4">
                            {/* Logo and Eclipse Container */}
                            <div className="relative w-14 h-14">
                                <img
                                    src={EclipseIcon}
                                    alt="Eclipse"
                                    className="absolute inset-0 w-full h-full"
                                />
                                <img
                                    src={logo}
                                    alt="logo"
                                    className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-8 h-8 z-10"
                                />
                            </div>

                            <div className="w-8 h-8 rounded-full bg-[#5B39F3] flex items-center justify-center">
                                <img
                                    src={timeIcone}
                                    alt="Time"
                                    className="w-6 h-6"
                                />
                            </div>
                            <img
                                src={iconGithub}
                                alt="GitHub"
                                className="w-14 h-14"
                            />
                        </div>

                        {/* Title and Subtitle */}
                        <div className="text-center">
                            <h2 className="text-xl font-semibold leading-7 text-black mb-2">
                                Enable GitHub to continue
                            </h2>
                            <p className="text-sm leading-5 text-gray-600">
                                {status === 'UNINSTALLED'
                                    ? 'GitHub connection unsuccessful. Please try connecting again to generate code.'
                                    : 'A GitHub connection with admin approval is required for code generation. Your connection is pending approval. Refresh status to check for updates.'}
                            </p>
                        </div>
                    </div>

                    <div className="w-full flex justify-center gap-4 mt-auto sm:mt-2">
                        <div
                            role="button"
                            className="secondary-button min-w-max"
                            onClick={handleContinue}>
                            {status === 'UNINSTALLED' ? 'Cancel' : 'Continue'}
                        </div>
                        <button
                            onClick={handleCheckStatus}
                            className="primary-button min-w-max px-5 py-2">
                            {status === 'UNINSTALLED'
                                ? 'Try Again'
                                : 'Check status'}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
}
