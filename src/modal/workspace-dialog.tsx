import {useCallback} from 'react';
import {Dialog, DialogContent} from '@/components/ui/dialog';
import {ProjectTypePane} from './project-type-pane';
import {FeaturePane} from './feature-pane';
import {ProjectNamePane} from './project-name-pane';
import {GithubConnectPane} from './github-connect';
import {GithubApprovalPane} from './github-approval';
import {GithubEnablePane} from './github-enable';

export const WORKSPACE_DIALOG_TYPES = [
    'feature-display',
    'choose-project-type',
    'project-name',
    'github-connect',
    'github-approval',
    'github-enable',
] as const;
export type WorkspaceDialogType = (typeof WORKSPACE_DIALOG_TYPES)[number];

export function WorkspaceDialog({
    type,
    onClose,
}: {
    type: WorkspaceDialogType;
    onClose: () => void;
}) {
    const handleClose = useCallback(() => {
        if (onClose) {
            onClose();
        }
    }, [onClose]);

    const handleOpenChange = useCallback(
        (open: boolean) => {
            if (!open) {
                handleClose();
            }
        },
        [handleClose],
    );

    if (!WORKSPACE_DIALOG_TYPES.includes(type)) return null;

    return (
        <>
            {type === 'feature-display' ? (
                <FeaturePane handleClose={handleClose} />
            ) : type === 'github-connect' ? (
                <GithubConnectPane handleClose={handleClose} />
            ) : type === 'github-approval' ? (
                <GithubApprovalPane handleClose={handleClose} />
            ) : type === 'github-enable' ? (
                <GithubEnablePane handleClose={handleClose} />
            ) : (
                <Dialog open onOpenChange={handleOpenChange}>
                    <DialogContent
                        className={
                            type === 'project-name'
                                ? 'dialog-content-small'
                                : 'dialog-content'
                        }>
                        {type === 'choose-project-type' && <ProjectTypePane />}
                        {type === 'project-name' && (
                            <ProjectNamePane handleClose={handleClose} />
                        )}
                    </DialogContent>
                </Dialog>
            )}
        </>
    );
}
