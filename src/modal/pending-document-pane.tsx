import {TechSpecJobType} from '@/lib/entity-types';
import IndeterminateLinearProgressIndicator from '@/components/custom/indeterminate-linear-progress-indicator';
import {useProjectContext} from '@/context/project-context';
import {useNavigate} from 'react-router-dom';

export function PendingDocumentPane() {
    const {projectState} = useProjectContext();
    const purpose = projectState?.technicalSpec?.jobType;
    const navigate = useNavigate();

    const title = getTitle(purpose);
    const subTitles = getSubTitles(purpose);
    const footerText =
        'Blitzy AI agents are coordinating to ensure high quality';
    return (
        <div className="flex flex-col justify-center items-center gap-[24px] sm:gap-[48px] w-full max-w-[800px] p-0 sm:p-6 h-full min-h-[400px]">
            <div className="text-center">
                <h2 className="mb-[12px] text-[20px] sm:text-[24px] text-black font-semibold">
                    {title}
                </h2>
                {subTitles &&
                    subTitles.map(subTitle => (
                        <p
                            key={subTitle}
                            className="text-[16px] text-black leading-[22px] sm:leading-[24px]">
                            {subTitle}
                        </p>
                    ))}
            </div>
            <div className="max-w-full w-[320px]">
                <IndeterminateLinearProgressIndicator />
            </div>
            <div className="flex flex-col items-center gap-[4px] leading-[22px] sm:leading-[24px]">
                <div className="text-[16px] text-[#999] text-center">
                    {footerText}
                </div>
                {/* show only when email notifications are enabled */}
                {/* Email notifications enabled */}
                <div className="text-[16px] text-[#999]">
                    Email notifications enabled
                </div>
                <div
                    className="text-[16px] text-[#5B39F5] cursor-pointer hover:underline"
                    onClick={() =>
                        navigate('/workspace/settings/notifications', {
                            replace: true,
                        })
                    }>
                    Manage notification preferences
                </div>
            </div>
        </div>
    );
}

function getTitle(purpose?: TechSpecJobType): string {
    switch (purpose) {
        case 'NEW_PRODUCT':
            return 'Building enterprise-grade tech spec';
        case 'EXISTING_PRODUCT':
            return 'Building enterprise-grade tech spec from your codebase';
        case 'ADD_FEATURE':
            return 'Updating the enterprise-grade tech spec to include your feature';
        case 'REFACTOR_CODE':
            return 'Updating the enterprise-grade tech spec to include your refactor';
        case 'SYNC_TECH_SPEC':
            return 'Syncing tech spec after analyzing code changes';
        case 'FIX_BUGS':
            return 'Updating the enterprise-grade tech spec to include your bug fix';
        case 'FIX_CVES':
            return 'Updating the enterprise-grade tech spec to include your security fix';
        case 'ADD_TESTING':
            return 'Updating the enterprise-grade tech spec to include your tests';
        case 'DOCUMENT_CODE':
            return 'Updating the enterprise-grade tech spec to include code documentation';
        case 'CUSTOM':
            return 'Updating the enterprise-grade tech spec to include your requirement';
        default:
            return 'Building enterprise-grade tech spec';
    }
}

function getSubTitles(purpose?: TechSpecJobType): string[] {
    switch (purpose) {
        case 'NEW_PRODUCT':
        case 'ADD_FEATURE':
        case 'REFACTOR_CODE':
            return ['Check back in about 1 hour...'];
        case 'EXISTING_PRODUCT':
            return [
                'This can take 1-3 days depending on codebase size',
                'Code is typically processed at ~5 minutes per file',
            ];
        case 'SYNC_TECH_SPEC':
            return [
                'Check back in about 1 hour — actual time may vary depending on the size of the code changes.',
            ];
        default:
            return ['This takes a while...'];
    }
}
