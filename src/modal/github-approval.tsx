import {useCallback, useEffect, useState} from 'react';
import {useNavigate, useSearchParams} from 'react-router-dom';
import {useToast} from '@/hooks/use-toast.ts';
import iconGithub from '../panel/workspace/icons/github.svg';
// import {XClose} from '../modal/icons/x-close.tsx';
import timeIcone from '../modal/icons/time-five.svg';
import EclipseIcon from '../panel/workspace/icons/ecllipse.svg';
import {saveGithubInstallationInfo} from '@/lib/backend.ts';
import {useGithubInstallation} from '../panel/workspace/hooks/github-status-hook';
import logo from '/images/logo-blitzy.svg';
import {logGAEvent} from '@/lib/utils';

interface Props {
    handleClose: () => void;
}

export function GithubApprovalPane({handleClose}: Props) {
    const navigate = useNavigate();
    const {toast} = useToast();
    const [searchParams] = useSearchParams();
    const {checkGithubInstallationStatus} = useGithubInstallation();
    // const [isInstalling, setIsInstalling] = useState(false);
    // const [isChecking, setIsChecking] = useState(true);
    const [shouldInit, setShouldInit] = useState(false);

    // const handleCheckStatus = useCallback(() => {
    //     navigate('/workspace/settings/integrations', {replace: true});
    // }, [navigate]);

    const handleContinue = useCallback(() => {
        handleClose();
        navigate('/workspace/projects', {replace: true});
    }, [navigate, handleClose]);

    const installationID = searchParams.get('installation_id') || '';
    const setupAction = searchParams.get('setup_action');
    const code = searchParams.get('code');
    // let title: string | undefined;
    // let subTitle: string | undefined;

    // if (isChecking) {
    //     title = 'Checking GitHub Status...';
    //     subTitle = 'Please wait while we verify your connection';
    // } else if (isInstalling) {
    //     title = 'Installing GitHub...';
    //     subTitle = 'Please wait while we complete the installation';
    // } else if (setupAction === 'install') {
    //     title = 'You have successfully connected GitHub to Blitzy';
    //     subTitle = 'Get ready to accelerate your development with Blitzy OS';
    // } else if (setupAction === 'request') {
    //     title = 'Waiting for organization approval ';
    //     subTitle =
    //         'Your request to connect to the GitHub organization has been sent to the owner for review.';
    // }

    const init = useCallback(async () => {
        if (
            code &&
            setupAction &&
            ((setupAction === 'install' && installationID) ||
                setupAction === 'request')
        ) {
            try {
                // setIsInstalling(true);
                await saveGithubInstallationInfo(
                    code,
                    installationID,
                    setupAction,
                );

                if (setupAction === 'install') {
                    await new Promise(resolve => setTimeout(resolve, 1000));
                    const status = await checkGithubInstallationStatus();

                    if (status?.status === 'ACTIVE') {
                        handleClose();
                        const projectID = localStorage.getItem(
                            'github-connect-redirect-to-project-id',
                        );
                        const redirectToIntegrationTab = localStorage.getItem(
                            'github-connect-redirect-to-integration-tab',
                        );
                        if (projectID) {
                            localStorage.removeItem(
                                'github-connect-redirect-to-project-id',
                            );
                            navigate(`/workspace/project/${projectID}/status`, {
                                replace: true,
                            });
                        } else if (redirectToIntegrationTab === 'true') {
                            localStorage.removeItem(
                                'github-connect-redirect-to-integration-tab',
                            );
                            navigate('/workspace/settings/integrations', {
                                replace: true,
                            });
                        }
                    }
                }
                // setIsInstalling(false);

                logGAEvent('github_installation_success', {
                    installation_id: installationID,
                    setup_action: setupAction,
                });
            } catch (error) {
                // setIsInstalling(false);
                console.error(error);
                toast({
                    variant: 'destructive',
                    duration: 10000,
                    title: 'There was a problem saving Github installation information.',
                    // description: `Error saving Github installation information: ${(error as Error).message}`,
                });
                handleContinue();
            }
        } else {
            toast({
                variant: 'destructive',
                duration: 10000,
                description: `Invalid search parameters: ${searchParams.toString()}`,
            });
            handleContinue();
        }
    }, [
        code,
        installationID,
        setupAction,
        toast,
        searchParams,
        checkGithubInstallationStatus,
        handleClose,
        navigate,
        handleContinue,
    ]);

    useEffect(() => {
        const checkInitialStatus = async () => {
            try {
                const currentStatus = await checkGithubInstallationStatus();
                // console.log('currentStatus', currentStatus);
                if (currentStatus?.status === 'ACTIVE') {
                    const newUrl = window.location.pathname;
                    window.history.replaceState({}, '', newUrl);
                    handleClose();
                } else if (currentStatus?.status !== 'ACTIVE') {
                    setShouldInit(true);
                }
            } catch (error) {
                console.error('Error checking initial GitHub status:', error);
                setShouldInit(true);
            } finally {
                // setIsChecking(false);
            }
        };

        checkInitialStatus();
    }, [checkGithubInstallationStatus, handleClose]);

    useEffect(() => {
        if (shouldInit) {
            init();
        }
    }, [init, shouldInit]);

    return (
        <div className="bg-black/40 fixed inset-0  flex flex-col items-center justify-center z-50">
            <div className="bg-white h-[100dvh] sm:h-auto px-6 pt-16 sm:p-12 pb-9 flex flex-col w-full sm:m-0 sm:w-[500px] rounded-none sm:rounded-3xl relative shadow-[0px_20px_24px_-4px_rgba(16,24,40,0.10),0px_8px_8px_-4px_rgba(16,24,40,0.04)]">
                {/* Close Icon */}
                {/* <button
                    onClick={handleClose}
                    className="focus:outline-none focus-visible:ring-0 focus:ring-0 active:outline-none bg-transparent border-none p-3 rounded-lg absolute right-3 sm:right-3 sm:left-auto top-3 flex justify-center items-center">
                    <XClose />
                </button> */}

                {/* Content Container */}
                <div className="flex flex-col items-center justify-between flex-grow gap-6 min-h-[calc(100dvh-6rem)] sm:min-h-0">
                    <div className="flex flex-col items-center justify-center flex-grow gap-6 w-full">
                        {/* Icons Container */}
                        <div className="flex items-center gap-4">
                            {/* Logo and Eclipse Container */}
                            <div className="relative w-14 h-14">
                                <img
                                    src={EclipseIcon}
                                    alt="Eclipse"
                                    className="absolute inset-0 w-full h-full"
                                />
                                <img
                                    src={logo}
                                    alt="logo"
                                    className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-8 h-8 z-10"
                                />
                            </div>

                            <div className="w-8 h-8 rounded-full bg-[#5B39F3] flex items-center justify-center">
                                <img
                                    src={timeIcone}
                                    alt="Time"
                                    className="w-6 h-6"
                                />
                            </div>
                            <img
                                src={iconGithub}
                                alt="GitHub"
                                className="w-14 h-14"
                            />
                        </div>

                        {/* Title and Subtitle */}
                        <div className="text-center">
                            <h2 className="text-xl font-semibold leading-7 text-black mb-2">
                                Installing GitHub...
                            </h2>
                            <p className="text-sm leading-5 text-gray-600">
                                This will only take a few seconds.
                            </p>
                            <p className="text-sm leading-5 text-gray-600">
                                You will be redirected automatically once it's
                                complete.
                            </p>
                        </div>
                    </div>

                    {/* <div className="w-full flex justify-center gap-4 mt-auto sm:mt-2">
                        <div
                            role="button"
                            className="secondary-button min-w-max"
                            onClick={handleContinue}>
                            Continue
                        </div>
                        {setupAction != 'install' && (
                            <button
                                onClick={handleCheckStatus}
                                className="primary-button min-w-max px-5 py-2">
                                Check status
                            </button>
                        )}
                    </div> */}
                </div>
            </div>
        </div>
    );
}
