@import url('https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
    font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
    line-height: 1.5;
    font-weight: 400;

    color: rgba(255, 255, 255, 0.87);
    background-color: #242424;

    font-synthesis: none;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

a {
    color: #5b39f3 !important;
}
a:hover {
    text-decoration: underline;
    text-decoration-style: dotted;
}

body {
    margin: 0;
    display: flex;
    min-width: 320px;
    min-height: 100dvh;
    max-height: 100dvh;
    font-family: 'Inter', system-ui, Avenir, Helvetica, Arial, sans-serif;
    font-weight: 300;
    @media screen(sm) {
        width: 100%;
        /* min-width: 1200px; */
        min-height: 100vh;
        max-height: none;
        place-items: center;
    }
}

h1 {
    font-size: 3.2em;
    line-height: 1.1;
}

button {
    border-radius: 8px;
    border: 1px solid transparent;
    padding: 0.6em 1.2em;
    font-size: 1em;
    font-weight: 500;
    font-family: inherit;
    background-color: #1a1a1a;
    cursor: pointer;
    transition: border-color 0.25s;
}
button:hover {
    border-color: #646cff;
}
button:focus,
button:focus-visible {
    outline: 4px auto -webkit-focus-ring-color;
}

@media (prefers-color-scheme: light) {
    :root {
        color: #213547;
        background-color: #ffffff;
    }
    /* a:hover {
        color: #747bff;
    } */
    button {
        background-color: #f9f9f9;
    }
}

@layer base {
    :root {
        --background: 0 0% 100%;
        --foreground: 240 10% 3.9%;
        --card: 0 0% 100%;
        --card-foreground: 240 10% 3.9%;
        --popover: 0 0% 100%;
        --popover-foreground: 240 10% 3.9%;
        --primary: 240 5.9% 10%;
        --primary-foreground: 0 0% 98%;
        --secondary: 240 4.8% 95.9%;
        --secondary-foreground: 240 5.9% 10%;
        --muted: 240 4.8% 95.9%;
        --muted-foreground: 240 3.8% 46.1%;
        --accent: 240 4.8% 95.9%;
        --accent-foreground: 240 5.9% 10%;
        --destructive: 0 84.2% 60.2%;
        --destructive-foreground: 0 0% 98%;
        --border: 240 5.9% 90%;
        --input: 240 5.9% 90%;
        --ring: 240 10% 3.9%;
        --chart-1: 12 76% 61%;
        --chart-2: 173 58% 39%;
        --chart-3: 197 37% 24%;
        --chart-4: 43 74% 66%;
        --chart-5: 27 87% 67%;
        --radius: 0.5rem;
        --brand-purple: #5b39f3;
        --form-error-red: #ec3636;

        --bg-primary: #ffffff;
        --bg-secondary: #f5f5f5;
        --bg-tertiary: #f5f5f5;
        --bg-brand-purple: #5b39f3;
        --bg-success: #c9fcea;
        --bg-success-light: #eafef7;
        --bg-error: #ffdfdf;
        --bg-primary-invert: #000000;
        --bg-secondary-invert: #666666;
        --bg-tertiary-invert: #333333;
        --bg-brand-50: #f2f0fe;
        --bg-brand-100: #d4cbfc;
        --bg-purple: #b23af2;
        --bg-tertiary-hover: #d9d9d9;
        --bg-tertiary-hover-invert: #666666;
        --bg-brand-hover: #2d1c77;
        --bg-brand-hover-invert: #d4cbfc;

        --text-primary: #000000;
        --text-secondary: #333333;
        --text-tertiary: #999999;
        --text-brand-purple: #5b39f3;
        --text-success: #005335;
        --text-error-100: #ec3636;
        --text-error-200: #991010;
        --text-primary-invert: #ffffff;
        --text-brand-hover: #5b39f3;

        --border-primary: #999999;
        --border-secondary: #f5f5f5;
        --border-brand-purple: #5b39f3;
        --border-primary-invert: #f5f5f5;
        --border-brand-invert: #ffffff;
        --border-brand-hover: #2d1c77;
        --border-error: #ec3636;
        --border-tertiary-hover-invert: #d4cbfc;
    }
}

@layer base {
    * {
        @apply border-border;
    }
    body {
        @apply bg-background text-foreground;
    }
}

@layer components {
    .workspace-nav {
        box-shadow:
            0px -9px 50px 0px rgba(0, 0, 0, 0.06),
            0px 374px 105px 0px rgba(130, 130, 130, 0),
            0px 239px 96px 0px rgba(130, 130, 130, 0.01),
            0px 135px 81px 0px rgba(130, 130, 130, 0.05),
            0px 60px 60px 0px rgba(130, 130, 130, 0.09),
            0px 15px 33px 0px rgba(130, 130, 130, 0.1);
    }

    .project-tile-image {
        background: lightgray 50%;
    }

    .avatar {
        border-radius: 100px;
        border: 0.5px solid #fff;
        background: lightgray 0px 0px / 100% 100% no-repeat;
    }

    .help-support-link,
    .help-support-link:visited,
    .help-support-link:hover,
    .help-support-link:active,
    .help-support-link:focus {
        color: #333 !important;
        font-weight: inherit;
        text-decoration: none;
        background: none;
        font-size: inherit;
    }

    .gradient-name {
        background-image: radial-gradient(
            at left top,
            #4101db 30%,
            #5b39f3 40%,
            #77c8ab 90%
        );
        /* background-image: linear-gradient(#5b39f3, #94fad5); */
        color: transparent;
        background-clip: text;
    }

    .input-field,
    .input-field-error {
        height: 52px;
        padding: 8px 16px;
        border-radius: 24px;
        font-size: 16px;
    }

    .input-field {
        border: 1px solid #999;
    }

    .input-field-error {
        border: 1px solid var(--form-error-red);
    }

    .input-field:focus {
        border: 1px solid #d4cbfc;
        outline: none;
    }

    .home-panel {
        width: 100%;
        display: flex;
        flex-direction: column;
        gap: 32px;
        padding: 0 24px;
    }

    .outlet-wrapper {
        width: 100%;
        @media screen(sm) {
            width: auto;
        }
    }

    .home-panel .form-label {
        align-self: self-start;
        color: #000;
        font-weight: 600;
        font-size: 16px;
        letter-spacing: -0.04px;
    }

    .home-panel .form-note,
    .home-panel .foot-note,
    .home-panel .form-error-note {
        font-size: 14px;
        line-height: 1.4em;
        letter-spacing: -0.3px;
    }

    .home-panel .form-note {
        color: #999;
    }

    .home-panel .form-error-note {
        color: var(--form-error-red);
    }

    .home-panel .foot-note {
        color: #33333380;
    }

    .primary-button {
        width: 100%;
        text-align: center;
        padding: 8px 20px;
        border-radius: 32px;
        color: #ffffff;
        font-size: 16px;
        font-weight: 600;
        border: 1px solid #5b39f3;
        @apply bg-brand-purple;
        line-height: 22px;
        @media screen(sm) {
            max-width: 520px;
            min-width: max-content;
            width: auto;
        }
    }

    .primary-button:hover {
        background-color: #2d1c77;
        border-color: #2d1c77;
    }

    .secondary-button {
        width: 100%;
        text-align: center;
        padding: 8px 20px;
        border-radius: 32px;
        background-color: white;
        color: #5b39f3;
        font-size: 16px;
        font-weight: 600;
        border: 1px solid #5b39f3;
        line-height: 22px;
        @media screen(sm) {
            max-width: 520px;
            min-width: max-content;
            width: auto;
        }
    }

    .tertiary-button {
        width: 100%;
        text-align: center;
        padding: 8px 20px;
        border-radius: 32px;
        background-color: transparent;
    }

    .dialog-content,
    .dialog-content-small {
        width: 100vw;
        height: 100dvh;
        /* margin-top: 25px !important; for mobile */
        /* padding-top: 60px !important; for mobile */
        /* padding-bottom: 75px !important; for mobile */
        @media screen(sm) {
            margin-top: 0px !important;
            padding-top: 1.5em !important;
            padding-bottom: 1.5em !important;
            height: auto;
            max-width: 1024px;
        }
    }

    .dialog-content {
        @media screen(sm) {
            min-width: 768px;
        }
    }

    .dialog-content-small {
        @media screen(sm) {
            min-width: 500px;
        }
    }

    /****  Tiptap Editor Overrides ****/

    /* Ensure proper height inheritance */
    .tiptap-editor-content .ProseMirror {
        min-height: 400px;
    }

    /* tool bar */
    .md-editor .js-sticky {
        /* border: 1px solid #e5e5e5 !important;
        border-radius: 24px !important;
        margin: 12px 12px 12px 12px !important;
        box-shadow:
            1px 3px 12px 3px rgba(0, 0, 0, 0.1),
            0px 2px 4px -2px rgba(0, 0, 0, 0.06); */
        width: 100% !important;
        /* max-width: 420px !important; */
        border-bottom: 0.5px solid #d9d9d9;
    }

    /* remove help/question mark button */
    .md-editor .js-block-other {
        display: none !important;
    }

    /* remove md/rich view switch */
    .md-editor .myn2 {
        display: none !important;
    }

    /* remove insert-image button */
    .md-editor .js-insert-image-btn {
        display: none !important;
    }

    .md-editor .s-popover.s-popover__tooltip {
        border: none !important;
        background-color: #000 !important;
        box-shadow: none !important;
        border-radius: 12px !important;
        color: white !important;
    }

    .md-editor .s-popover.s-popover.s-popover__tooltip .s-popover--arrow {
        color: #000 !important;
    }

    .md-editor .s-btn.s-editor-btn.is-selected,
    .md-editor .s-btn.s-editor-btn:hover,
    .md-editor .s-btn.s-editor-btn:active {
        background-color: #f2f0fe !important;
    }

    .md-editor .js-editor ul,
    .md-editor .js-editor ol {
        list-style: initial;
        margin-block-start: 1em;
        margin-block-end: 1em;
        padding-inline-start: 40px;
    }

    .md-editor .js-editor ul {
        list-style: initial;
    }

    .md-editor .js-editor ol {
        list-style: decimal;
    }

    .md-editor .js-editor li ul {
        list-style: circle;
    }

    .markdown-body ol {
        list-style: decimal !important;
    }

    .markdown-body ul {
        list-style: initial !important;
    }

    .markdown-body li ul {
        list-style: circle !important;
    }
}

.feature-pane-free-background {
    background:
        radial-gradient(
            360% 220% at 0px 0px,
            #1a105f 11.53%,
            #372aba 32.55%,
            rgba(148, 250, 213, 0.8) 56.3%
        ),
        #fff;
}

.feature-pane-pro-background {
    background-image:
        url('/images/pro-modal-shape-1.png'),
        url('/images/pro-modal-shape-2.png');
    background-size: cover, cover;
    background-position:
        left,
        bottom right;
    background-repeat: no-repeat, no-repeat;
}

.feature-pane-enterprise-background {
    background-image:
        url('/images/enterprise-modal-shape-1.png'),
        url('/images/enterprise-modal-shape-2.png'),
        url('/images/enterprise-modal-cubes.png');
    background-size: cover, cover, contain;
    background-position:
        bottom left,
        top right,
        top right;
    background-repeat: no-repeat, no-repeat, no-repeat;
    @media screen(sm) {
        background-size: cover, cover, auto;
    }
}

@keyframes spin-reverse {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(-360deg);
    }
}

.animate-spin-reverse {
    animation: spin-reverse 1s linear infinite;
}

.hide-scrollbar {
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* Internet Explorer 10+ */
}
.hide-scrollbar::-webkit-scrollbar {
    display: none; /* Safari and Chrome */
}
