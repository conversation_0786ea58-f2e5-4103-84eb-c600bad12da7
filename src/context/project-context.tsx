import {
    createContext,
    useCallback,
    useContext,
    useEffect,
    useState,
} from 'react';
import {
    Outlet,
    useLoaderData,
    useParams,
    useSearchParams,
} from 'react-router-dom';
import dayjs from 'dayjs';
import {
    CodeGenStatus,
    Project,
    ProjectDetail,
    ProjectDocumentType,
    ProjectState,
    PromptType,
    TechSpecStatus,
} from '@/lib/entity-types';
import {
    approveProjectDocument,
    fetchProject,
    updateProjectDocument,
    updateProjectPrompt,
    downloadProjectDocument,
    downloadTechSpecPDF,
    fetchProjectGitRepos,
    getCodeGenCommitStatus,
    updateProjectDocumentPrompt,
    fetchTechSpecStatus,
    fetchTechSpecRunStatus,
    // fetchCodeGenStatus,
    fetchTechSpecCodeGenStatus,
    // fetchTechSpecSyncStatus,
} from '@/lib/backend';
import {useAlertContext} from '@/context/alert-context';
import {PlatformError} from '@/lib/platform-error';
import {useToast} from '@/hooks/use-toast';
import {isLegacyProject} from '@/lib/utils';
import {fetchTechSpecSyncStatus} from '@/lib/backend';
import {DocumentSubmissionProvider} from '@/context/document-submission-context';

interface ProjectContextType {
    project?: ProjectDetail;
    projectState?: ProjectState;
    isFetchingProject?: boolean;
    isFetchingProjectState?: boolean;
    isPromptSubmitting?: boolean;
    setIsPromptSubmitting?: (value: boolean) => void;
    isTechSpecSubmitting?: boolean;
    setTechSpecSubmitting?: (value: boolean) => void;
    refreshProject?: () => Promise<ProjectDetail | undefined>;
    refreshProjectState?: (options?: {
        project?: ProjectDetail;
        skipTechSpecSyncCheck?: boolean;
    }) => void;
    retrieveDocument?: (
        docType: ProjectDocumentType,
    ) => Promise<string | undefined>;
    updatePrompt?: (
        doc: string,
        type: PromptType,
        isDraft: boolean,
    ) => Promise<Project | undefined>;
    // finalizePrompt?: () => Promise<Project | undefined>;
    approveDocument?: (docType: ProjectDocumentType) => void;
    updateDocument?: (doc: string, docType: ProjectDocumentType) => void;
    getPDFDownloadStatus?: (projectId: string) => Promise<
        | {
              status: string;
              downloadUrl?: string;
          }
        | undefined
    >;
    pdfStatus?: string;
    setPDFStatus?: (status: string) => void;
    promptType?: PromptType;
    fetchIsTechSpecInSync?: () => Promise<boolean | undefined>;
}

const ProjectContext = createContext<ProjectContextType>({});

export const useProjectContext = (): ProjectContextType => {
    return useContext(ProjectContext);
};

export const ProjectProvider = () => {
    const initProject = useLoaderData() as ProjectDetail;
    const [project, setProject] = useState<ProjectDetail>(initProject);
    // projectJobStatus is a very simplistic data structure that capatures only the job status. It can be independently refreshed
    // to get the latest from server
    const [projectState, setProjectState] = useState<ProjectState>();
    const [isTechSpecSubmitting, setTechSpecSubmitting] = useState(false);
    const [isPromptSubmitting, setIsPromptSubmitting] =
        useState<boolean>(false);
    const [isFetchingProject, setIsFetchingProject] = useState(true);
    const [isFetchingProjectState, setIsFetchingProjectState] = useState(true);

    const [searchParams] = useSearchParams();
    const promptType = searchParams.get('action') as PromptType;

    const {projectId} = useParams();
    const {showAlert} = useAlertContext();
    const {toast} = useToast();
    const [pdfStatus, setPDFStatus] = useState<string>('');

    // refresh project AND project GIT repos
    const refreshProject = useCallback(async (): Promise<
        ProjectDetail | undefined
    > => {
        if (!projectId) return;
        try {
            setIsFetchingProject(true);
            const {project} = await fetchProject(projectId);
            if (project) {
                const {source, sink} = await fetchProjectGitRepos(projectId);
                project.gitSource = source;
                project.gitSink = sink;

                setProject(project);
                return project;
                // console.log(project);
            } else {
                showAlert?.({
                    type: 'HttpError',
                    message: 'Project not found',
                });
            }
        } catch (error) {
            console.error(error);
            if (
                error instanceof PlatformError &&
                error.customData?.error_code === 'PROJECT_NOT_FOUND'
            ) {
                window.location.href =
                    (error.customData?.redirect_to as string) ||
                    '/workspace/projects';
                return undefined;
            } else if (error instanceof Error) {
                showAlert?.({
                    type: 'HttpError',
                    message: `Failed to retrieve project: ${error.message}`,
                });
            }
        } finally {
            setIsFetchingProject(false);
        }
    }, [showAlert, projectId]);

    const fetchIsTechSpecInSync = useCallback(async () => {
        if (project?.id) {
            const {isTechSpecInSync} = await fetchTechSpecSyncStatus(
                project.id,
            );
            setProjectState(prevState => ({
                ...prevState,
                isTechSpecInSync: isTechSpecInSync,
            }));
            return isTechSpecInSync;
        }
    }, [project?.id]);

    // const initProjectState = useCallback(
    //     (project: ProjectDetail) => {
    //         setProjectState({
    //             status: project.status,
    //             technicalSpec: project.tech,
    //             codeGeneration: codeGenStatus,
    //             isTechSpecInSync:
    //                 projectState?.isTechSpecInSync || true,
    //         });
    //     }, []);

    const refreshProjectState = useCallback(
        async (options?: {
            project?: ProjectDetail;
            skipTechSpecSyncCheck?: boolean;
        }) => {
            const RETRIES = 3; // ARUI-1138
            if (projectId) {
                for (let retry = 0; retry < RETRIES; retry++) {
                    try {
                        setIsFetchingProjectState(true);
                        let project = options?.project;
                        if (!project) {
                            const {project: fetchedProject} =
                                await fetchProject(projectId);
                            project = fetchedProject;
                        }

                        const {
                            status,
                            technicalSpecAvailable,
                            codeGenerationAvailable,
                        } = project ?? {};

                        let techSpecStatus: TechSpecStatus | undefined;
                        if (technicalSpecAvailable) {
                            // For NEW_PRODUCT and REFACTOR_CODE, skip GitHub status check
                            // For other job types, check GitHub status

                            techSpecStatus =
                                await fetchTechSpecStatus(projectId);
                            if (techSpecStatus) {
                                const techSpecRunStatus =
                                    await fetchTechSpecRunStatus(projectId);
                                if (techSpecRunStatus) {
                                    techSpecStatus.status =
                                        techSpecRunStatus.status;
                                    techSpecStatus.jobMetadata =
                                        techSpecRunStatus.jobMetadata;
                                }
                            }
                        }

                        let isTechSpecInSync: boolean | undefined = undefined;
                        if (
                            technicalSpecAvailable &&
                            !options?.skipTechSpecSyncCheck
                        ) {
                            isTechSpecInSync = await fetchIsTechSpecInSync();
                        }

                        // let isTechSpecInSync = true;
                        // if (!project?.isDisabled && techSpecStatus?.id !== undefined) {
                        //     const {isTechSpecInSync: isSync} =
                        //         await fetchTechSpecSyncStatus(projectId);
                        //     isTechSpecInSync = isSync;
                        // }

                        let codeGenStatus: CodeGenStatus | undefined;
                        // if (codeGenerationAvailable) {
                        if (codeGenerationAvailable && techSpecStatus?.id) {
                            // codeGenStatus = await fetchCodeGenStatus(projectId);
                            codeGenStatus = await fetchTechSpecCodeGenStatus(
                                projectId,
                                techSpecStatus.id,
                            );
                            if (
                                codeGenStatus?.status === 'DONE' &&
                                codeGenStatus?.id
                            ) {
                                try {
                                    const response =
                                        await getCodeGenCommitStatus(
                                            projectId,
                                            codeGenStatus.id,
                                        );

                                    if (response) {
                                        codeGenStatus.commitStatus =
                                            response.status;
                                    }
                                } catch (error) {
                                    console.error(error);
                                }
                            }
                        }

                        setProjectState({
                            status,
                            technicalSpec: techSpecStatus || undefined, // turn null to undefined
                            codeGeneration: codeGenStatus || undefined, // turn null to undefined

                            isTechSpecInSync:
                                isTechSpecInSync ??
                                projectState?.isTechSpecInSync ??
                                true,
                        });
                        break; // exit loop on success
                    } catch (error) {
                        console.error(error);
                        if (retry === RETRIES - 1) {
                            // ARUI-1138: suppress rasing the error to user's attention until the last retry
                            if (error instanceof Error) {
                                showAlert?.({
                                    type: 'HttpError',
                                    message: `Failed to retrieve project state ${error.message}`,
                                });
                            }
                        }
                    } finally {
                        setIsFetchingProjectState(false);
                    }
                }
            }
        },
        [
            projectId,
            showAlert,
            fetchIsTechSpecInSync,
            projectState?.isTechSpecInSync,
        ],
    );

    const getPDFDownloadStatus = useCallback(
        async (projectId: string) => {
            if (projectId) {
                try {
                    const downloadResult = await downloadTechSpecPDF(projectId);
                    setPDFStatus(downloadResult.status);
                    return downloadResult;
                } catch (error) {
                    console.error(error);
                    if (error instanceof Error) {
                        showAlert?.({
                            type: 'HttpError',
                            message: `Failed to download PDF: ${error.message}`,
                        });
                    }
                }
            }
        },
        [showAlert, setPDFStatus],
    );

    const retrieveDocument = useCallback(
        async (docType: ProjectDocumentType): Promise<string | undefined> => {
            if (project) {
                try {
                    const doc = await downloadProjectDocument(project, docType);
                    return doc;
                } catch (error) {
                    if (!isLegacyProject(project)) {
                        console.error(error);
                        if (error instanceof Error) {
                            showAlert?.({
                                type: 'HttpError',
                                message: `Failed to retrieve document: ${error.message}`,
                            });
                        }
                    }
                }
            }
        },
        [project, showAlert],
    );

    const approveDocument = useCallback(
        async (docType: ProjectDocumentType) => {
            try {
                if (project?.id) {
                    let newJobStatus: ProjectState | undefined;
                    setTechSpecSubmitting(true);
                    await approveProjectDocument(project?.id, docType);
                    await refreshProjectState();
                    // newJobStatus = {
                    //     ...projectJobStatus,
                    //     technicalSpec: {status: 'SUBMITTED'},
                    //     codeGeneration: {status: 'TODO'},
                    // };

                    if (newJobStatus) setProjectState(newJobStatus);
                }
            } catch (error) {
                console.error(error);
                if (
                    error instanceof PlatformError &&
                    (error as PlatformError).code === 'HttpStatus429'
                ) {
                    toast({
                        variant: 'destructive',
                        duration: 10000,
                        description: (error as PlatformError).message,
                    });
                } else if (error instanceof Error) {
                    showAlert?.({
                        type: 'HttpError',
                        message: error.message,
                    });
                }
                throw error;
            } finally {
                setTechSpecSubmitting(false);
            }
        },
        [project?.id, refreshProjectState, showAlert, toast],
    );

    const updatePrompt = useCallback(
        async (
            doc: string,
            type: PromptType,
            isDraft: boolean,
        ): Promise<Project | undefined> => {
            try {
                if (project?.id) {
                    if (type === 'ONBOARD_CODE') {
                        await updateProjectDocumentPrompt(project, doc);
                        await updateProjectPrompt(
                            project,
                            '',
                            'EXISTING_PRODUCT',
                            isDraft,
                        );
                    } else {
                        await updateProjectPrompt(project, doc, type, isDraft);
                    }
                    const now = dayjs();
                    const updatedProject = {
                        ...project,
                        prompt: doc,
                        promptUpdatedAt: now.unix(),
                        updatedAt: now.unix(),
                        promptStatus: isDraft ? 'DRAFT' : 'SUBMITTED',
                    } as Project;

                    // console.log(updatedProject);
                    setProject(updatedProject);
                    if (!isDraft) {
                        await refreshProjectState();
                        // setProjectJobStatus({
                        //     ...projectJobStatus,
                        //     technicalSpec: {status: 'TODO'},
                        // });
                    }
                    return updatedProject;
                }
            } catch (error) {
                console.error(error);
                if (error instanceof Error) {
                    toast({
                        variant: 'default',
                        duration: 10000,
                        description: error.message,
                    });
                }
            }
        },
        [project, refreshProjectState, toast],
    );

    const updateDocument = useCallback(
        async (doc: string, docType: ProjectDocumentType) => {
            try {
                if (project?.id && projectState?.technicalSpec?.id) {
                    await updateProjectDocument(
                        project.id,
                        projectState.technicalSpec.id,
                        doc,
                        project.type,
                        docType,
                    );
                }
            } catch (error) {
                console.error(error);
                if (error instanceof Error) {
                    showAlert?.({
                        type: 'HttpError',
                        message: error.message,
                    });
                }
            }
        },
        [
            project?.id,
            projectState?.technicalSpec?.id,
            project?.type,
            showAlert,
        ],
    );

    useEffect(() => {
        (async function () {
            const project = await refreshProject();
            if (project) {
                refreshProjectState({project});
            }
        })();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [projectId]);

    return (
        <ProjectContext.Provider
            value={{
                project,
                isFetchingProject,
                isFetchingProjectState,
                projectState,
                refreshProject,
                refreshProjectState,
                isPromptSubmitting,
                setIsPromptSubmitting,
                isTechSpecSubmitting,
                setTechSpecSubmitting,
                promptType,
                updatePrompt,
                retrieveDocument,
                approveDocument,
                updateDocument,
                getPDFDownloadStatus,
                pdfStatus,
                setPDFStatus,
                fetchIsTechSpecInSync,
            }}>
            {/* DocumentSubmissionProvider is provided at the project level to ensure
                all project-related pages (tech-spec, git location, etc.) can share
                document submission functions and state across navigation */}
            <DocumentSubmissionProvider>
                <Outlet />
            </DocumentSubmissionProvider>
        </ProjectContext.Provider>
    );
};
