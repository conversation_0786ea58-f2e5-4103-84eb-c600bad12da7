/**
 * Document Submission Context
 *
 * This context provides a way to share document submission functions and state
 * across different components in the project flow, particularly for NEW_PRODUCT
 * and REFACTOR_CODE job types.
 *
 * Architecture:
 * 1. The context is provided at the project level (ProjectProvider) to ensure
 *    all project-related pages have access to it.
 * 2. ActionBuildCodebase component sets context data when "Select destination"
 *    button is clicked for NEW_PRODUCT/REFACTOR_CODE flows.
 * 3. GitLocationPanel component uses this context data to execute the document
 *    submission after git location setup.
 *
 * This approach allows us to:
 * - Navigate from tech-spec page to git location page while preserving
 *   the onApprove function and other necessary data
 * - Execute the document submission flow after git location setup
 * - Navigate back to tech-spec page to show progress
 *
 * Flow:
 * Tech-spec page → "Select destination" → Git location page → "Continue" →
 * Document submission → Back to tech-spec page
 */

import React, {createContext, useContext, ReactNode, useState} from 'react';
import {ProjectDocumentType, ProjectType} from '@/lib/entity-types';

interface DocumentSubmissionContextType {
    onApprove?: () => void;
    docType?: ProjectDocumentType;
    isDocumentSubmitting: boolean;
    setIsDocumentSubmitting: React.Dispatch<React.SetStateAction<boolean>>;
    projectType?: ProjectType;
    userProfile?: any;
    setDocumentSubmissionData: (
        data: Partial<DocumentSubmissionContextType>,
    ) => void;
}

const DocumentSubmissionContext = createContext<
    DocumentSubmissionContextType | undefined
>(undefined);

interface DocumentSubmissionProviderProps {
    children: ReactNode;
    initialValue?: Partial<DocumentSubmissionContextType>;
}

export const DocumentSubmissionProvider: React.FC<
    DocumentSubmissionProviderProps
> = ({children, initialValue = {}}) => {
    const [isDocumentSubmitting, setIsDocumentSubmitting] = useState(false);
    const [contextData, setContextData] =
        useState<Partial<DocumentSubmissionContextType>>(initialValue);

    const setDocumentSubmissionData = (
        data: Partial<DocumentSubmissionContextType>,
    ) => {
        setContextData(prev => ({...prev, ...data}));
    };

    const value: DocumentSubmissionContextType = {
        isDocumentSubmitting,
        setIsDocumentSubmitting,
        setDocumentSubmissionData,
        ...contextData,
    };

    return (
        <DocumentSubmissionContext.Provider value={value}>
            {children}
        </DocumentSubmissionContext.Provider>
    );
};

export const useDocumentSubmission = () => {
    const context = useContext(DocumentSubmissionContext);
    if (context === undefined) {
        throw new Error(
            'useDocumentSubmission must be used within a DocumentSubmissionProvider',
        );
    }
    return context;
};
