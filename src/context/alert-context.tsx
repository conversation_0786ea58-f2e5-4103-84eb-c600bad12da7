import {
    createContext,
    ReactNode,
    useCallback,
    useContext,
    useState,
} from 'react';
import {
    Dialog,
    DialogContent,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from '@/components/ui/dialog';
import {Toaster} from '@/components/ui/toaster';

type AlertType = 'HttpError' | 'Firebase' | 'PlatformError';

interface Alert {
    type: AlertType;
    message?: string;
    buttonText?: string;
    children?: ReactNode;
    hideCloseIcon?: boolean;
}

interface AlertContextType {
    showAlert?: (type: Alert) => void;
    clearAlert?: () => void;
}

const AlertContext = createContext<AlertContextType>({});

export const useAlertContext = (): AlertContextType => {
    return useContext(AlertContext);
};

export const AlertProvider = ({children}: {children: ReactNode}) => {
    const [alert, setAlert] = useState<Alert>();
    const showAlert = useCallback((alert: Alert) => {
        setAlert(alert);
    }, []);

    const clearAlert = useCallback(() => {
        setAlert(undefined);
    }, []);

    const handleDimissAlert = useCallback(() => {
        setAlert(undefined);
    }, []);

    return (
        <AlertContext.Provider
            value={{
                showAlert,
                clearAlert,
            }}>
            {children}
            <Toaster />
            {alert && (
                <Dialog open onOpenChange={handleDimissAlert}>
                    <DialogContent
                        className={`min-h-dvh sm:min-h-0 w-full sm:rounded-[24px] sm:max-w-md bg-white ${alert?.hideCloseIcon && '[&>button]:hidden'}`}>
                        <DialogHeader className="flex-grow">
                            <DialogTitle>
                                {alert?.message ?? 'An error has occurred'}
                            </DialogTitle>
                        </DialogHeader>
                        {alert.children}
                        <DialogFooter>
                            <div
                                role="button"
                                className="primary-button"
                                onClick={handleDimissAlert}>
                                {alert?.buttonText ? alert.buttonText : 'OK'}
                            </div>
                        </DialogFooter>
                    </DialogContent>
                </Dialog>
            )}
        </AlertContext.Provider>
    );
};
