export type SubscriptionPlan = 'FREE' | 'ENTERPRISE' | 'PRO' | 'TEAMS';

export interface Subscription {
    planName: SubscriptionPlan;
    status?: string;
    trial_start_date?: string;
    trialEndDate?: number;
    is_trialing?: boolean;
    currentPeriodStartDate?: number;
    currentPeriodEndDate?: number;
}

export interface UserProfile {
    id?: string;
    email?: string;
    firstName?: string;
    lastName?: string;
    company?: string;
    avatar?: string | null;
    providerId?: string;
    subscription?: Subscription;
    isGithubAuthenticated?: boolean;
    registrationCompleted?: boolean;
    userConfig?: UserConfiguration;
}

export interface UserConfiguration {
    techSpecNotificationEnabled: boolean;
    codeGenNotificationEnabled: boolean;
    platformConfig?: Record<string, any>;
}

export type ProjectType = 'NEW_PRODUCT' | 'EXISTING_PRODUCT';
export type ProjectDocumentType = 'software_req' | 'tech_spec' | 'prompt';
type TStatus =
    | 'TODO'
    | 'QUEUED'
    | 'IN_PROGRESS'
    | 'DONE'
    | 'SUBMITTED'
    | 'CREATED'
    | 'COMPLETED';

type CommitStatus = 'PENDING' | 'MERGED' | 'CLOSED';

export type ProjectStatus = TStatus | 'GITHUB_PENDING' | 'GITHUB_COMPLETED';
export type DocStatus = TStatus;
export type TechSpecJobType =
    | 'NEW_PRODUCT'
    | 'EXISTING_PRODUCT'
    | 'ADD_FEATURE'
    | 'REFACTOR_CODE'
    | 'SYNC_TECH_SPEC'
    | 'FIX_BUGS'
    | 'FIX_CVES'
    | 'ADD_TESTING'
    | 'DOCUMENT_CODE'
    | 'CUSTOM';
export type PromptType =
    | 'NEW_PRODUCT'
    | 'ADD_FEATURE'
    | 'REFACTOR_CODE'
    | 'ONBOARD_CODE'
    | 'FIX_BUGS'
    | 'FIX_CVES'
    | 'ADD_TESTING'
    | 'DOCUMENT_CODE'
    | 'CUSTOM';
type PromptStatus = 'DRAFT' | 'SUBMITTED';
export const PendingStates = ['TODO', 'QUEUED', 'IN_PROGRESS'];

export interface ProjectJobMetadata {
    currentIndex: number;
    totalSteps: number;
}

export interface TechSpecRunStatus {
    status: DocStatus;
    jobMetadata?: ProjectJobMetadata;
}
export interface TechSpecStatus extends TechSpecRunStatus {
    id: string;
    jobType: TechSpecJobType; // in json coming from server
    version: number;
    pdfReportStatus: string;
    updatedAt: number;
}

// export interface CodeGenRunStatus {
//     commitStatus?: CommitStatus;
//     status: TStatus;
//     createAt: number;
//     updatedAt: number;
//     jobMetadata: {
//         repoUrl: string;
//         repoName: string;
//     };
// }

export interface CodeGenCommitStatus {
    id: string;
    projectRunId: string;
    codeGenId: string;
    orgName: string;
    repoName: string;
    repoId: string;
    blitzyBranchUrl: string;
    blitzyCommitHash: string;
    blitzyCommitUrl: string;
    branchName: string;
    prAction: 'NOT_TAKEN' | 'CLOSED' | 'OPEN';
    prLink: string;
    status: CommitStatus;
    originalHeadCommitHash: string;
    prNumber: number;
    metadata: any;
    createdAt: string;
    resolvedAt: string;
}

export interface CodeGenStatus {
    id: string;
    status: TStatus;
    tech_spec_id: string;
    code_repo_url: string;
    readyAt: number;
    commitStatus?: CommitStatus;
}
export interface ProjectState {
    status?: ProjectStatus; // 'CREATED' or 'COMPLETED'
    technicalSpec?: TechSpecStatus;
    softwareRequirement?: {
        status: DocStatus;
    };
    codeGeneration?: CodeGenStatus;
    isTechSpecInSync?: boolean;
}

export interface Project {
    // these attributes must be there when the project is created
    id: string;
    name: string;
    initialType: ProjectType; // in json coming from server
    type: ProjectType; // copy of initialType
    owner: string;
    repoUrl: string;
    createdAt: number; // UNIX timestamp
    updatedAt: number; // UNIX timestamp

    isDisabled?: boolean;
    disableReason?: string;
    // then we need the prompt from the user
    prompt?: string;
    promptStatus?: PromptStatus;
    promptUpdatedAt?: number; // UNIX timestamp
}
export interface ProjectWithStatus extends Project {
    status?: ProjectStatus;
}

export interface GitAccount {
    id: number;
    name: string;
    type?: string;
}

export interface GitRepo {
    id: number;
    name: string;
    fullName: string;
    private?: boolean;
}

export type GitLocationType = 'SOURCE' | 'TARGET';
export interface GitLocation {
    type: GitLocationType;
    orgName: string;
    repoId: string;
    repoName?: string;
    branchName: string;
    createRepo: boolean;
}

export interface GitBranch {
    name: string;
    protected: boolean;
}

export interface ProjectDetail extends ProjectWithStatus {
    // codeGeneration?: CodeGenStatus;
    // technicalSpec?: TechSpecStatus;
    technicalSpecAvailable?: boolean;
    codeGenerationAvailable?: boolean;
    gitSink?: GitLocation;
    gitSource?: GitLocation;
}

export interface RegisterUserInput {
    email: string;
    password: string;
    firstName: string;
    lastName: string;
    company: string;
    avatarBlob: string;
    utm_source?: string;
    utm_medium?: string;
    utm_campaign?: string;
    utm_id?: string;
}

export interface Status201Result {
    id: string;
    message: string;
}

export interface UtmParams {
    utm_source: string;
    utm_medium: string;
    utm_campaign: string;
    utm_id: string;
    landing_page: string;
    referrer: string;
    landing_timestamp: string;
}

export interface GithubInstallationStatus {
    status: string;
    installation_id: string;
    installation_type: string;
    target_name: string;
}
export interface GithubInstallationStatusResponse {
    results: GithubInstallationStatus[];
}

export interface Usage {
    maxLoC: number;
    usedLoC: number;
}

// export interface GeoLocation {
//     id: string;
//     user_id: string;
//     ip_address: string;
//     country_code: string;
//     city: string;
//     region: string;
//     latitude: number;
//     longitude: number;
//     timezone: string;
// }
