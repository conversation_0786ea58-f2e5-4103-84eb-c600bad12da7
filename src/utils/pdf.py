from blitzy_utils.logger import logger
from blitzy_utils.service_client import ServiceClient


def convert_to_pdf(project_name: str, file_path: str):
    """
    Send a POST request to convert markdown to PDF.

    Args:
        project_name (str): Name of the project
        file_path (str): Repository URL

    Returns:
        Response: The response from the PDF conversion service
    """
    updated_path = file_path.replace(".md", ".pdf")
    payload = {
        "projectName": project_name,
        "downloadCloudFilePath": file_path,
        "uploadCloudFilePath": updated_path,
    }

    try:
        with ServiceClient() as client:
            response = client.post("markdown", "/v1/pdf/convert_direct", json=payload, timeout=600)

            return response
    except Exception as e:
        logger.error(f"Failed to convert markdown to PDF: {str(e)}")
        raise Exception(f"PDF conversion failed: {str(e)}")
