from google.cloud import storage

from src.consts import storage_client, GCS_BUCKET_NAME


def copy_gcs_file(source_path, destination_path):
    """
    Copies a file from one path to another in Google Cloud Storage (GCS).

    :param source_path: The path of the source file in the bucket.
    :type source_path: str

    :param destination_path: The path where the file will be copied to in
        the bucket.
    :type destination_path: str

    :return: Returns True upon successful copy operation.
    :rtype: bool
    """
    bucket = storage_client.bucket(GCS_BUCKET_NAME)
    source_blob = bucket.blob(source_path)
    bucket.copy_blob(
        source_blob, bucket, destination_path
    )

    print(f"File {source_path} copied to {destination_path} in bucket {GCS_BUCKET_NAME}.")
    return True
