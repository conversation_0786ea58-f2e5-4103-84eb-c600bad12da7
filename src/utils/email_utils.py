from typing import Optional, <PERSON><PERSON>

import requests
from blitzy_utils.logger import logger
from notification.constants import EmailTemplates
from notification.service import NotificationService

from src.consts import SENDGRID_VALIDATION_API_KEY

# Global notification service instance
notification_service = NotificationService()


def validate_email_with_sendgrid(email: str) -> <PERSON>ple[bool, Optional[str]]:
    """
    Validates email using SendGrid's Email Validation API.

    Args:
        email: The email address to validate

    Returns:
        Tuple[bool, Optional[str]]: (is_valid, error_message)
    """
    api_key = SENDGRID_VALIDATION_API_KEY
    if not api_key:
        logger.warning("SENDGRID_VALIDATION_API_KEY not found, skipping email validation")
        return True, None

    # SendGrid Email Validation API endpoint
    api_url = "https://api.sendgrid.com/v3/validations/email"

    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json'
    }

    payload = {
        'email': email,
        'source': 'EmailNotification'
    }

    try:
        response = requests.post(
            api_url,
            headers=headers,
            json=payload,
            timeout=10  # 10 second timeout
        )

        if response.status_code != 200:
            logger.error(f"SendGrid validation failed: {response.text}")
            return True, None  # Continue with sending if validation fails

        try:
            result = response.json().get('result', {})
            verdict = result.get('verdict', 'Valid')

            if verdict.lower() == 'invalid':
                return False, "Email address is invalid"

            return True, None
        except Exception as e:
            logger.error(f"Error parsing SendGrid validation response: {str(e)}")
            return True, None  # Continue with sending if validation fails

    except Exception as e:
        logger.error(f"Unexpected error during email validation: {str(e)}")
        return True, None  # Continue with sending if validation fails


def send_tech_spec_completion_notification(to_email: str, username: str, project_name: str, project_url: str,
                                           notification_preferences_url: str) -> bool:
    """
    Send tech spec completion notification email using notification_utils.

    Args:
        to_email: Recipient email address
        username: User's name
        project_name: Name of the project
        project_url: URL to view the project

    Returns:
        bool: True if email sent successfully, False otherwise
    """
    try:
        # Validate email before sending
        is_valid, error_message = validate_email_with_sendgrid(to_email)
        if not is_valid:
            logger.error(f"Email validation failed for {to_email}: {error_message}")
            return False

        # Prepare template variables
        template_vars = {
            'username': username,
            'project_name': project_name,
            'project_url': project_url,
            'notification_preferences_url': notification_preferences_url
        }

        # Send email using notification service
        try:
            result = notification_service.send(
                template_type=EmailTemplates.TECH_SPEC_READY,
                to=to_email,
                template_vars=template_vars,
                subject="Tech spec is ready!"
            )

            if result.get('success'):
                logger.info(f"Tech spec completion email sent successfully to {to_email}")
                return True
            else:
                logger.error(f"Failed to send tech spec completion email to {to_email}")
                return False

        except Exception as e:
            logger.error(f"Error sending tech spec completion email via notification service: {str(e)}")
            return False

    except Exception as e:
        logger.error(f"Error sending tech spec completion notification: {str(e)}")
        return False


def send_code_generation_completion_notification(to_email: str, username: str, project_name:
                                                 str, project_url: str, notification_preferences_url: str) -> bool:
    """
    Send code generation completion notification email using notification_utils.

    Args:
        to_email: Recipient email address
        username: User's name
        project_name: Name of the project
        project_url: URL to view the project

    Returns:
        bool: True if email sent successfully, False otherwise
    """
    try:
        # Validate email before sending
        is_valid, error_message = validate_email_with_sendgrid(to_email)
        if not is_valid:
            logger.error(f"Email validation failed for {to_email}: {error_message}")
            return False

        # Prepare template variables
        template_vars = {
            'username': username,
            'project_name': project_name,
            'project_url': project_url,
            'notification_preferences_url': notification_preferences_url
        }

        # Send email using notification service
        try:
            result = notification_service.send(
                template_type=EmailTemplates.CODE_GEN_READY,
                to=to_email,
                template_vars=template_vars,
                subject="Code generation is ready!"
            )

            if result.get('success'):
                logger.info(f"Code generation completion email sent successfully to {to_email}")
                return True
            else:
                logger.error(f"Failed to send code generation completion email to {to_email}")
                return False

        except Exception as e:
            logger.error(f"Error sending code generation completion email via notification service: {str(e)}")
            return False

    except Exception as e:
        logger.error(f"Error sending code generation completion notification: {str(e)}")
        return False
