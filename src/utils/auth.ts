// src/utils/auth.ts

export const isAuthenticated = (): boolean => {
  return localStorage.getItem("authToken") !== null;
};

export const logout = (): void => {
  localStorage.removeItem("authToken");
  localStorage.removeItem("userEmail");
  window.location.href = "/login";
};

export const getAuthToken = (): string | null => {
  return localStorage.getItem("authToken");
};

export const getUserEmail = (): string | null => {
  return localStorage.getItem("userEmail");
};
