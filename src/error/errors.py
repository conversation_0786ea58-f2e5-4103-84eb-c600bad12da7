import http

from flask_utils.base_error import BaseError


class ResourceNotFound(BaseError):
    """Resource not found error."""

    def __init__(self, message: str = None, status_code: int = 404):
        super().__init__(message=message, status_code=status_code)


class GithubBaseError(BaseError):
    """Raised when the Github API returns an error."""

    def __init__(self, message: str = None, status_code: int = 500, error_code="GITHUB_API_ERROR"):
        super().__init__(message=message, status_code=status_code, error_code=error_code)


class SecretNotFoundError(BaseError):
    """Failed to update user information."""

    def __init__(self, message: str = None, status_code: int = 404):
        super().__init__(message=message, status_code=status_code)


class BranchNotFoundError(BaseError):
    """Failed to get branch."""

    def __init__(self, message: str = None, status_code: int = 400):
        super().__init__(message=message, status_code=status_code)


class OrganizationNotFoundError(BaseError):
    """Failed to get organization."""

    def __init__(self, message: str = None, status_code: int = 400):
        super().__init__(message=message, status_code=status_code)


class FileContentNotFoundError(BaseError):
    """File content not available."""

    def __init__(self, message: str = None, status_code: int = 400):
        super().__init__(message=message, status_code=status_code)


class GithubAppException(BaseError):
    """Raised when the Github API returns an error."""

    def __init__(self, message: str = None, status_code: int = 500):
        super().__init__(message=message, status_code=status_code)


class GithubRepoPRAlreadyExistError(BaseError):
    """Raised when Github PR already exists."""

    def __init__(self, message: str = None, status_code: int = 422):
        super().__init__(message=message, status_code=status_code)


class GithubRepoAlreadyExistsError(BaseError):
    """Github repo already exists error."""

    def __init__(self, message: str = None, status_code: int = 409):
        super().__init__(message=message, status_code=status_code)


class GithubPermissionError(BaseError):
    """Github App lacks permission"""

    def __init__(self, message: str = None, status_code: int = 403):
        super().__init__(message=message, status_code=status_code)


class AccessNotAllowedError(BaseError):
    """Access not allowed error."""

    def __init__(self, message: str = None, status_code: int = 403):
        super().__init__(message=message, status_code=status_code)


class InstallationNotFoundError(BaseError):
    def __init__(self, message: str):
        super().__init__(message=message, status_code=http.HTTPStatus.BAD_REQUEST.value)


class EndpointMisconfigured(BaseError):
    """Raised when the endpoint is misconfigured(eg missing some env variables)"""

    def __init__(self, message: str = None, status_code: int = http.HTTPStatus.SERVICE_UNAVAILABLE):
        super().__init__(message=message, status_code=status_code)


class AzureAPIError(BaseError):
    """Raised when the Azure API returns an error."""

    def __init__(self, message: str, status_code: int = http.HTTPStatus.SERVICE_UNAVAILABLE):
        super().__init__(message=message, status_code=status_code)


class AzureInvalidTokenError(BaseError):
    """
    Raised when an Azure access token is invalid or unusable for any reason
    """
    def __init__(self, message: str, status_code: int = http.HTTPStatus.SERVICE_UNAVAILABLE):
        super().__init__(message=message, status_code=status_code)


class TokenExpiredError(BaseError):
    """Raised when the refresh token has expired and user needs to re-authenticate."""
    def __init__(self, message: str = None, status_code: int = 401, error_code="AZURE_REFRESH_ERROR"):
        super().__init__(message=message, status_code=status_code, error_code=error_code)


class TokenRefreshError(BaseError):
    """Raised when token refresh fails for reasons other than expiration."""
    pass


class AzureBaseError(BaseError):
    """Raised when the Github API returns an error."""

    def __init__(self, message: str = None, status_code: int = 500, error_code="AZURE_API_ERROR"):
        super().__init__(message=message, status_code=status_code, error_code=error_code)


class PRActionError(BaseError):
    """PR Action error."""

    def __init__(self, message: str = None, status_code: int = 400):
        super().__init__(message=message, status_code=status_code)


class SecretNotFound(BaseError):
    def __init__(self, message: str):
        super().__init__(message=message, status_code=http.HTTPStatus.BAD_REQUEST.value)


class AzureAlreadyIntegrated(BaseError):
    def __init__(self, message: str):
        super().__init__(message=message, status_code=http.HTTPStatus.CONFLICT.value)


class AzureAlreadyIntegrated(BaseError):
    def __init__(self, message: str):
        super().__init__(message=message, status_code=http.HTTPStatus.CONFLICT.value)
