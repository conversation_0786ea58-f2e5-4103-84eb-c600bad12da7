from flask_utils.base_error import BaseError


class InvalidPayloadError(BaseError):
    """Invalid payload."""

    def __init__(self, message: str = None, status_code: int = 400):
        super().__init__(message=message, status_code=status_code)


class NotFoundError(BaseError):
    """Resource not found."""

    def __init__(self, message: str = None, status_code: int = 404):
        super().__init__(message=message, status_code=status_code)
