// src/services/api.ts
import axios from "axios";

const API_BASE_URL = "http://localhost:8081/v1";

// Define types for API responses
export interface Job {
  createdAt: string;
  id: string;
  isTriggered: boolean;
  jobId: string;
  jobName: string | null;
  jobPhase: string;
  jobStatus: string | string[];
  projectId: string;
  projectName: string;
  updatedAt: string;
  userEmail: string;
  userId: string;
  userFirstName?: string;
  userLastName?: string;
  eventData: any;
  planName?: string;
}

export interface JobDetail extends Job {
  eventData: any;
  jobSubmissionMetadata: {
    executionId: string;
    executionName: string;
    status: string;
    submissionTime: string;
  };
  techSpecId?: string;
  codeGenId?: string;
  triggerTopic?: string | null;
}

export interface JobsResponse {
  items: Job[];
  pagination: {
    limit: number;
    page: number;
    pages: number;
    total: number;
  };
}

export interface JobStats {
  total: number;
  succeeded: number;
  failed: number;
  running: number;
}

export interface TriggerResponse {
  message: string;
}

// Add authentication APIs
export const login = async (email: string, password: string) => {
  try {
    const response = await axios.post(`${API_BASE_URL}/auth/login`, {
      email,
      password,
    });

    // Store token in localStorage for future requests
    localStorage.setItem("authToken", response.data.token);
    localStorage.setItem("userEmail", response.data.email);

    return response.data;
  } catch (error) {
    console.error("Login error:", error);
    throw error;
  }
};

// Add authorization header to all requests if token exists
axios.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem("authToken");
    if (token) {
      config.headers["Authorization"] = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Handle 401 responses (expired or invalid token)
axios.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response && error.response.status === 401) {
      // Clear auth data and redirect to login
      localStorage.removeItem("authToken");
      localStorage.removeItem("userEmail");
      window.location.href = "/login";
    }
    return Promise.reject(error);
  }
);

// Process job status to determine if it's succeeded, failed, or running
export const processJobStatus = (job: Job) => {
  if (Array.isArray(job.jobStatus)) {
    // If jobStatus is an array, check if it contains 'CANCELLED'
    return job.jobStatus.includes("CANCELLED") ? "cancelled" : "succeeded";
  } else {
    // If jobStatus is a string
    if (job.jobStatus === "SUCCESS") return "succeeded";
    if (job.jobStatus === "FAILED") return "failed";
    // If it's not success or failed, consider it running
    return "running";
  }
};

// API functions
export const getJobs = async (
  page: number = 1,
  limit: number = 20,
  search: string = "",
  status?: string,
  jobType?: string,
  planType?: string,
  duration?: string
): Promise<JobsResponse> => {
  try {
    const response = await axios.get<JobsResponse>(`${API_BASE_URL}/job`, {
      params: {
        page,
        limit,
        search: search || undefined,
        status: status || undefined,
        jobType: jobType && jobType !== "all" ? jobType : undefined,
        planType: planType && planType !== "all" ? planType : undefined,
        duration: duration && duration !== "all" ? duration : undefined,
      },
    });
    return response.data;
  } catch (error) {
    console.error("Error fetching jobs:", error);
    throw error;
  }
};

// Get all jobs for statistics
export const getAllJobsStats = async (
  search: string = "",
  jobType?: string,
  planType?: string,
  duration?: string
): Promise<JobStats> => {
  try {
    // Make parallel requests for each status to get accurate counts
    const [totalResponse, succeededResponse, runningResponse, failedResponse] =
      await Promise.all([
        // Get total count
        axios.get<JobsResponse>(`${API_BASE_URL}/job`, {
          params: {
            limit: 1,
            search: search || undefined,
            jobType: jobType && jobType !== "all" ? jobType : undefined,
            planType: planType && planType !== "all" ? planType : undefined,
          },
        }),
        // Get succeeded count
        axios.get<JobsResponse>(`${API_BASE_URL}/job`, {
          params: {
            limit: 1,
            status: "SUCCESS",
            search: search || undefined,
            jobType: jobType && jobType !== "all" ? jobType : undefined,
            planType: planType && planType !== "all" ? planType : undefined,
          },
        }),
        // Get running count
        axios.get<JobsResponse>(`${API_BASE_URL}/job`, {
          params: {
            limit: 1,
            status: "RUNNING",
            search: search || undefined,
            jobType: jobType && jobType !== "all" ? jobType : undefined,
            planType: planType && planType !== "all" ? planType : undefined,
            duration: duration && duration !== "all" ? duration : undefined,
          },
        }),
        // Get failed count
        axios.get<JobsResponse>(`${API_BASE_URL}/job`, {
          params: {
            limit: 1,
            status: "FAILED",
            search: search || undefined,
            jobType: jobType && jobType !== "all" ? jobType : undefined,
            planType: planType && planType !== "all" ? planType : undefined,
          },
        }),
      ]);

    return {
      total: totalResponse.data.pagination.total,
      succeeded: succeededResponse.data.pagination.total,
      failed: failedResponse.data.pagination.total,
      running: runningResponse.data.pagination.total,
    };
  } catch (error) {
    console.error("Error fetching all jobs stats:", error);
    throw error;
  }
};

export const getJobDetails = async (id: string): Promise<JobDetail> => {
  try {
    const response = await axios.get<JobDetail>(`${API_BASE_URL}/job/${id}`);
    return response.data;
  } catch (error) {
    console.error(`Error fetching job details for ${id}:`, error);
    throw error;
  }
};

export const refreshJobStatus = async (id: string): Promise<JobDetail> => {
  try {
    const response = await axios.get<JobDetail>(`${API_BASE_URL}/job/${id}`);
    return response.data;
  } catch (error) {
    console.error(`Error refreshing job status for ${id}:`, error);
    throw error;
  }
};

export const triggerJob = async (id: string): Promise<TriggerResponse> => {
  try {
    const response = await axios.post<TriggerResponse>(
      `${API_BASE_URL}/job/${id}/trigger`
    );
    return response.data;
  } catch (error) {
    console.error(`Error triggering job ${id}:`, error);
    throw error;
  }
};

const JOB_TYPE_TO_TRIGGER_TOPIC: Record<string, string> = {
  "code-uploader": "upload-code",
  "code-graph-generator": "graph-code",
  "reverse-document-generator": "generate-reverse-document",
  "code-downloader": "download-code",
  "document-generator": "generate-document",
  "reverse-file-map-generator": "generate-reverse-file-map",
  "repo-structure-generator": "generate-repo-structure",
  "code-generator": "generate-code",
  "code-spec-generator": "generate-code-spec",
  "reverse-code-generator": "generate-reverse-code",
};

// Utility to convert camelCase keys to snake_case recursively
function camelToSnake(obj: any): any {
  if (Array.isArray(obj)) {
    return obj.map(camelToSnake);
  } else if (obj !== null && typeof obj === "object") {
    return Object.fromEntries(
      Object.entries(obj).map(([key, value]) => [
        key.replace(/([A-Z])/g, "_$1").toLowerCase(),
        camelToSnake(value),
      ])
    );
  }
  return obj;
}

export const triggerCustomJob = async (
  jobType: string,
  payload: Record<string, any>
): Promise<TriggerResponse> => {
  const triggerTopic = JOB_TYPE_TO_TRIGGER_TOPIC[jobType];
  if (!triggerTopic) {
    throw new Error(`No trigger topic found for job type: ${jobType}`);
  }
  try {
    const snakePayload = camelToSnake(payload);
    const response = await axios.post<TriggerResponse>(
      `${API_BASE_URL}/job/trigger/custom/job`,
      {
        trigger_topic: triggerTopic,
        ...snakePayload,
      }
    );
    return response.data;
  } catch (error) {
    console.error(`Error triggering custom job for jobType ${jobType}:`, error);
    throw error;
  }
};

export const downloadLatestBinary = async (): Promise<Blob> => {
  try {
    const response = await axios.get(`${API_BASE_URL}/job/binary/latest`, {
      responseType: "blob",
    });
    return response.data;
  } catch (error) {
    console.error("Error downloading binary:", error);
    throw error;
  }
};
