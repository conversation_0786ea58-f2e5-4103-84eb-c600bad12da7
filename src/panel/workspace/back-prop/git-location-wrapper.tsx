import {useEffect} from 'react';
import {useNavigate} from 'react-router-dom';
import {GitLocationPanel} from './git-location-panel';
import {useGithubInstallation} from '../hooks/github-status-hook';
import {useProjectContext} from '@/context/project-context';

interface GitLocationWrapperProps {
    type: 'new-product' | 'onboard-code' | 'refactor-code' | 'custom';
}

export function GitLocationWrapper({type}: GitLocationWrapperProps) {
    const {project} = useProjectContext();
    const {status} = useGithubInstallation();
    const navigateTo = useNavigate();

    useEffect(() => {
        if (
            ['404', 'PENDING', 'REJECTED', 'UNINSTALLED', 'SUSPENDED'].includes(
                status || '',
            )
        ) {
            navigateTo(
                `/workspace/project/${project?.id}/back-prop/connect-git`,
            );
        }
    }, [navigateTo, project?.id, status]);

    return <GitLocationPanel type={type} />;
}
