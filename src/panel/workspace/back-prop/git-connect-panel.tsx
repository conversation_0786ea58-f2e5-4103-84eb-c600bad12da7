import {useCallback, useEffect} from 'react';
import {ProjectPanelContent} from '../project-panel-content.tsx';
import iconCheck from '../icons/check.svg';
import iconGithub from '../icons/github.svg';
import {useGithubInstallation} from '../hooks/github-status-hook';
import {useNavigate} from 'react-router-dom';
import {logGAEvent} from '@/lib/utils';
export function GitConnectPanel() {
    // const [showConnectMsg, setShowConnectMsg] = useState<boolean>(true);
    const {status, checkGithubInstallationStatus} = useGithubInstallation();
    const navigateTo = useNavigate();

    useEffect(() => {
        checkGithubInstallationStatus();
    }, [checkGithubInstallationStatus]);

    const handleConnectGithub = useCallback(() => {
        const projectID = window.location.pathname.split('/')[3];
        logGAEvent('github_connect_button_clicked', {
            event_category: 'Integration',
            event_label: status || 'unknown',
            project_id: projectID,
            action: status === 'PENDING' ? 'view_status' : 'connect',
        });
        if (status === 'PENDING') {
            navigateTo('/workspace/settings/integrations');
        } else {
            const url = import.meta.env.VITE_GITHUB_INSTALL_URL;
            if (url) {
                logGAEvent('github_oauth_initiated', {
                    event_category: 'Integration',
                    project_id: projectID,
                });
                localStorage.setItem(
                    'github-connect-redirect-to-project-id',
                    projectID,
                );
                window.location.assign(url);
            }
        }
    }, [status, navigateTo]);

    const currentStatus = status ?? 'PENDING';

    return (
        <ProjectPanelContent>
            <div className="h-full flex flex-col items-center justify-center">
                <div className="bg-white h-auto py-[8px] sm:p-0 pb-[24px] flex flex-col w-full sm:m-0 sm:w-[500px]">
                    {/* Content Container */}
                    <div className="flex flex-col items-center justify-between flex-grow gap-[24px]">
                        {/* Main Content - Centered */}
                        <div className="flex flex-col items-center gap-0 flex-grow justify-center">
                            <img
                                src={iconGithub}
                                alt="GitHub"
                                className="w-[64px] h-[64px]"
                            />

                            {/* Title and Subtitle */}
                            <div className="mt-[16px] mb-[24px] text-center">
                                <h2 className="text-[20px] sm:text-[24px] font-semibold leading-7 text-black">
                                    {currentStatus !== 'ACTIVE'
                                        ? currentStatus === 'PENDING'
                                            ? 'GitHub connection waiting for organization approval'
                                            : 'Connect your GitHub account'
                                        : 'GitHub connected'}
                                </h2>
                            </div>

                            {/* Features List */}
                            <div className="flex flex-col w-full text-[16px] leading-[150%] p-[24px] sm:p-[32px] gap-[24px] rounded-[24px] bg-gray-100">
                                {[
                                    'Create technical specs from your codebase',
                                    'Add new features with natural language',
                                    'Refactor outdated technologies easily',
                                    'Sync your repo with your existing product',
                                ].map((text, index) => (
                                    <div
                                        key={index}
                                        className="flex items-start gap-[8px]">
                                        <img
                                            src={iconCheck}
                                            alt="Check"
                                            className="w-[24px] h-[24px] flex-shrink-0"
                                        />
                                        <span className="text-[16px] leading-[150%] tracking-tight text-[#333]">
                                            {text}
                                        </span>
                                    </div>
                                ))}
                            </div>
                        </div>
                        <button
                            onClick={handleConnectGithub}
                            className="primary-button text-[18px] w-fit">
                            {currentStatus === 'PENDING'
                                ? 'View status'
                                : 'Connect'}
                        </button>
                    </div>
                </div>
            </div>
        </ProjectPanelContent>
    );
}
