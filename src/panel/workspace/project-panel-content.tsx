import {ReactNode} from 'react';
import CommandCenter from './command-center';
import {ProjectDocumentType} from '@/lib/entity-types';
import {ProjectState} from '@/lib/entity-types';
import {DetailDestination} from './command-center/detail-destination';
import {DetailHumanTasks} from './command-center/detail-human-tasks';
import {DetailDeepInThought} from './command-center/detail-deep-in-thought';
import {useProjectContext} from '@/context/project-context';
// import {useLocation} from 'react-router-dom';
import {DetailSource} from './command-center/detail-source';
import {DetailPR} from './command-center/detail-pr';
import {useLocation} from 'react-router-dom';

interface Props {
    header?: ReactNode;
    children: ReactNode;
    isReadOnly?: boolean;
    isPromptValid?: boolean;
    handleSubmit?: () => Promise<boolean>;

    onStartEditing?: () => void;
    onCancelEditing?: () => void;
    onSaveEdits?: () => Promise<void>;
    onApprove?: () => Promise<void>;
    onDownloadPDF?: () => Promise<void>;
    docType?: ProjectDocumentType;
    isEditing?: boolean;
    isContentDirty?: boolean;
    isDocumentSubmitting?: boolean;
    setIsDocumentSubmitting?: React.Dispatch<React.SetStateAction<boolean>>;
    jobStatus?: ProjectState;
    disableNavigationBlocker?: () => void;
}

export function ProjectPanelContent({
    children,
    header,
    isReadOnly,
    isPromptValid,
    handleSubmit,

    docType,
    isEditing = false,
    onStartEditing,
    onCancelEditing,
    onSaveEdits,
    onApprove,
    onDownloadPDF,
    isContentDirty = false,
    isDocumentSubmitting = false,
    setIsDocumentSubmitting,
    disableNavigationBlocker,
}: Props) {
    const {projectState, project} = useProjectContext();
    const {pathname} = useLocation();

    const showDetailsSection =
        pathname.includes('prompt') ||
        pathname.includes('tech-spec') ||
        !(
            pathname === '/workspace' ||
            pathname.includes('workspace/projects') ||
            pathname.includes('settings') ||
            pathname.includes('status') ||
            pathname.includes('delete') ||
            pathname?.includes('back-prop/new-product') ||
            pathname?.includes('back-prop/source-code') ||
            pathname?.includes('back-prop/connect-git')
        );

    return (
        <div className="flex flex-col gap-0 h-full w-full">
            <div className="sticky top-0">
                <CommandCenter
                    isReadOnly={isReadOnly}
                    isPromptValid={isPromptValid}
                    handleSubmit={handleSubmit}
                    docType={docType}
                    onApprove={onApprove}
                    onCancelEditing={onCancelEditing}
                    onSaveEdits={onSaveEdits}
                    onStartEditing={onStartEditing}
                    onDownloadPDF={onDownloadPDF}
                    isEditing={isEditing}
                    isContentDirty={isContentDirty}
                    isDocumentSubmitting={isDocumentSubmitting}
                    setIsDocumentSubmitting={setIsDocumentSubmitting}
                    disableNavigationBlocker={disableNavigationBlocker}
                />
            </div>

            {/* TODO: we need to incorporate {header} into <CommandCenter /> */}
            {header && (
                <div className="px-[24px] py-[16px] sm:p-[32px]">{header}</div>
            )}
            <div className="flex-grow overflow-y-auto w-full">
                {/* Mobile Details Row */}
                {projectState?.codeGeneration?.status !== 'TODO' &&
                    showDetailsSection && (
                        <div className="w-full flex sm:hidden flex-col gap-[24px] px-[24px] --mb-[24px] border-b border-[#D9D9D9] pb-[24px]">
                            <DetailSource
                                project={project}
                                projectState={projectState}
                            />
                            <DetailDestination
                                project={project}
                                projectState={projectState}
                            />
                            <DetailPR
                                projectType={project?.type}
                                projectState={projectState}
                            />
                            <DetailHumanTasks
                                jobStatus={projectState}
                                projectType={project?.type}
                            />
                            <DetailDeepInThought jobStatus={projectState} />
                        </div>
                    )}
                <div className="max-w-[1440px] sm:p-[32px] px-[24px] py-[16px] h-full w-full mx-auto">
                    {children}
                </div>
            </div>
        </div>
    );
}
