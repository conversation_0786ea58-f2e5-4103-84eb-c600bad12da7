import {useCallback, useEffect, useState} from 'react';
import {getGithubInstallationStatus} from '@/lib/backend';
import {GithubInstallationStatus} from '@/lib/entity-types';

export function useGithubInstallation() {
    const [status, setStatus] = useState<string>();
    const [installationId, setInstallationId] = useState<string>();
    const [target_name, setTargetName] = useState<string>();
    const [installation_type, setInstallationType] = useState<string>();

    const checkGithubInstallationStatus = useCallback(async () => {
        try {
            const githubInstallationStatusResponse =
                await getGithubInstallationStatus();
            if (githubInstallationStatusResponse.results.length === 0) {
                setStatus('404');
                return {
                    status: '404',
                    installation_id: undefined,
                    installation_type: undefined,
                    target_name: undefined,
                } as unknown as GithubInstallationStatus;
            }
            const response = githubInstallationStatusResponse.results[0];
            setStatus(response.status);
            if ('installation_id' in response) {
                setInstallationId(response.installation_id);
            }
            if ('target_name' in response) {
                setTargetName(response.target_name);
            }
            if ('installation_type' in response) {
                setInstallationType(response.installation_type);
            }
            return response as GithubInstallationStatus;
        } catch (err) {
            console.error('Failed to fetch GitHub status:', err);
            return undefined;
        }
    }, []);

    const resetStatus = useCallback(() => {
        setStatus(undefined);
    }, []);

    useEffect(() => {
        checkGithubInstallationStatus();
    }, [checkGithubInstallationStatus]);

    return {
        status,
        checkGithubInstallationStatus,
        resetStatus,
        installationId,
        target_name,
        installation_type,
    };
}
