import {useState, FC, useCallback, useEffect} from 'react';
import {useGithubInstallation} from '../hooks/github-status-hook';
import iconGithub from '../icons/github.svg';
import DotsHorizontalIcon from '../icons/dots-horizontal-rounded';
import PlugIcon from '../icons/plug';
import Refresh from '../icons/refresh';
import SettingsIcon from '../icons/wrench';

const BASE_STATUS_CLASSES =
    'inline-flex px-[12px] py-[4px] justify-center items-center gap-1 rounded-full text-[12px] text-center font-semibold';

const STATUS_CONFIGS = {
    PENDING: {
        text: 'Pending',
        classes: `${BASE_STATUS_CLASSES} bg-yellow-100 text-yellow-800`,
        menuConfig: {
            showMenu: true,
            options: {
                showRefresh: true,
                showReconnect: true,
                showConfigure: false,
            },
        },
    },
    ACTIVE: {
        text: 'Connected',
        classes: `${BASE_STATUS_CLASSES} bg-green-100 text-green-800`,
        menuConfig: {
            showMenu: true,
            options: {
                showRefresh: false,
                showReconnect: false,
                showConfigure: true,
            },
        },
    },
    UNINSTALLED: {
        text: 'Disconnected',
        classes: `${BASE_STATUS_CLASSES} bg-red-100 text-red-800`,
        menuConfig: {
            showMenu: true,
            options: {
                showRefresh: false,
                showReconnect: true,
                showConfigure: false,
            },
        },
    },
    SUSPENDED: {
        text: 'Suspended',
        classes: `${BASE_STATUS_CLASSES} bg-orange-100 text-orange-800`,
        menuConfig: {
            showMenu: true,
            options: {
                showRefresh: false,
                showReconnect: true,
                showConfigure: false,
            },
        },
    },
    REJECTED: {
        text: 'Rejected',
        classes: `${BASE_STATUS_CLASSES} bg-red-100 text-red-800`,
        menuConfig: {
            showMenu: true,
            options: {
                showRefresh: false,
                showReconnect: true,
                showConfigure: false,
            },
        },
    },
    '404': {
        text: 'Authentication Required',
        classes: `${BASE_STATUS_CLASSES} bg-blue-100 text-blue-800 cursor-pointer`,
        menuConfig: {
            showMenu: true,
            options: {
                showRefresh: false,
                showReconnect: true,
                showConfigure: false,
            },
        },
    },
} as const;

interface TableHeaderProps {
    label: string;
    className?: string;
}

interface ActionMenuItemProps {
    icon: React.ReactNode;
    label: string;
    onClick?: () => void;
    className?: string;
}

interface ActionMenuProps {
    isOpen: boolean;
    onClose: () => void;
    onRefresh: () => void;
    onReconnect: () => void;
    onConfigure: () => void;
    showRefresh: boolean;
    showReconnect: boolean;
    showConfigure: boolean;
    currentStatus: string;
}

const TableHeader = ({label, className}: TableHeaderProps) => (
    <div
        className={`p-[16px] flex justify-start items-center border-b border-[#D9D9D9] ${className ?? ''}`}>
        <span className="text-[16px] font-semibold tracking-tight">
            {label}
        </span>
    </div>
);

const ActionMenuItem = ({
    icon,
    label,
    onClick,
    className,
}: ActionMenuItemProps) => (
    <div
        role="button"
        onClick={onClick}
        className={`flex w-full p-[12px] items-center gap-2 cursor-pointer hover:bg-gray-50 ${className ?? ''}`}>
        <div className="flex w-[20px] h-[20px] justify-center items-center flex-shrink-0">
            {icon}
        </div>
        <span className="text-black font-inter text-[16px] font-semibold leading-6">
            {label}
        </span>
    </div>
);

const ActionMenu = ({
    isOpen,
    onClose,
    onRefresh,
    onReconnect,
    showRefresh,
    showReconnect,
    currentStatus,
    showConfigure,
    onConfigure,
}: ActionMenuProps) => {
    if (!isOpen) return null;

    return (
        <>
            <div className="fixed inset-0 z-40" onClick={onClose} />
            <div className="absolute right-[16px] top-[46px] sm:top-[56px] w-max sm:flex flex-col items-start self-stretch rounded-2xl border border-gray-200 bg-white z-50 shadow-lg">
                {showRefresh && (
                    <ActionMenuItem
                        icon={<Refresh />}
                        label="Refresh status"
                        onClick={onRefresh}
                        className="border-b border-gray-200"
                    />
                )}
                {showReconnect && (
                    <ActionMenuItem
                        icon={<PlugIcon />}
                        label={
                            currentStatus === '404' ? 'Connect' : 'Reconnect'
                        }
                        onClick={onReconnect}
                    />
                )}
                {showConfigure && (
                    <ActionMenuItem
                        icon={<SettingsIcon />}
                        label="Configure"
                        onClick={onConfigure}
                    />
                )}
            </div>
        </>
    );
};

const IntegrationsPane: FC = () => {
    const [isMenuOpen, setIsMenuOpen] = useState(false);
    const {
        status,
        checkGithubInstallationStatus,
        installationId,
        target_name,
        installation_type,
    } = useGithubInstallation();

    useEffect(() => {
        checkGithubInstallationStatus();
    }, [checkGithubInstallationStatus]);

    const handleRefreshStatus = useCallback(() => {
        checkGithubInstallationStatus();
        setIsMenuOpen(false);
    }, [checkGithubInstallationStatus]);

    const handleConnectGithub = useCallback(() => {
        const url = import.meta.env.VITE_GITHUB_INSTALL_URL;
        if (url) {
            localStorage.setItem(
                'github-connect-redirect-to-integration-tab',
                'true',
            );
            window.location.assign(url);
        }
    }, []);

    const handleConfigure = useCallback(() => {
        let url: string;
        if (installation_type === 'ORGANIZATION') {
            url = `https://github.com/organizations/${target_name}/settings/installations/${installationId}`;
        } else {
            url = `https://github.com/settings/installations/${installationId}`;
        }
        if (url) {
            window.open(url, '_blank');
        }
    }, [installationId, target_name, installation_type]);

    const currentStatus = status ?? 'PENDING';
    const statusConfig =
        STATUS_CONFIGS[currentStatus as keyof typeof STATUS_CONFIGS] ??
        STATUS_CONFIGS.PENDING;
    const {text, classes, menuConfig} = statusConfig;

    return (
        <div>
            <div className="w-full">
                <h2 className="my-[32px] text-[24px] font-semibold">
                    Your integrations
                </h2>

                <div className="w-full">
                    <div className="flex w-full text-[#999]">
                        <TableHeader label="Integration" className="flex-[1]" />
                        <TableHeader
                            label="Access"
                            className="hidden sm:flex flex-[2]"
                        />
                        <TableHeader label="Status" className="flex-[1]" />
                        <TableHeader label="" className="min-w-[56px]" />
                    </div>

                    <div className="flex w-full">
                        <div className="flex-[1] p-[16px] flex items-center border-b border-[#D9D9D9]">
                            <div className="flex items-center gap-3">
                                <img
                                    src={iconGithub}
                                    alt="Github"
                                    className="w-[24px] h-[24px] sm:w-[46px] sm:h-[46px] object-contain"
                                />
                                <span className="text-[#333] text-[16px] font-semibold">
                                    Github
                                </span>
                            </div>
                        </div>
                        <div className="hidden sm:flex flex-[2] p-[16px] items-start border-b border-[#D9D9D9]">
                            <p className="text-[#999] text-[16px] leading-[150%]">
                                Can access and modify repository data. Can
                                execute code and integrate with external
                                services and more
                            </p>
                        </div>
                        <div className="flex-[1] p-[16px] flex items-center border-b border-[#D9D9D9]">
                            <span
                                className={classes}
                                onClick={
                                    currentStatus === '404'
                                        ? handleConnectGithub
                                        : undefined
                                }>
                                {text}
                            </span>
                        </div>
                        <div className="min-w-[56px] p-[16px] flex items-center justify-center border-b border-[#D9D9D9] relative">
                            {menuConfig.showMenu &&
                            menuConfig.options.showConfigure &&
                            (!installation_type ||
                                !installationId ||
                                !target_name) ? null : (
                                <>
                                    <div
                                        className="w-[24px] h-[24px] object-contain cursor-pointer"
                                        onClick={() =>
                                            setIsMenuOpen(!isMenuOpen)
                                        }>
                                        <DotsHorizontalIcon
                                            width={24}
                                            height={24}
                                        />
                                    </div>
                                    <ActionMenu
                                        isOpen={isMenuOpen}
                                        onClose={() => setIsMenuOpen(false)}
                                        onRefresh={handleRefreshStatus}
                                        onReconnect={handleConnectGithub}
                                        onConfigure={handleConfigure}
                                        showConfigure={
                                            menuConfig.options.showConfigure
                                        }
                                        showRefresh={
                                            menuConfig.options.showRefresh
                                        }
                                        showReconnect={
                                            menuConfig.options.showReconnect
                                        }
                                        currentStatus={currentStatus}
                                    />
                                </>
                            )}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default IntegrationsPane;
