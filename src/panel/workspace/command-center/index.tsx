import {useCallback, useEffect, useState} from 'react';
import {
    <PERSON><PERSON>,
    DialogContent,
    DialogFooter,
    DialogDescription,
    DialogTitle,
    DialogHeader,
} from '@/components/ui/dialog';
import {useProjectContext} from '@/context/project-context';
import {CommandStatus} from './command-status';
import {ActionBuildCodebase} from './action-build-codebase';
import {ActionEditTechSpec} from './action-edit-tech-spec';
import {DetailDeepInThought} from './detail-deep-in-thought';
import {DetailHumanTasks} from './detail-human-tasks';
import {DetailDestination} from './detail-destination';
import {useLocation, useNavigate} from 'react-router-dom';
import {ProjectDocumentType} from '@/lib/entity-types';
import {ActionWriteTechSpec} from './action-write-techspec';
import {ThinkingLoader} from './thinking-loader';

import {ActionSaveChanges} from './action-save-changes';
import {ActionExitEditing} from './action-exit-edit';
import {DownloadPdfButton} from '../header/download-pdf-button';
import {MobileActionsMenu} from './mobile-actions-menu';
import {DetailSource} from './detail-source';
// import {ActionAddFeatureRefactor} from './action-add-feature-refactor';
import {ActionClosePR} from './action-close-pr';
import DotsHorizontalIcon from '../icons/dots-horizontal-rounded';
import {Popover, PopoverContent, PopoverTrigger} from '@/components/ui/popover';
import {ActionSync} from './action-sync';
import ActionDiscard, {shouldHideActionDiscard} from './action-discard';
import {ActionSyncTechSpec} from './action-sync-tech-spec';
import {DetailPR} from './detail-pr';
import {syncTechSpec} from '@/lib/backend';
import {shouldHide} from './action-close-pr';
import {isLegacyProject} from '@/lib/utils';
import Copy from '../icons/copy';
import {useToast} from '@/hooks/use-toast';
import {PlatformError} from '@/lib/platform-error';
import {ActionOnboardCode} from './action-onboard-code';
import ActionBlitzyBuild from './action-blitzy-build';

interface CommandCenterProps {
    isReadOnly?: boolean;
    isPromptValid?: boolean;
    handleSubmit?: () => Promise<boolean>;
    docType?: ProjectDocumentType;
    onApprove?: () => Promise<void>;
    onCancelEditing?: () => void;
    onSaveEdits?: () => Promise<void>;
    onStartEditing?: () => void;
    onDownloadPDF?: () => Promise<void>;
    isEditing?: boolean;
    isContentDirty?: boolean;
    isDocumentSubmitting?: boolean;
    setIsDocumentSubmitting?: React.Dispatch<React.SetStateAction<boolean>>;
    disableNavigationBlocker?: () => void;
}

export default function CommandCenter(props: CommandCenterProps) {
    const {
        isReadOnly,
        isPromptValid,
        handleSubmit,
        docType,
        isEditing,
        onStartEditing,
        onCancelEditing,
        onSaveEdits,
        onApprove,
        onDownloadPDF,
        isContentDirty,
        isDocumentSubmitting,
        setIsDocumentSubmitting,
        disableNavigationBlocker,
    } = props;
    const {project, projectState, promptType, refreshProjectState} =
        useProjectContext();
    const {technicalSpec, isTechSpecInSync} = projectState ?? {};
    const {jobType: techSpecType} = technicalSpec ?? {};
    const navigateTo = useNavigate();
    const [confirmContinue, setContinueConfirm] = useState(false);
    const [showDisabledDialog, setShowDisabledDialog] =
        useState<boolean>(false);
    const [isMenuOpen, setIsMenuOpen] = useState(false);
    const {pathname} = useLocation();
    const {toast} = useToast();

    useEffect(() => {
        if (isLegacyProject(project)) {
            setShowDisabledDialog(true);
        }
    }, [project]);

    const handleApprove = useCallback(async () => {
        if (project?.isDisabled) {
            setShowDisabledDialog(true);
        } else {
            await onApprove?.();
        }
    }, [onApprove, project?.isDisabled]);

    const handleSyncTechSpec = useCallback(async () => {
        if (project?.id) {
            if (project?.isDisabled) {
                setShowDisabledDialog(true);
            } else {
                // const {codeGeneration: {id: codeGenId} = {}} =
                //     projectState ?? {};
                if (!project.isDisabled) {
                    try {
                        await syncTechSpec(project.id);
                        if (refreshProjectState) {
                            await refreshProjectState({
                                skipTechSpecSyncCheck: true,
                            });
                        }
                    } catch (error: unknown) {
                        console.error(error);
                        if (
                            error instanceof PlatformError &&
                            error.code === 'HttpStatus409'
                        ) {
                            toast({
                                variant: 'default',
                                duration: 10000,
                                description: error.message,
                            });
                        } else {
                            toast({
                                variant: 'destructive',
                                duration: 10000,
                                description: 'Failed to sync tech spec.',
                            });
                        }
                    }
                }
            }
        }
    }, [
        project?.id,
        project?.isDisabled,
        // projectState,
        refreshProjectState,
        toast,
    ]);

    const handleSubmitTechSpec = useCallback(async (): Promise<boolean> => {
        if (project?.isDisabled) {
            setShowDisabledDialog(true);
            return false;
        } else {
            return handleSubmit ? handleSubmit() : false;
        }
    }, [handleSubmit, project?.isDisabled]);

    const handleStartEditing = useCallback(() => {
        if (project?.isDisabled) {
            setShowDisabledDialog(true);
        } else {
            onStartEditing?.();
        }
    }, [onStartEditing, project?.isDisabled]);

    if (!project) return null; // no command center displayed without a project

    const showDetailsSection = !(
        pathname?.includes('back-prop/new-product') ||
        pathname?.includes('back-prop/source-code')
    );

    const showMobileDetailsSection =
        pathname.includes('prompt') ||
        pathname.includes('tech-spec') ||
        !(
            pathname === '/workspace' ||
            pathname.includes('workspace/projects') ||
            pathname.includes('settings') ||
            pathname.includes('status') ||
            pathname.includes('delete') ||
            pathname?.includes('back-prop/new-product') ||
            pathname?.includes('back-prop/source-code') ||
            pathname?.includes('back-prop/connect-git')
        );

    return (
        <div
            className={`flex flex-col gap-[8px] w-full sm:p-[32px] ${!showMobileDetailsSection && 'border-b'} sm:border-b border-[#D9D9D9] relative`}>
            {/* row 1 */}
            <div className="hidden sm:block text-[16px]">
                <span
                    className="font-semibold cursor-pointer"
                    onClick={() => navigateTo('/workspace/projects')}>
                    Projects
                </span>
                <span className="font-semibold"> /</span>
            </div>

            <div className="flex flex-col gap-[24px] sm:gap-[48px] w-full">
                {/* action row */}
                <div className="w-full flex flex-col sm:flex-row gap-[12px] items-stretch sm:items-center sm:justify-between">
                    <div className="w-full sm:w-auto flex flex-col sm:flex-row items-start sm:items-center gap-0 sm:gap-[12px] relative">
                        <div className="w-full flex items-start sm:items-center flex-col sm:flex-row gap-0 sm:gap-[8px] justify-between">
                            <div className="w-full flex flex-row shrink-0 items-center sm:items-center gap-[8px] sm:gap-[12px] px-[24px] pt-[24px] sm:p-0 ">
                                <div
                                    className="text-[24px] sm:text-[32px] font-semibold leading-[31.2px] sm:leading-[41.6px] tracking-[0.4px] text-left line-clamp-1"
                                    title={project.name}>
                                    {project.name}
                                </div>
                                <CommandStatus
                                    projectType={project.type}
                                    projectState={projectState}
                                    promptType={promptType}
                                    isLegacyProject={isLegacyProject(project)}
                                />
                            </div>
                            <div className="w-full flex justify-between items-center">
                                <MobileActionsMenu
                                    jobStatus={projectState}
                                    projectId={project.id}
                                    projectType={project.type}
                                    isEditing={isEditing}
                                    isLegacyProject={isLegacyProject(project)}
                                    docType={docType}
                                    onApprove={handleApprove}
                                    onStartEditing={handleStartEditing}
                                    onCancelEditing={onCancelEditing}
                                    onSaveEdits={onSaveEdits}
                                    isContentDirty={isContentDirty}
                                    isDocumentSubmitting={isDocumentSubmitting}
                                    setIsDocumentSubmitting={
                                        setIsDocumentSubmitting
                                    }
                                    onDownloadPDF={onDownloadPDF}
                                    isPromptValid={isPromptValid}
                                    handleSubmit={handleSubmitTechSpec}
                                    confirmContinue={confirmContinue}
                                    setContinueConfirm={setContinueConfirm}
                                    promptType={promptType}
                                    disableNavigationBlocker={
                                        disableNavigationBlocker
                                    }
                                    handleSyncTechSpec={handleSyncTechSpec}
                                />
                                <div className="block sm:hidden pt-[24px]">
                                    {/* Delete this below div & loader when new loader is added */}
                                    <div
                                        className={
                                            projectState?.technicalSpec
                                                ?.status === 'IN_PROGRESS'
                                                ? 'px-[24px] pb-[24px]'
                                                : ''
                                        }>
                                        <ThinkingLoader
                                            jobStatus={projectState}
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div className="w-full sm:w-auto hidden sm:flex flex-col sm:flex-row items-center gap-[12px]">
                        <ActionBuildCodebase
                            projectId={project.id}
                            projectType={project.type}
                            projectState={projectState}
                            isLegacyProject={isLegacyProject(project)}
                            isEditing={isEditing ?? false}
                            docType={docType}
                            onApprove={handleApprove}
                            setIsDocumentSubmitting={setIsDocumentSubmitting}
                            isDocumentSubmitting={isDocumentSubmitting}
                        />

                        <ActionWriteTechSpec
                            projectId={project.id}
                            projectType={project.type}
                            projectState={projectState}
                            isLegacyProject={isLegacyProject(project)}
                            jobStatus={projectState}
                            isPromptValid={isPromptValid}
                            isReadOnly={isReadOnly}
                            handleSubmit={handleSubmitTechSpec}
                            confirmContinue={confirmContinue}
                            setContinueConfirm={setContinueConfirm}
                        />

                        <ActionOnboardCode
                            projectId={project.id}
                            projectType={project.type}
                            projectState={projectState}
                            disableNavigationBlocker={disableNavigationBlocker}
                        />

                        <ActionSyncTechSpec
                            handleSync={handleSyncTechSpec}
                            projectState={projectState}
                            projectId={project?.id}
                        />
                        {/* <ActionAddFeatureRefactor
                            key="add-feature"
                            action="ADD_FEATURE"
                            projectId={project?.id}
                            projectState={projectState}
                            isEditing={isEditing ?? false}
                        />
                        <ActionAddFeatureRefactor
                            key="prepare-refactor-codebase"
                            action="REFACTOR_CODE"
                            projectId={project?.id}
                            projectState={projectState}
                            isEditing={isEditing ?? false}
                        /> */}
                        {/* Build button replacing commented action */}
                        <ActionBlitzyBuild
                            projectId={project?.id}
                            projectState={projectState}
                            isEditing={isEditing ?? false}
                        />

                        <ActionEditTechSpec
                            projectState={projectState}
                            isEditing={isEditing}
                            projectId={project?.id}
                            onStartEditing={handleStartEditing}
                        />
                        <ActionSaveChanges
                            jobStatus={projectState}
                            isEditing={isEditing}
                            projectId={project?.id}
                            onSaveEdits={onSaveEdits}
                            isContentDirty={isContentDirty}
                        />
                        <ActionExitEditing
                            jobStatus={projectState}
                            isEditing={isEditing}
                            projectId={project?.id}
                            onCancelEditing={onCancelEditing}
                        />

                        <ActionClosePR
                            key="merge-pr"
                            action="MERGE"
                            projectId={project?.id}
                            projectState={projectState}
                            jobStatus={projectState}
                        />
                        {!promptType &&
                            projectState?.codeGeneration?.status !== 'QUEUED' &&
                            (projectState?.technicalSpec?.status ===
                                'SUBMITTED' ||
                                projectState?.technicalSpec?.status ===
                                    'DONE') &&
                            !isEditing && <ActionSync />}

                        <ThinkingLoader jobStatus={projectState} />

                        {(projectState?.technicalSpec?.status === 'SUBMITTED' ||
                            projectState?.technicalSpec?.status === 'DONE') &&
                            !isEditing &&
                            onDownloadPDF && (
                                <DownloadPdfButton
                                    onDownloadPDF={onDownloadPDF}
                                    projectId={project?.id}
                                />
                            )}

                        {((techSpecType !== 'EXISTING_PRODUCT' &&
                            projectState?.technicalSpec?.status === 'DONE') ||
                            (projectState?.codeGeneration?.status === 'DONE' &&
                                projectState?.codeGeneration?.commitStatus !==
                                    'MERGED' &&
                                techSpecType !== 'NEW_PRODUCT') ||
                            (techSpecType === 'EXISTING_PRODUCT' &&
                                projectState?.technicalSpec?.status ==
                                    'DONE')) &&
                            !isEditing &&
                            isTechSpecInSync &&
                            (!shouldHide(projectState) ||
                                !shouldHideActionDiscard(projectState)) && (
                                <Popover
                                    open={isMenuOpen}
                                    onOpenChange={setIsMenuOpen}>
                                    <PopoverTrigger asChild>
                                        <div className="h-[40px] w-[40px] p-[8px] flex items-center justify-center border-[#D9D9D9] cursor-pointer">
                                            <DotsHorizontalIcon
                                                isActive={isMenuOpen}
                                                fill="#5B39F3"
                                                width={24}
                                                height={24}
                                                addTooltip={true}
                                                tooltipText="More options"
                                            />
                                        </div>
                                    </PopoverTrigger>
                                    <PopoverContent
                                        className={`mt-[8px] mr-[12px] transition-transform duration-600 
                                ${isMenuOpen ? 'translate-x-0' : '-translate-x-8 opacity-0'}
                                rounded-[16px] border-[0.5px] border-[#D9D9D9] bg-white
                                shadow-[0px_-9px_50px_0px_rgba(0,0,0,0.06),0px_374px_105px_0px_rgba(130,130,130,0.00),0px_239px_96px_0px_rgba(130,130,130,0.01),0px_135px_81px_0px_rgba(130,130,130,0.05),0px_60px_60px_0px_rgba(130,130,130,0.09),0px_15px_33px_0px_rgba(130,130,130,0.10)]`}>
                                        <div className="w-[264px] flex flex-col bg-[#D9D9D9] rounded-[16px] overflow-hidden">
                                            <div className=" flex gap-[8px] bg-[#FFF] hover:bg-[#F2F0FE] items-center cursor-pointer">
                                                <ActionClosePR
                                                    key="close-pr"
                                                    action="CLOSE"
                                                    projectId={project?.id}
                                                    projectState={projectState}
                                                    jobStatus={projectState}
                                                    inPopup={true}
                                                    setIsMenuOpen={
                                                        setIsMenuOpen
                                                    }
                                                />
                                            </div>
                                            <ActionDiscard
                                                projectId={project.id}
                                                projectState={projectState}
                                                setIsMenuOpen={setIsMenuOpen}
                                            />
                                        </div>
                                    </PopoverContent>
                                </Popover>
                            )}
                    </div>
                </div>

                {/* details row */}
                {showDetailsSection && (
                    <div className="hidden sm:flex w-full flex-col sm:flex-row gap-[48px]">
                        <DetailSource
                            project={project}
                            projectState={projectState}
                        />
                        <DetailDestination
                            project={project}
                            projectState={projectState}
                        />
                        <DetailPR
                            projectType={project.type}
                            projectState={projectState}
                        />
                        <DetailHumanTasks
                            projectType={project.type}
                            jobStatus={projectState}
                        />
                        <DetailDeepInThought jobStatus={projectState} />
                    </div>
                )}
                {showDisabledDialog && (
                    <Dialog
                        open={showDisabledDialog}
                        onOpenChange={open => {
                            if (!open) setShowDisabledDialog?.(false);
                        }}>
                        <DialogContent className="min-h-dvh sm:min-h-0 w-full sm:w-[608px] sm:h-[439px] sm:max-w-none sm:pt-[1.5rem] [&>button]:hidden">
                            <DialogHeader className="flex-grow">
                                <DialogTitle>Create a new project</DialogTitle>
                                <DialogDescription>
                                    This is a legacy project created using an
                                    older version of the system and has limited
                                    functionality.
                                    <br />
                                    <br />
                                    To access all features and improvements,
                                    please create a new project.
                                    <div className=" h-[1px] bg-[#D9D9D9] my-[24px]"></div>
                                    <span className="font-bold">
                                        Need to retrieve data from this project?
                                    </span>
                                    <br />
                                    Copy the Project ID below and email us at{' '}
                                    {''}
                                    <a href="mailto:<EMAIL>">
                                        <EMAIL>
                                    </a>
                                    {/* add a placeholder here with the project id and a copy button when click on copy id should be copied to the clipboard */}
                                </DialogDescription>
                                <div className="mt-4 flex items-center justify-between text-[16px] text-[#333333] border border-[#E5E5E5] bg-[#F5F5F5] rounded-[24px] px-[16px] py-[8px]">
                                    <span>{project?.id}</span>
                                    <div
                                        role="button"
                                        onClick={() => {
                                            navigator.clipboard.writeText(
                                                project?.id || '',
                                            );
                                            toast({
                                                description:
                                                    'Project ID copied to clipboard',
                                                duration: 2000,
                                            });
                                        }}
                                        title="Copy project ID">
                                        <Copy />
                                    </div>
                                </div>
                            </DialogHeader>
                            <DialogFooter className="gap-[16px] sm:gap-0">
                                <div
                                    role="button"
                                    className="secondary-button"
                                    onClick={() => {
                                        setShowDisabledDialog?.(false);
                                    }}>
                                    Got it
                                </div>
                            </DialogFooter>
                        </DialogContent>
                    </Dialog>
                )}
            </div>
        </div>
    );
}
