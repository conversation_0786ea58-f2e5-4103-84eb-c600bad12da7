import {ProjectDetail, ProjectState} from '@/lib/entity-types';
import {useSearchParams} from 'react-router-dom';
import {useCommitStatus} from '../hooks/commit-status-hook';

interface IProps {
    projectState?: ProjectState | undefined;
    project?: ProjectDetail;
}

function shouldHide(
    projectState: ProjectState | undefined,
    project?: ProjectDetail,
    codeStatus?: string,
) {
    // More robust extraction of jobType
    const jobType = projectState?.technicalSpec && typeof projectState.technicalSpec === 'object'
        ? (projectState.technicalSpec as any).jobType
        : undefined;
    if (
        project?.type === 'EXISTING_PRODUCT' &&
        jobType !== 'REFACTOR_CODE' &&
        jobType !== 'CUSTOM' 
    )
        return true;
    if ((jobType === 'REFACTOR_CODE' || jobType === 'CUSTOM') && codeStatus !== 'DONE') {
        // We should hide it since job is not done yet, user not selected destination yet.
        return true;
    }
    return (
        jobType === 'ADD_FEATURE' ||
        jobType === 'FIX_BUGS' ||
        jobType === 'FIX_CVES' ||
        jobType === 'ADD_TESTING' ||
        jobType === 'DOCUMENT_CODE'
    );
}

export function DetailDestination({project, projectState}: IProps) {
    const [searchParams] = useSearchParams();
    const action = searchParams.get('action');
    const {gitSink} = project ?? {};
    const {codeGeneration} = projectState ?? {};
    const {status: codeStatus, id: codeGenId} = codeGeneration ?? {};
    const {status} = useCommitStatus(project?.id ?? '', codeGenId ?? '');
    if (
        gitSink === undefined ||
        action === 'FIX_BUGS' ||
        action === 'FIX_CVES' ||
        action === 'ADD_FEATURE'||
        action === 'ADD_TESTING' ||
        action === 'DOCUMENT_CODE' ||
         ((action === 'REFACTOR_CODE' || action === 'CUSTOM') && codeStatus !== 'DONE') ||
        (action !== 'REFACTOR_CODE' &&
            action !== 'CUSTOM' &&
            shouldHide(projectState, project,codeStatus))
    )
        return null;

    let branchName: string | null = null;
    if (gitSink?.branchName === '' && projectState?.codeGeneration?.id) {
        branchName = 'blitzy-' + projectState.codeGeneration.id;
    }

    return (
        <div>
            <h3 className="text-[16px] font-semibold">Destination</h3>
            <div
                className={`text-[16px] ${codeStatus !== 'DONE' ? 'text-[#333333] ' : ''}`}>
                {codeStatus === 'DONE' ? (
                    gitSink?.branchName ? (
                        <a
                            className="text-[16px]"
                            href={`https://github.com/${gitSink?.orgName}/${gitSink?.repoName}/tree/${gitSink?.branchName}`}
                            target="_blank">
                            {`${gitSink?.orgName ?? '-'}/${gitSink?.repoName ?? '-'}/${gitSink?.branchName ?? '-'}`}{' '}
                            ↗
                        </a>
                    ) : (
                        <a
                            className="text-[16px]"
                            href={status?.blitzyBranchUrl}
                            target="_blank">
                            {status?.blitzyBranchUrl?.replace(
                                'https://github.com/',
                                ' ',
                            )}{' '}
                            ↗
                        </a>
                    )
                ) : (
                    <span>
                        {`${gitSink?.orgName ?? '-'}/${gitSink?.repoName ?? '-'}/${branchName ?? gitSink?.branchName ?? '-'}`}
                    </span>
                )}
            </div>
        </div>
    );
}
