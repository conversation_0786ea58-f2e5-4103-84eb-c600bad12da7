import {
    ProjectState,
    ProjectDocumentType,
    PromptType,
} from '@/lib/entity-types';
import {ActionBuildCodebase} from './action-build-codebase';
import {ActionWriteTechSpec} from './action-write-techspec';
import {DownloadPdfButton} from '../header/download-pdf-button';
// import {ActionAddFeatureRefactor} from './action-add-feature-refactor';
import {ActionClosePR} from './action-close-pr';
import {ActionSync} from './action-sync';
import ActionDiscard from './action-discard';
import {ProjectType} from '@/lib/entity-types';
import {ActionEditTechSpec} from './action-edit-tech-spec';
import {ActionSaveChanges} from './action-save-changes';
import {ActionExitEditing} from './action-exit-edit';
import {ActionOnboardCode} from './action-onboard-code';
import ActionBlitzyBuild from './action-blitzy-build';
import {ActionSyncTechSpec} from './action-sync-tech-spec';

interface MobileActionsMenuProps {
    jobStatus: ProjectState | undefined;
    projectId: string;
    projectType: string;
    isEditing?: boolean;
    isLegacyProject: boolean;
    docType?: ProjectDocumentType;
    onApprove?: () => void;
    onStartEditing?: () => void;
    onCancelEditing?: () => void;
    onSaveEdits?: () => void;
    isContentDirty?: boolean;
    isDocumentSubmitting?: boolean;
    setIsDocumentSubmitting?: React.Dispatch<React.SetStateAction<boolean>>;
    onDownloadPDF?: () => void;
    isPromptValid?: boolean;
    handleSubmit?: () => Promise<boolean>;
    confirmContinue?: boolean;
    setContinueConfirm?: (confirmContinue: boolean) => void;
    promptType?: PromptType;
    disableNavigationBlocker?: () => void;
    handleSyncTechSpec: () => void;
}

export function MobileActionsMenu(props: MobileActionsMenuProps) {
    if (
        location.pathname.includes('back-prop') ||
        props.jobStatus?.technicalSpec?.status === 'QUEUED' ||
        props.jobStatus?.technicalSpec?.status === 'IN_PROGRESS'
    ) {
        return null;
    }

    // const techSpecType = props.jobStatus?.technicalSpec?.type ?? 'NEW_PRODUCT';

    return (
        <div className=" flex flex-nowrap gap-[8px] px-[24px] py-[24px] pr-[24px] sm:hidden w-full overflow-x-auto [-ms-overflow-style:none] [scrollbar-width:none] [&::-webkit-scrollbar]:hidden">
            <ActionBuildCodebase
                projectId={props.projectId}
                projectType={props.projectType as ProjectType}
                projectState={props.jobStatus}
                isEditing={props.isEditing}
                isLegacyProject={props.isLegacyProject}
                docType={props.docType}
                onApprove={props.onApprove}
                setIsDocumentSubmitting={props.setIsDocumentSubmitting}
                isDocumentSubmitting={props.isDocumentSubmitting}
            />

            <ActionSyncTechSpec
                handleSync={props.handleSyncTechSpec}
                projectState={props.jobStatus}
                projectId={props.projectId}
            />

            {/* <ActionAddFeatureRefactor
                key="add-feature"
                action="ADD_FEATURE"
                projectId={props.projectId}
                projectState={props.jobStatus}
                isEditing={props.isEditing ?? false}
            />

            <ActionAddFeatureRefactor
                key="prepare-refactor-codebase"
                action="REFACTOR_CODE"
                projectId={props.projectId}
                projectState={props.jobStatus}
                isEditing={props.isEditing ?? false}
            /> */}
            {/* Build button replacing commented action */}
            <ActionBlitzyBuild
                projectId={props.projectId}
                projectState={props.jobStatus}
                isEditing={props.isEditing ?? false}
            />

            <ActionEditTechSpec
                projectState={props.jobStatus}
                onStartEditing={props.onStartEditing}
                isEditing={props.isEditing}
                isMobile={true}
                projectId={props.projectId}
            />

            <ActionSaveChanges
                jobStatus={props.jobStatus}
                isEditing={props.isEditing}
                onSaveEdits={props.onSaveEdits}
                isContentDirty={props.isContentDirty}
                projectId={props.projectId}
            />

            <ActionExitEditing
                jobStatus={props.jobStatus}
                isEditing={props.isEditing}
                onCancelEditing={props.onCancelEditing}
                projectId={props.projectId}
            />

            <ActionWriteTechSpec
                projectId={props.projectId}
                projectType={props.projectType as ProjectType}
                jobStatus={props.jobStatus}
                isLegacyProject={props.isLegacyProject}
                isPromptValid={props.isPromptValid}
                isReadOnly={false}
                handleSubmit={props.handleSubmit}
                confirmContinue={props.confirmContinue}
                setContinueConfirm={props.setContinueConfirm}
            />

            <ActionOnboardCode
                projectId={props.projectId}
                projectType={props.projectType as ProjectType}
                projectState={props.jobStatus}
                disableNavigationBlocker={props.disableNavigationBlocker}
            />

            <ActionClosePR
                key="merge-pr"
                action="MERGE"
                projectId={props.projectId}
                projectState={props.jobStatus}
                jobStatus={props.jobStatus}
            />

            {!props.promptType &&
                props.jobStatus?.codeGeneration?.status !== 'QUEUED' &&
                (props.jobStatus?.technicalSpec?.status === 'SUBMITTED' ||
                    props.jobStatus?.technicalSpec?.status === 'DONE') &&
                !props.isEditing && <ActionSync isMobile={true} />}

            {(props.jobStatus?.technicalSpec?.status === 'DONE' ||
                props.jobStatus?.technicalSpec?.status === 'SUBMITTED') &&
                !props.isEditing &&
                props.onDownloadPDF && (
                    <DownloadPdfButton
                        isMobile={true}
                        onDownloadPDF={props.onDownloadPDF}
                        projectId={props.projectId}
                    />
                )}

            {(props.jobStatus?.technicalSpec?.status === 'DONE' ||
                props.jobStatus?.technicalSpec?.status === 'SUBMITTED') &&
                !props.isEditing && (
                    <ActionClosePR
                        key="close-pr"
                        action="CLOSE"
                        projectId={props.projectId}
                        projectState={props.jobStatus}
                        isMobile={true}
                    />
                )}

            {(props.jobStatus?.technicalSpec?.status === 'DONE' ||
                props.jobStatus?.technicalSpec?.status === 'SUBMITTED') &&
                !props.isEditing && (
                    <ActionDiscard
                        projectId={props.projectId}
                        projectState={props.jobStatus}
                        isMobile={true}
                    />
                )}
        </div>
    );
}
