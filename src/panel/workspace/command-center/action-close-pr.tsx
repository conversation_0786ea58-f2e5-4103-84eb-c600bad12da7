import {useCallback, useState} from 'react';
import {ProjectState} from '@/lib/entity-types';
import GitMerge from '../icons/git-merge';
import {actOnGitHubPR} from '@/lib/backend';
import PRClose from '../icons/pr-close';
import {useProjectContext} from '@/context/project-context';
import {LoadingSubmitButton} from '@/components/custom/loading-submit-button';
import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogDescription,
    DialogFooter,
} from '@/components/ui/dialog';
import {useCommitStatus} from '../hooks/commit-status-hook';
import {PlatformError} from '@/lib/platform-error';
import {logGAEvent} from '@/lib/utils';
import {useNavigate} from 'react-router-dom';

interface IProps {
    readonly projectId: string;
    readonly projectState?: ProjectState;
    readonly action: 'MERGE' | 'CLOSE';
    readonly isMobile?: boolean;
    readonly jobStatus?: ProjectState | undefined;
    readonly inPopup?: boolean;
    setIsMenuOpen?: (isMenuOpen: boolean) => void;
}

export function shouldHide(projectState: ProjectState | undefined) {
    const {isTechSpecInSync, codeGeneration: {status, commitStatus} = {}} =
        projectState ?? {};
    const techSpecStatus = projectState?.technicalSpec?.status;
    return (
        !isTechSpecInSync ||
        techSpecStatus !== 'DONE' ||
        commitStatus !== 'PENDING' ||
        status !== 'DONE'
    );
}

export function ActionClosePR({
    projectId,
    projectState,
    action,
    isMobile,
    inPopup = false,
    setIsMenuOpen,
}: IProps) {
    const {project, refreshProjectState, refreshProject} = useProjectContext();
    const [isLoading, setIsLoading] = useState(false);
    const [showDisabledDialog, setShowDisabledDialog] = useState(false);
    const [isClosePRDialogOpen, setIsClosePRDialogOpen] = useState(false);
    const {status: commitStatus} = useCommitStatus(
        project?.id ?? '',
        projectState?.codeGeneration?.id ?? '',
    );
    const navigateTo = useNavigate();

    const handleClosePR = useCallback(async () => {
        logGAEvent(`pr_${action.toLowerCase()}_clicked`, {
            event_category: 'Project',
            event_label: projectState?.technicalSpec?.jobType || 'unknown',
            project_id: projectId,
            code_gen_id: projectState?.codeGeneration?.id,
            action: action,
        });
        try {
            setIsLoading(true);
            const {codeGeneration: {id: codeGenId} = {}} = projectState ?? {};
            if (codeGenId) {
                await actOnGitHubPR(projectId, codeGenId, action);
                if (refreshProject) {
                    const proj = await refreshProject();
                    if (refreshProjectState) {
                        refreshProjectState({
                            project: proj,
                            skipTechSpecSyncCheck: true,
                        });
                    }
                }
                if (project?.type === 'NEW_PRODUCT') {
                    navigateTo(`/workspace/project/${projectId}/status`, {
                        replace: true,
                    });
                }
            }
        } catch (error) {
            if (
                error instanceof PlatformError &&
                error.code === 'HttpStatus409'
            ) {
                setShowDisabledDialog(true);
            } else {
                console.error('Unexpected error:', error);
            }
        } finally {
            setIsLoading(false);
            setIsClosePRDialogOpen(false);
            setIsMenuOpen?.(false);
        }
    }, [
        action,
        projectId,
        projectState,
        refreshProjectState,
        refreshProject,
        setIsMenuOpen,
        navigateTo,
        project,
    ]);

    const handleButtonClick = useCallback(() => {
        if (action === 'CLOSE') {
            setIsClosePRDialogOpen(true);
        } else {
            handleClosePR();
        }
    }, [action, handleClosePR]);

    if (shouldHide(projectState)) return null;

    return (
        <>
            <div
                className={`flex-none ${inPopup && action === 'CLOSE' ? 'w-full' : ''}`}>
                {!inPopup ? (
                    <LoadingSubmitButton
                        loading={isLoading}
                        loadingText={`${action === 'MERGE' ? 'Merging' : 'Closing'} PR`}
                        onClick={handleButtonClick}
                        className={`${
                            action === 'MERGE'
                                ? 'primary-button'
                                : 'leading-[24px] font-semibold text-[16px] gap-[16px] text-[#EC3636]'
                        } flex justify-center items-center gap-[8px] ${
                            isMobile
                                ? 'secondary-button border-[#EC3636] border-[1px]'
                                : ''
                        }`}>
                        <span className="flex flex-row justify-center items-center gap-[8px]">
                            {action === 'MERGE' ? <GitMerge /> : <PRClose />}
                            <div
                                className={`${action === 'MERGE' ? 'px-[0px]' : isMobile ? 'px-[0px]' : 'px-[16px]'}`}>
                                {action === 'MERGE' ? 'Merge' : 'Close'} PR
                            </div>
                        </span>
                    </LoadingSubmitButton>
                ) : (
                    <div
                        role="button"
                        onClick={() => setIsClosePRDialogOpen(true)}
                        className={`w-full px-[16px] py-[12px] leading-[24px] font-semibold text-[16px] text-[#EC3636] flex ${
                            isMobile
                                ? 'secondary-button border-[#EC3636] border-[1px]'
                                : ''
                        }`}>
                        <span className="flex flex-row justify-center items-center gap-[16px]">
                            {action === 'MERGE' ? <GitMerge /> : <PRClose />}
                            <div
                                className={`${action === 'MERGE' ? 'px-[0px]' : isMobile ? 'px-[0px]' : 'px-[0px]'}`}>
                                {action === 'MERGE' ? 'Merge' : 'Close'} PR
                            </div>
                        </span>
                    </div>
                )}
            </div>

            <Dialog
                open={showDisabledDialog}
                onOpenChange={open => {
                    if (!open) setShowDisabledDialog(false);
                }}>
                <DialogContent className="min-h-dvh sm:min-h-0 w-full sm:max-w-md sm:pt-[1.5rem] [&>button]:hidden">
                    <DialogHeader className="flex-grow">
                        <DialogTitle>Pull request has conflicts</DialogTitle>
                        <DialogDescription>
                            This pull request can't be merged automatically due
                            to conflicts. Review the changes and resolve any
                            conflicts before trying again.
                            <div className="mt-4">
                                <h3 className="text-[16px] font-semibold">
                                    Blitzy AI PR
                                </h3>
                                <div className="text-[16px] text-[#5B39F3]">
                                    <a
                                        className="text-[16px]"
                                        href={
                                            commitStatus?.status === 'MERGED'
                                                ? commitStatus?.blitzyCommitUrl
                                                : commitStatus?.prLink
                                        }
                                        target="_blank">
                                        {commitStatus?.blitzyCommitHash?.substring(
                                            0,
                                            5,
                                        )}{' '}
                                        ↗
                                    </a>
                                </div>
                            </div>
                        </DialogDescription>
                    </DialogHeader>
                    <DialogFooter className="gap-[16px] sm:gap-0">
                        <div
                            role="button"
                            className="inline-flex font-semibold text-[16px] h-[40px] px-[20px] py-[8px] justify-center items-center gap-[8px] rounded-[32px] bg-[#F5F5F5]"
                            onClick={() => {
                                setShowDisabledDialog(false);
                            }}>
                            Got it
                        </div>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
            {action === 'CLOSE' && (
                <Dialog open={isClosePRDialogOpen}>
                    <DialogContent className="min-h-dvh sm:min-h-0 w-full p-[24px] gap-[48px] sm:max-w-[512px] sm:rounded-[24px] bg-white [&>button]:hidden">
                        <DialogHeader className="flex-grow gap-[12px]">
                            <DialogTitle className="text-[24px] font-semibold">
                                Close PR and revert tech spec
                            </DialogTitle>
                            <DialogDescription className="text-[16px]">
                                This will discard all proposed changes and
                                revert the technical spec to its previous state.
                                This action cannot be undone.
                            </DialogDescription>
                        </DialogHeader>
                        <DialogFooter className="gap-[12px]">
                            <div
                                role="button"
                                className="inline-flex font-semibold text-[16px] h-[40px] px-[20px] py-[8px] justify-center items-center gap-[8px] rounded-[32px] bg-[#F5F5F5]"
                                onClick={() => {
                                    setIsClosePRDialogOpen(false);
                                    setIsMenuOpen?.(false);
                                }}>
                                <span className="text-semibold">Cancel</span>
                            </div>
                            <LoadingSubmitButton
                                className="secondary-button text-[#EC3636] border-[#EC3636] hover:border-[#EC3636]"
                                loading={isLoading}
                                loadingText="Closing PR"
                                onClick={handleClosePR}>
                                <span className="flex items-center gap-[8px]">
                                    <PRClose
                                        width={20}
                                        height={19}
                                        color="#EC3636"
                                    />
                                    <span>Close PR</span>
                                </span>
                            </LoadingSubmitButton>
                        </DialogFooter>
                    </DialogContent>
                </Dialog>
            )}
        </>
    );
}
