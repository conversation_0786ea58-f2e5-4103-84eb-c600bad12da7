import {ProjectDetail, ProjectState} from '@/lib/entity-types';
import {useSearchParams} from 'react-router-dom';

interface IProps {
    project?: ProjectDetail;
    projectState?: ProjectState;
}

function shouldHide(project?: ProjectDetail, projectState?: ProjectState) {
    const techSpecType = projectState?.technicalSpec?.jobType;
    if (
        project?.type === 'NEW_PRODUCT' &&
        (!techSpecType || techSpecType === 'NEW_PRODUCT')
    )
        return true;
    if (!project?.gitSource) return true;
    return false;
}

export function DetailSource({project, projectState}: IProps) {
    const [searchParams] = useSearchParams();
    const {technicalSpec} = projectState ?? {};
    const {jobType: techSpecType} = technicalSpec ?? {};

    if (shouldHide(project, projectState)) return null;

    const gitSource = project?.gitSource;
    // const gitDestination = project?.gitSink;
    // const sameRepo =
    //     gitSource?.orgName === gitDestination?.orgName &&
    //     gitSource?.repoName === gitDestination?.repoName;

    return (
        <div>
            <h3 className="text-[16px] font-semibold">
                {techSpecType === 'ADD_FEATURE' ||
                techSpecType === 'FIX_BUGS' ||
                techSpecType === 'FIX_CVES' ||
                techSpecType === 'ADD_TESTING' ||
                techSpecType === 'DOCUMENT_CODE' ||
                searchParams.get('action') === 'ADD_FEATURE' ||
                searchParams.get('action') === 'FIX_BUGS' ||
                searchParams.get('action') === 'FIX_CVES' ||
                searchParams.get('action') === 'ADD_TESTING' ||
                searchParams.get('action') === 'DOCUMENT_CODE'
                    ? 'Source + Destination'
                    : 'Source'}
            </h3>
            <a
                href={`https://github.com/${gitSource?.orgName}/${gitSource?.repoName}/tree/${gitSource?.branchName}`}
                target="_blank"
                rel="noopener noreferrer"
                className="text-[16px] hover:underline">
                {`${gitSource?.orgName ?? '-'}/${gitSource?.repoName ?? '-'}/${gitSource?.branchName ?? '-'}`}{' '}
                ↗
            </a>
        </div>
    );
}
