import {useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {useNavigate} from 'react-router-dom';
import {MarkdownPane} from './markdown-pane';
import {useProjectContext} from '@/context/project-context';
import {IEditor} from './markdown-editor';
import {PendingStates, ProjectDocumentType} from '@/lib/entity-types';
import {useProjectDialog} from './hooks/project-dialog-hook';
import {ProjectDialog} from '@/modal/project-dialog';
import {ProjectPanelContent} from './project-panel-content';
import {
    documentStatus,
    currentSectionIndex,
    totalSteps,
    logGAEvent,
    isLegacyProject,
} from '@/lib/utils';
import {useAlertContext} from '@/context/alert-context';
import {downloadTechSpecPDF} from '@/lib/backend';
import {toast} from '@/hooks/use-toast';
import {NavigationBlocker} from '@/components/custom/navigation-blocker';
import {DocumentLoadingFallback} from './document-loading-fallback';

interface Props {
    docType: ProjectDocumentType;
}

function ProjectDocumentPanelContent({docType}: Props) {
    const [isEditing, setEditing] = useState(false);
    const [isContentDirty, setContentDirty] = useState(false);
    const [isDocumentSubmitting, setIsDocumentSubmitting] =
        useState<boolean>(false);
    const [markdown, setMarkdown] = useState<string>();
    const [isMarkdownLoading, setIsMarkdownLoading] = useState<boolean>(false);
    const {showAlert} = useAlertContext();
    const navigateTo = useNavigate();
    const editorRef = useRef<IEditor>(null);

    const {
        project,
        projectState,
        retrieveDocument,
        approveDocument,
        updateDocument,
        isTechSpecSubmitting,
        setTechSpecSubmitting,
        refreshProjectState,
        pdfStatus,
        getPDFDownloadStatus,
        setPDFStatus,
        isFetchingProject,
        isFetchingProjectState,
    } = useProjectContext();

    const {dialogType, checkProjectDocumentDialog, closeDialog} =
        useProjectDialog();

    const handleEditorContentChange = useCallback((isDirty: boolean) => {
        setContentDirty(isDirty);
    }, []);

    const checkProjectDocumentDialogCallback = useCallback(() => {
        checkProjectDocumentDialog(
            projectState,
            docType,
            isTechSpecSubmitting,
            projectState?.technicalSpec?.jobMetadata?.currentIndex,
        );
    }, [
        checkProjectDocumentDialog,
        docType,
        projectState,
        isTechSpecSubmitting,
    ]);

    useEffect(() => {
        // If the tech spec is not available, navigate to the status page
        if (project && projectState && !projectState.technicalSpec) {
            navigateTo(`/workspace/project/${project.id}/status`, {
                replace: true,
            });
        }
        if (
            project &&
            projectState &&
            projectState?.softwareRequirement?.status === 'DONE' &&
            projectState?.technicalSpec?.status === 'TODO'
        ) {
            navigateTo('/workspace/projects', {replace: true});
            showAlert?.({
                type: 'PlatformError',
                message: 'Project unavailable',
                children: (
                    <div>
                        <p className="mb-[20px] text-[14px] text-[#333]">
                            Project {project?.name ? `'${project.name}'` : ''}{' '}
                            is no longer available. Please contact our support
                            team for assistance.
                        </p>
                        <a
                            href="mailto:<EMAIL>"
                            className="text-[14px]">
                            <EMAIL> ↗
                        </a>
                    </div>
                ),
                hideCloseIcon: true,
                buttonText: 'Got it',
            });
        }
    }, [
        navigateTo,
        project,
        projectState,
        showAlert,
        isFetchingProject,
        isFetchingProjectState,
    ]);

    useEffect(() => {
        checkProjectDocumentDialogCallback();
    }, [checkProjectDocumentDialogCallback]);

    const docStatus = useMemo(
        () => documentStatus(docType, projectState),
        [docType, projectState],
    );

    const sectionIndex = useMemo(
        () => currentSectionIndex(docType, projectState),
        [docType, projectState],
    );

    const totalSections = useMemo(
        () => totalSteps(docType, projectState),
        [docType, projectState],
    );

    const getDocumentContent = useCallback(async () => {
        if (retrieveDocument) {
            setIsMarkdownLoading(true);
            setMarkdown(await retrieveDocument(docType));
            setIsMarkdownLoading(false);
        }
    }, [docType, retrieveDocument]);

    // legacy project
    useEffect(() => {
        if (project && isLegacyProject(project)) {
            (async function () {
                let markdown: string | undefined = undefined;
                if (retrieveDocument) {
                    markdown = await retrieveDocument(docType);
                }
                if (markdown) {
                    setMarkdown(markdown);
                } else {
                    navigateTo(
                        `/workspace/project/${project.id}/prompt?action=NEW_PRODUCT`,
                        {replace: true},
                    );
                }
            })();
        }
    }, [docType, getDocumentContent, navigateTo, project, retrieveDocument]);

    useEffect(() => {
        if (
            ['DONE', 'SUBMITTED'].includes(docStatus ?? '') ||
            ('IN_PROGRESS' === docStatus &&
                sectionIndex !== undefined &&
                totalSections &&
                sectionIndex >= 0)
        ) {
            getDocumentContent();
        }
    }, [docStatus, sectionIndex, totalSections, getDocumentContent]);

    useEffect(() => {
        if (PendingStates.includes(docStatus ?? '')) {
            const handle = setInterval(() => {
                refreshProjectState?.({skipTechSpecSyncCheck: true});
            }, 15000);
            return () => clearInterval(handle);
        }
        return undefined;
    }, [docStatus, refreshProjectState]);

    useEffect(() => {
        if (pdfStatus === 'IN_PROGRESS' && project?.id) {
            let attempts = 0;
            const maxAttempts = 4;

            const handle = setInterval(() => {
                attempts++;

                if (attempts > maxAttempts) {
                    if (pdfStatus === 'IN_PROGRESS') {
                        toast({
                            duration: 10000,
                            title: 'Technical spec PDF is being prepared - Try again in a few minutes',
                        });
                        setPDFStatus?.('');
                    }
                    clearInterval(handle);
                    return;
                }

                if (getPDFDownloadStatus) {
                    getPDFDownloadStatus(project.id).then(async result => {
                        if (
                            result?.status === 'COMPLETED' &&
                            result.downloadUrl
                        ) {
                            setPDFStatus?.('');
                            clearInterval(handle);
                            const response = await fetch(result.downloadUrl);
                            const blob = await response.blob();
                            const url = window.URL.createObjectURL(blob);
                            const a = document.createElement('a');
                            a.href = url;
                            a.download = `${project.name} Blitzy AI ${
                                docType === 'software_req'
                                    ? 'Software Requirement'
                                    : 'Technical Specification'
                            }.pdf`;
                            a.click();
                            window.URL.revokeObjectURL(url);
                        }
                    });
                }
            }, 5000);

            return () => clearInterval(handle);
        }
        return undefined;
    }, [
        pdfStatus,
        project?.id,
        project?.name,
        getPDFDownloadStatus,
        setPDFStatus,
        docType,
    ]);

    // const markdown = useMemo(() => {
    //     return docType === 'software_req'
    //         ? project?.software_req
    //         : project?.tech_spec;
    // }, [docType, project?.software_req, project?.tech_spec]);

    // const onBuild = useCallback(() => {
    //     checkProjectDocumentDialog(
    //         projectJobStatus,
    //         docType,
    //         isTechSpecSubmitting,
    //     );
    // }, [
    //     checkProjectDocumentDialog,
    //     docType,
    //     projectJobStatus,
    //     isTechSpecSubmitting,
    // ]);

    const onStartEditing = useCallback(async () => {
        setEditing(true);
    }, []);

    const onCancelEditing = useCallback(async () => {
        setEditing(false);
        setContentDirty(false);
    }, []);

    const onSaveEdits = useCallback(async () => {
        logGAEvent('save_document_start', {docType}); // Log the start of saving operation
        try {
            if (editorRef.current && updateDocument) {
                const doc = editorRef.current.content();
                if (doc) {
                    await updateDocument(doc, docType);
                    setMarkdown(doc);
                }
            }
            setEditing(false);
            setContentDirty(false);
        } catch (error) {
            console.error('Failed to save document:', error);
            showAlert?.({
                type: 'HttpError',
                message: `Failed to save the ${docType}. Please try again.`,
            });
        }
    }, [docType, updateDocument, showAlert]);

    const onApprove = useCallback(async () => {
        setIsDocumentSubmitting(true);
        logGAEvent('submit_document_start', {docType}); // Log the start of submission operation
        if (isContentDirty) {
            await onSaveEdits();
        }

        if (!approveDocument) return;

        try {
            await approveDocument(docType);

            if (docType === 'software_req') {
                navigateTo(`/workspace/project/${project?.id}/tech-spec`, {
                    replace: true,
                });
            }
        } catch (error) {
            console.error('Failed to submit document:', error);
            showAlert?.({
                type: 'HttpError',
                message: `Failed to submit the ${docType}. Please try again.`,
            });
        } finally {
            setIsDocumentSubmitting(false);
        }
    }, [
        approveDocument,
        docType,
        isContentDirty,
        navigateTo,
        onSaveEdits,
        project?.id,
        showAlert,
    ]);

    const onCloseProjectDialog = useCallback(() => {
        if (isTechSpecSubmitting) {
            if (setTechSpecSubmitting) setTechSpecSubmitting(false);
        }
        if (closeDialog) {
            closeDialog();
        }
    }, [closeDialog, isTechSpecSubmitting, setTechSpecSubmitting]);

    const handleDownloadPDF = useCallback(async () => {
        if (!project || !markdown) {
            console.error('Project or document is not available.');
            return;
        }

        try {
            const result = await downloadTechSpecPDF(project.id);

            if (result.status === 'IN_PROGRESS') {
                setPDFStatus?.('IN_PROGRESS');
            } else if (result.status === 'COMPLETED' && result.downloadUrl) {
                setPDFStatus?.('COMPLETED');
                const response = await fetch(result.downloadUrl);
                if (!response.ok) {
                    throw new Error('Failed to download file from storage');
                }

                const blob = await response.blob();

                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `${project.name} Blitzy AI ${
                    docType === 'software_req'
                        ? 'Software Requirement'
                        : 'Technical Specification'
                }.pdf`;
                a.click();
                window.URL.revokeObjectURL(url);
            }
        } catch (error) {
            console.error(error);
            const {message} = error as {message: string};
            showAlert?.({
                type: 'HttpError',
                message: message,
            });
        }
    }, [project, markdown, docType, showAlert, setPDFStatus]);

    // if (isFetchingProject === true) return <FullScreenLoading />;

    return (
        <ProjectPanelContent
            docType={docType}
            onApprove={onApprove}
            onCancelEditing={onCancelEditing}
            onSaveEdits={onSaveEdits}
            onStartEditing={onStartEditing}
            onDownloadPDF={handleDownloadPDF}
            isEditing={isEditing}
            isContentDirty={isContentDirty}
            isDocumentSubmitting={isDocumentSubmitting}
            setIsDocumentSubmitting={setIsDocumentSubmitting}>
            <div className="flex flex-col w-full">
                <div className="w-full flex flex-col flex-grow sm:flex-row sm:gap-[24px] flex-1 overflow-y-auto sm:self-stretch">
                    {dialogType === 'pending-tech-spec' ? (
                        <div className="w-full h-full flex flex-col flex-grow">
                            <ProjectDialog
                                type={dialogType}
                                onClose={onCloseProjectDialog}
                                renderAs="overlay"
                            />
                        </div>
                    ) : (
                        <div className="w-full flex flex-col flex-grow">
                            {isMarkdownLoading && !markdown && (
                                <DocumentLoadingFallback />
                            )}
                            {dialogType && !markdown && (
                                <ProjectDialog
                                    type={dialogType}
                                    onClose={onCloseProjectDialog}
                                    renderAs="overlay"
                                />
                            )}
                            {dialogType === 'start-building' && (
                                <ProjectDialog
                                    type={dialogType}
                                    onClose={onCloseProjectDialog}
                                    renderAs="modal"
                                />
                            )}
                            <div className="sm:p-0 overflow-y-auto flex-grow">
                                <MarkdownPane
                                    ref={editorRef}
                                    initialMarkdown={markdown}
                                    docType={docType}
                                    isEditing={
                                        isEditing &&
                                        docStatus !== 'SUBMITTED' &&
                                        !isTechSpecSubmitting
                                    }
                                    onContentChange={handleEditorContentChange}
                                    viewerMessage={
                                        docStatus === 'SUBMITTED'
                                            ? 'It has been submitted to the system.'
                                            : "Click the 'Edit' button above to make changes."
                                    }
                                />
                                {projectState?.technicalSpec?.status ===
                                    'IN_PROGRESS' && (
                                    <DocumentLoadingFallback />
                                )}
                            </div>
                        </div>
                    )}
                </div>
            </div>
            <NavigationBlocker
                when={isContentDirty && docStatus !== 'SUBMITTED'}
            />
        </ProjectPanelContent>
    );
}

export function ProjectDocumentPanel({docType}: Props) {
    return <ProjectDocumentPanelContent docType={docType} />;
}
