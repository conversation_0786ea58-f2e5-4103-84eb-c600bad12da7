import {ForwardedRef, forwardRef, useMemo, useState, useEffect} from 'react';
import dayjs from 'dayjs';
import {Loader2} from 'lucide-react';
import {useProjectContext} from '@/context/project-context';
import {ProjectDocumentType} from '@/lib/entity-types';
import {TiptapEditor, IEditor} from '@/components/editor/TiptapEditor';

export type {IEditor};

interface Props {
    docType: ProjectDocumentType;
    markdown: string;
    onContentChange?: (isChanged: boolean) => void;
}

export const MarkdownEditor = forwardRef(
    (props: Props, ref: ForwardedRef<IEditor>) => {
        const {markdown = '', docType, onContentChange} = props;
        const {project, projectState} = useProjectContext();
        const [isContentLoading, setIsContentLoading] = useState(false);
        const [loadedMarkdown, setLoadedMarkdown] = useState('');

        const lastUpdatedAt = useMemo(() => {
            if (docType === 'prompt') {
                return project?.promptUpdatedAt;
            } else return projectState?.technicalSpec?.updatedAt;
        }, [
            docType,
            project?.promptUpdatedAt,
            projectState?.technicalSpec?.updatedAt,
        ]);

        // Load content asynchronously for large documents
        useEffect(() => {
            if (markdown && markdown.length > 5000) {
                // Consider it large if > 5000 characters
                setIsContentLoading(true);
                setLoadedMarkdown(''); // Start with empty content

                // Use setTimeout to defer content loading to prevent UI blocking
                const timeoutId = setTimeout(() => {
                    setLoadedMarkdown(markdown);
                    setIsContentLoading(false);
                }, 150); // Small delay to allow UI to render and show loading state

                return () => clearTimeout(timeoutId);
            } else {
                // For smaller documents, load immediately
                setLoadedMarkdown(markdown);
                setIsContentLoading(false);
            }
        }, [markdown]);

        // Handle content changes - this triggers validation
        const handleContentChange = () => {
            // We don't need to do anything with the content here since
            // the parent uses the ref to get content, but we need this
            // to trigger the onUpdate method in TiptapEditor
        };

        return (
            <div className="self-stretch overflow-hidden h-full relative">
                <div className="hidden sm:block absolute top-0 right-0 py-[12px] z-10">
                    {lastUpdatedAt && lastUpdatedAt > 0
                        ? `saved ${dayjs.unix(lastUpdatedAt).fromNow()}`
                        : ''}
                </div>

                {/* Loading overlay */}
                {isContentLoading && (
                    <div className="absolute inset-0 bg-white bg-opacity-90 flex items-center justify-center z-20">
                        <div className="flex flex-col items-center gap-3">
                            <Loader2 className="w-8 h-8 animate-spin text-[#5B39F3]" />
                            <p className="text-sm text-gray-600">
                                Loading document...
                            </p>
                        </div>
                    </div>
                )}

                <div className="h-full">
                    <TiptapEditor
                        ref={ref}
                        markdown={loadedMarkdown}
                        onChange={handleContentChange}
                        onContentChange={onContentChange}
                        editable={true}
                        docType={docType}
                    />
                </div>
            </div>
        );
    },
);

MarkdownEditor.displayName = 'MarkdownEditor';
