import {useCallback, useRef, useState, useEffect} from 'react';
import {MarkdownPane} from './markdown-pane';
import {useProjectContext} from '@/context/project-context';
import {IEditor} from './markdown-editor';
import {ProjectPanelContent} from './project-panel-content';
import {useAlertContext} from '@/context/alert-context';
import {logGAEvent, isLegacyProject, sleep} from '@/lib/utils';
import {NavigationBlocker} from '@/components/custom/navigation-blocker';

export function ProjectPromptPanel() {
    const {showAlert} = useAlertContext();
    const {project, promptType, updatePrompt, setIsPromptSubmitting} =
        useProjectContext();

    const [isPromptValid, setIsPromptValid] = useState<boolean>(false);
    const [enableNavigationBlock, setEnableNavigationBlock] = useState(true);
    const [isDirty, setIsDirty] = useState(false);

    const editorRef = useRef<IEditor>(null);

    useEffect(() => {
        if (project && isLegacyProject(project)) {
            setIsPromptValid(true); // legacy's prompt is always valid
        }
    }, [project]);

    const isReadOnly =
        project?.promptStatus === 'SUBMITTED' || isLegacyProject(project);

    const disableNavigationBlocker = useCallback(() => {
        setEnableNavigationBlock(false);
    }, []);

    const saveEdits = useCallback(
        async (isDraft: boolean): Promise<boolean> => {
            const eventName = 'save_prompt';
            const operationType = isDraft ? 'draft' : 'submit';
            let isSuccess = false;

            logGAEvent(`${eventName}_start`, {operation_type: operationType});

            if (editorRef.current && updatePrompt) {
                isSuccess = false;
                const content = editorRef.current.content();
                const contentChanged = content !== project?.prompt;
                if (promptType && (!isDraft || contentChanged)) {
                    try {
                        setIsPromptSubmitting?.(true);
                        const updatedProject = await updatePrompt(
                            content ?? '',
                            promptType,
                            isDraft,
                        );
                        isSuccess = !!updatedProject;

                        // disable blocking when prompt is submitted successfully
                        setEnableNavigationBlock(!isSuccess);
                        await sleep();
                    } catch (e) {
                        const errorMessage = (e as Error).message;
                        showAlert?.({
                            type: 'HttpError',
                            message: errorMessage,
                        });
                    } finally {
                        setIsPromptSubmitting?.(false);
                    }
                }
            }
            return isSuccess;
        },
        [
            updatePrompt,
            project?.prompt,
            promptType,
            setIsPromptSubmitting,
            showAlert,
        ],
    );

    // const delay = (ms:number) => {
    //     return new Promise(resolve => setTimeout(resolve, ms));
    // };

    const handleSubmit = useCallback(async (): Promise<boolean> => {
        if (!project?.type || !project.id) return false;

        const eventName = 'submit_prompt';
        logGAEvent(`${eventName}_start`, {operation_type: 'submit'});
        const success = await saveEdits(false);
        // setIsPromptSubmitting(true);
        // await delay(5000);
        // setIsPromptSubmitting(false);
        // const success = true;
        if (success) {
            return true;
        } else {
            console.error('Prompt submission failed.');
            return false;
        }
    }, [project?.type, project?.id, saveEdits]);

    const handleEditorContentChange = useCallback(() => {
        const content = editorRef.current?.content();
        setIsPromptValid(
            !!content && content.replace(/[^a-zA-Z0-9]/g, '').length > 100,
        );
        if (
            (!project?.prompt || project?.prompt === 'null') &&
            content === ''
        ) {
            setIsDirty(false);
        } else if (project?.prompt !== content) {
            setIsDirty(true);
        }
    }, [project?.prompt]);

    //set title and subtitle based on project type and techspec status
    const [title, setTitle] = useState('');
    const [subtitle, setSubtitle] = useState('');

    useEffect(() => {
        if (promptType === 'ADD_TESTING') {
            setTitle('Add testing');
            setSubtitle(
                "Specify what type of testing you'd like to implement (unit, integration, end-to-end) and which parts of your codebase need coverage.",
            );
        } else if (promptType === 'CUSTOM') {
            setTitle('What are your requirements?');
            setSubtitle(
                'Describe your specific development needs or any custom requirements for your project.',
            );
        } else if (promptType === 'FIX_BUGS') {
            setTitle('Fix bugs');
            setSubtitle(
                "Describe the bugs you're experiencing or paste error messages you need help resolving.",
            );
        } else if (promptType === 'FIX_CVES') {
            setTitle('Fix security vulnerabilities');
            setSubtitle(
                "Describe the security issues you've identified or specify the type of vulnerabilities you want to address.",
            );
        } else if (promptType === 'DOCUMENT_CODE') {
            setTitle('Document code');
            setSubtitle(
                'Describe which parts of your codebase need documentation or specify the documentation format you prefer.',
            );
        } else if (promptType === 'REFACTOR_CODE') {
            setTitle('Define refactoring scope');
            setSubtitle('Specify your target architecture or stack upgrade.');
        } else if (promptType === 'ADD_FEATURE') {
            setTitle('Add feature');
            setSubtitle(
                "Describe the new functionality you'd like to add to your existing codebase.",
            );
        } else if (promptType === 'ONBOARD_CODE') {
            setTitle("Tell us about the codebase you're onboarding");
            setSubtitle(
                'Sharing a bit of context helps us generate more accurate, useful documentation — ' +
                    'and ultimately a better tech spec. You can include what your product does, ' +
                    'what this codebase is responsible for, the tech stack and architecture, ' +
                    'key workflows, or any quirks.' +
                    '\n\n' +
                    'This step is optional — but the more context you give, the better the outcome',
            );
        } else {
            setTitle('New product prompt');
            setSubtitle(
                "Give us the details, and we'll help you bring your product to life.",
            );
        }
    }, [promptType]);

    // if (isFetchingProject === true) return <FullScreenLoading />;

    return (
        // <ProjectPanelContent header={<ProjectHeader />}>
        <ProjectPanelContent
            isReadOnly={isReadOnly}
            isPromptValid={isPromptValid}
            handleSubmit={handleSubmit}
            disableNavigationBlocker={disableNavigationBlocker}>
            <div className="mx-[24px] sm:mx-0">
                {/* <UpgradeAlertBanner /> */}
            </div>
            <div className="w-full flex flex-col flex-grow sm:flex-row sm:gap-[24px] flex-1 overflow-y-auto sm:self-stretch">
                <div className="w-full h-full flex flex-col gap-[24px] sm:gap-[24px] px-[0px] py-[0px] sm:p-0">
                    <div className="w-full sm:mb-[24px]">
                        <div className="mb-[12px] sm:mb-[12px] text-[20px] sm:text-[24px] leading-[1.5em] text-[#333] font-bold">
                            {title}
                        </div>
                        <div className="text-[16px] sm:text-[16px] leading-[1.5em] text-[#333] whitespace-pre-line">
                            {subtitle}
                            <a
                                href="https://blitzyai.notion.site/Blitzy-Input-Prompt-Guide-145238e0392a808d8ea1c067292d9931"
                                target="_blank">
                                {' '}
                                Prompt Guide ↗
                            </a>
                        </div>
                    </div>
                    <MarkdownPane
                        ref={editorRef}
                        initialMarkdown={project?.prompt}
                        docType="prompt"
                        isEditing={!isReadOnly}
                        viewerMessage={
                            isReadOnly
                                ? `It has been submitted to the system.`
                                : ''
                        }
                        onContentChange={handleEditorContentChange}
                    />
                    {/* spacer */}
                    <div className="h-[30px] sm:h-[100px]" />
                </div>
            </div>
            <NavigationBlocker when={isDirty && enableNavigationBlock} />
        </ProjectPanelContent>
    );
}
