import {useState, useCallback} from 'react';
import {useNavigate, useLocation} from 'react-router-dom';
import {Popover, PopoverContent, PopoverTrigger} from '@/components/ui/popover';
import {useAuthContext} from '@/context/auth-context';
import {UserPopOverMenuPane} from './user-popover-menu-pane';
import ArrowDown from './icons/arrow-down';
import Avatar from '@/components/ui/avatar';
import {Link} from 'react-router-dom';
import iconBlitzy from '/images/blitzy-icon-text.svg';
import {HelpCircleIcon} from './icons/help-circle';
import {useSubscriptionContext} from '@/context/subscription-context';
import {logGAEvent} from '@/lib/utils';
import {HelpAndSupportPane} from './help-support-pane';
import Cog from './icons/cog';
import {
    Tooltip,
    TooltipTrigger,
    TooltipContent,
    TooltipProvider,
} from '@/components/ui/tooltip';

interface Props {
    onClose: () => void;
}

export function TopNavPane({onClose}: Props) {
    const [isPopoverMenuOpen, setPopverMenuOpen] = useState(false);
    const [isHelpAndSupportOpen, setHelpAndSupportOpen] = useState(false);
    const location = useLocation();
    const {userProfile} = useAuthContext();
    const {trialRemainingInterval, subscriptionStatus, isTrialing} =
        useSubscriptionContext();
    const navigateTo = useNavigate();

    const handleSettings = useCallback(() => {
        logGAEvent('Settings Accessed', {
            userId: userProfile?.id,
        });

        navigateTo('/workspace/settings/profile', {replace: true});
        if (onClose) {
            onClose();
        }
    }, [navigateTo, onClose, userProfile]);

    const handleHelpClick = useCallback(() => {
        setHelpAndSupportOpen(prev => !prev);
    }, []);

    return (
        <div className="h-[64px] px-[32px] py-[16px] flex justify-between items-center gap-[16px] border-b">
            <Link to="/workspace/projects">
                <img
                    src={iconBlitzy}
                    alt="Blitzy"
                    className="mx-auto h-[32px] sm:h-[32px]"
                />
            </Link>
            <div className="flex items-center gap-[8px]">
                <Popover
                    open={isHelpAndSupportOpen}
                    onOpenChange={setHelpAndSupportOpen}>
                    <TooltipProvider delayDuration={100}>
                        <Tooltip>
                            <PopoverTrigger asChild>
                                <TooltipTrigger asChild>
                                    <div
                                        role="button"
                                        onClick={handleHelpClick}
                                        className="p-[4px] text-[#333] h-[64px] w-[64px] flex items-center justify-center cursor-pointer ">
                                        <HelpCircleIcon
                                            className="w-[24px] h-[24px]"
                                            isActive={isHelpAndSupportOpen}
                                            variant={
                                                isHelpAndSupportOpen
                                                    ? 'black'
                                                    : 'default'
                                            }
                                        />
                                    </div>
                                </TooltipTrigger>
                            </PopoverTrigger>
                            <TooltipContent>
                                <p className="text-[12px]">Help & Support</p>
                            </TooltipContent>
                        </Tooltip>
                    </TooltipProvider>
                    <PopoverContent className="mr-[0px]" align="end">
                        <HelpAndSupportPane
                            onClose={() => setHelpAndSupportOpen(false)}
                            showBack={false}
                        />
                    </PopoverContent>
                </Popover>

                <TooltipProvider delayDuration={100}>
                    <Tooltip>
                        <TooltipTrigger asChild>
                            <div
                                role="button"
                                className="h-[64px] w-[64px] flex items-center justify-center"
                                onClick={handleSettings}>
                                <Cog
                                    fill={
                                        location.pathname.includes('/settings')
                                            ? '#0F0F11'
                                            : '#666666'
                                    }
                                    hoverFill="#0F0F11"
                                />
                            </div>
                        </TooltipTrigger>
                        <TooltipContent>
                            <p className="text-[12px]">Settings</p>
                        </TooltipContent>
                    </Tooltip>
                </TooltipProvider>
                <Popover
                    open={isPopoverMenuOpen}
                    onOpenChange={setPopverMenuOpen}>
                    <PopoverTrigger asChild>
                        <div className="h-[64px] flex items-center px-[12px] gap-0 cursor-pointer">
                            <Avatar
                                src={
                                    userProfile?.avatar?.startsWith('http')
                                        ? userProfile.avatar
                                        : userProfile?.avatar
                                          ? `/images/${userProfile.avatar}`
                                          : undefined
                                }
                                name={userProfile?.firstName}
                                email={userProfile?.email}
                                size="large"
                            />
                            <div className="ml-[8px] mr-[4px] flex flex-col">
                                <span className="w-[80px] text-[16px] text-black hover:text-brand-purple font-semibold truncate">
                                    {userProfile?.firstName ??
                                        userProfile?.email?.split('@')[0]}
                                </span>
                                {userProfile?.subscription?.planName ===
                                    'FREE' && (
                                    <div className="text-[14px] text-[#999]">
                                        <span className="line-clamp-1">
                                            Free
                                        </span>
                                    </div>
                                )}
                                {userProfile?.subscription?.planName ===
                                    'TEAMS' && (
                                    <div className="text-[14px] text-[#999]">
                                        <span className="line-clamp-1">
                                            Team
                                        </span>
                                    </div>
                                )}
                                {userProfile?.subscription?.planName ===
                                    'PRO' &&
                                    ((subscriptionStatus === 'TRIALING' ||
                                        (subscriptionStatus === 'CANCELLING' &&
                                            isTrialing)) &&
                                    trialRemainingInterval &&
                                    trialRemainingInterval > 0 ? (
                                        <div className="text-[14px] text-[#999]">
                                            <span className="line-clamp-1">
                                                Pro Trial
                                            </span>
                                        </div>
                                    ) : (
                                        <div className="text-[14px] text-[#999]">
                                            <span className="line-clamp-1">
                                                Pro
                                            </span>
                                        </div>
                                    ))}
                                {userProfile?.subscription?.planName ===
                                    'ENTERPRISE' && (
                                    <div className="text-[14px] text-[#999]">
                                        <span className="line-clamp-1">
                                            Enterprise
                                        </span>
                                    </div>
                                )}
                            </div>
                            <span
                                className={`p-[4px] ${isPopoverMenuOpen ? 'rotate-180' : 'rotate-0'}`}>
                                <ArrowDown />
                            </span>
                        </div>
                    </PopoverTrigger>
                    <PopoverContent className="mx-[26px]">
                        <UserPopOverMenuPane
                            onClose={() => setPopverMenuOpen(false)}
                            showUserInfo={false}
                        />
                    </PopoverContent>
                </Popover>
            </div>
        </div>
    );
}
