import {useCallback, useEffect, useState, useRef} from 'react';
import {z} from 'zod';
import {zodResolver} from '@hookform/resolvers/zod';
import {Input} from '@/components/ui/input';
import {HomePanelShell} from './home-panel-shell';
import {Controller, SubmitHandler, useForm} from 'react-hook-form';
import {useHomeContext} from '@/home';
import {logGAEvent, useUtmTracker} from '@/lib/utils';
import {LoadingSubmitButton} from '@/components/custom/loading-submit-button';
import {TermClause} from '@/components/custom/term-clause';
import {Config} from '@/config';
import {auth} from '@/firebase';
import {SAMLAuthProvider} from 'firebase/auth';
import {signInWithPopupTracked} from '@/context/auth-context';

const SSO_SignInSchema = z.object({
    email: z.string().min(1, {message: 'Email is required'}).email(),
});
type SSO_SignInSchemaType = z.infer<typeof SSO_SignInSchema>;

interface SSOState {
    error: string;
    isChecking: boolean;
}

export function SSO_SignInPanel() {
    const {signUpEmail, setSignUpEmail} = useHomeContext();
    const [ssoState, setSsoState] = useState<SSOState>({
        error: '',
        isChecking: false,
    });
    const previousEmailRef = useRef<string>('');

    const {
        control,
        handleSubmit,
        watch,
        formState: {errors, isSubmitting},
    } = useForm<SSO_SignInSchemaType>({
        resolver: zodResolver(SSO_SignInSchema),
        mode: 'onTouched',
        defaultValues: {
            email: '',
        },
        values: {
            email: signUpEmail || '',
        },
    });

    const {getStoredUtmParams} = useUtmTracker();
    const watchedEmail = watch('email');

    // Initialize UTM tracking once
    useEffect(() => {
        getStoredUtmParams();
    }, [getStoredUtmParams]);

    // Optimized error clearing logic
    const clearSsoError = useCallback(() => {
        if (ssoState.error) {
            setSsoState(prev => ({...prev, error: ''}));
        }
    }, [ssoState.error]);

    // Clear SSO error when email changes
    useEffect(() => {
        const hasEmailChanged = watchedEmail !== previousEmailRef.current;
        const hadPreviousEmail = previousEmailRef.current !== '';

        if (ssoState.error && hasEmailChanged && hadPreviousEmail) {
            clearSsoError();
        }

        previousEmailRef.current = watchedEmail;
    }, [watchedEmail, ssoState.error, clearSsoError]);

    // SSO check function
    const checkSSOAvailability = useCallback(
        async (domain: string): Promise<boolean> => {
            // Return false if domain doesn't support SSO
            return Config.SAML_ENABLED_DOMAINS.includes(domain);
        },
        [],
    );

    const samlSSOLogin = useCallback(
        async (samlProvider: SAMLAuthProvider, onPopupClose?: () => void) => {
            try {
                const {result, popup} = await signInWithPopupTracked(
                    auth,
                    samlProvider,
                    onPopupClose,
                );

                // This gives you a Microsoft Access Token.
                const credential =
                    SAMLAuthProvider.credentialFromResult(result);
                if (credential) {
                    console.debug('SAML SSO signed in');
                }

                return {result, popup};
            } catch (error) {
                console.error(error);
                throw error;
            }
        },
        [],
    );

    const onSubmit: SubmitHandler<SSO_SignInSchemaType> = useCallback(
        async data => {
            const {email} = data;

            if (!email || !setSignUpEmail) return;

            // Reset state at the beginning
            setSsoState({error: '', isChecking: true});

            try {
                const domain = email.split('@')[1]?.toLowerCase();

                if (!domain) {
                    throw new Error('Invalid email domain');
                }

                // Check if domain supports SSO
                const ssoSupported = await checkSSOAvailability(domain);

                if (!ssoSupported) {
                    setSsoState({
                        error: "This email isn't set up for SSO with SAML. Try a different email or contact support.",
                        isChecking: false,
                    });
                    return;
                }

                // SSO is available, proceed with sign up
                setSignUpEmail(email);

                // Log analytics event
                logGAEvent('signup_email_continue', {
                    method: 'sso',
                    email: email,
                    step: 'email_submission',
                    domain: domain,
                });

                const samlProvider = new SAMLAuthProvider(`saml.${domain}`);

                await samlSSOLogin(samlProvider);
            } catch (error) {
                console.error('SSO login error:', error);
                setSsoState({
                    error: 'Unable to login with SSO. Please try again or contact support.',
                    isChecking: false,
                });
            } finally {
                // Ensure loading state is cleared if not already done
                setSsoState(prev => ({...prev, isChecking: false}));
            }
        },
        [setSignUpEmail, checkSSOAvailability, samlSSOLogin],
    );

    // Determine if form is in loading state
    const isLoading = isSubmitting || ssoState.isChecking;

    // Determine error message to display
    const errorMessage = errors.email?.message || ssoState.error;

    return (
        <HomePanelShell title="Sign in with SAML (SSO)" subtitle={null}>
            <div className="w-full flex flex-col gap-[32px] items-center">
                <form className="w-full" onSubmit={handleSubmit(onSubmit)}>
                    <div className="w-full flex flex-col gap-[12px]">
                        <div className="form-label">Work email</div>
                        <div className="mb-[12px] w-full flex flex-col gap-[4px]">
                            <Controller
                                name="email"
                                control={control}
                                render={({field}) => (
                                    <Input
                                        className={`w-full ${errorMessage ? 'input-field-error' : 'input-field'}`}
                                        type="email"
                                        placeholder="<EMAIL>"
                                        autoComplete="email"
                                        {...field}
                                        onChange={e => {
                                            field.onChange(e);
                                            // Clear errors on input change for better UX
                                            if (
                                                errorMessage &&
                                                e.target.value !==
                                                    previousEmailRef.current
                                            ) {
                                                clearSsoError();
                                            }
                                        }}
                                    />
                                )}
                            />

                            {errorMessage && (
                                <div
                                    className="form-error-note"
                                    role="alert"
                                    aria-live="polite">
                                    {errorMessage}
                                </div>
                            )}
                        </div>

                        <LoadingSubmitButton
                            loadingText={
                                ssoState.isChecking
                                    ? 'Logging you in...'
                                    : 'One moment...'
                            }
                            loading={isLoading}
                            className="primary-button"
                            disabled={isLoading || !!errors.email}>
                            Continue
                        </LoadingSubmitButton>
                    </div>
                </form>
            </div>
            <TermClause srcPage="sso-sign-in_page" />
        </HomePanelShell>
    );
}
