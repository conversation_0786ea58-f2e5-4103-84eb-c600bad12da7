import os

import jwt
from werkzeug.wrappers import Request

JWT_SECRET_KEY = os.environ.get('JWT_SECRET_KEY', 'Blitzy26')


class AuthMiddleware:
    def __init__(self, app, exclude_paths=None):
        self.app = app
        self.exclude_paths = exclude_paths or [
            '/v1/auth/login',
            '/v1/events',
        ]

    def __call__(self, environ, start_response):
        request = Request(environ)
        path = request.path

        if any(path.startswith(excluded) for excluded in self.exclude_paths):
            return self.app(environ, start_response)

        if request.method == 'OPTIONS':
            return self.app(environ, start_response)

        auth_header = request.headers.get('Authorization', '')
        token = None

        if auth_header.startswith('Bearer '):
            token = auth_header.split(' ')[1]

        if not token:
            environ['auth_error'] = 'Token is missing'
        else:
            try:
                payload = jwt.decode(token, JWT_SECRET_KEY, algorithms=['HS256'])

                environ['user_email'] = payload['sub']

            except jwt.ExpiredSignatureError:
                environ['auth_error'] = 'Token has expired'

            except jwt.InvalidTokenError:
                environ['auth_error'] = 'Invalid token'

        return self.app(environ, start_response)
