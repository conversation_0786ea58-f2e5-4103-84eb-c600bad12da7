import datetime
import os

import jwt
from blitzy_utils.logger import logger

AUTH_EMAIL = os.environ.get('AUTH_EMAIL', '<EMAIL>')
AUTH_PASSWORD = os.environ.get('AUTH_PASSWORD', 'password')
JWT_SECRET_KEY = os.environ.get('JWT_SECRET_KEY', 'Blitzy26')
TOKEN_EXPIRATION_HOURS = 24


def generate_token(email):
    """Generate a JWT token for authenticated users."""
    try:
        payload = {
            'exp': datetime.datetime.utcnow() + datetime.timedelta(hours=TOKEN_EXPIRATION_HOURS),
            'iat': datetime.datetime.utcnow(),
            'sub': email
        }
        return jwt.encode(
            payload,
            JWT_SECRET_KEY,
            algorithm='HS256'
        )
    except Exception as e:
        logger.error(f"Error generating token: {str(e)}")
        return None


def verify_credentials(email, password):
    """Check if provided credentials match the stored credentials."""
    return email == AUTH_EMAIL and password == AUTH_PASSWORD
