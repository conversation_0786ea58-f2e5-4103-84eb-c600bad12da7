from typing import Optional

from common_models.db_client import get_db_session
from common_models.models import TechSpecDocumentContext
from sqlalchemy.orm import Session


def get_tech_spec_document_context(branch_id: str, head_commit_sha: str, session: Optional[Session] = None) -> Optional[
    TechSpecDocumentContext]:
    """
    Fetches the technical specification document context for a given repository and branch.

    :param branch_id: ID of the branch.
    :param head_commit_sha: SHA of the branch's head commit.
    :param session: Optional database session.
    :return: An instance of TechSpecDocumentContext or None if not found.
    """
    with get_db_session(session) as session:
        tech_spec_document_context = session.query(TechSpecDocumentContext).filter(
            TechSpecDocumentContext.branch_id == branch_id,
            TechSpecDocumentContext.head_commit_hash == head_commit_sha
        ).first()

        if tech_spec_document_context and not session:
            session.expunge(tech_spec_document_context)
        return tech_spec_document_context
