from datetime import datetime, timedelta
from typing import List, Optional, <PERSON><PERSON>

import sqlalchemy.sql.expression as sql
from common_models.db_client import get_db_session
from common_models.models import (CloudRunJobTracker, CloudRunJobTrackerStatus,
                                  Project, Subscription, User)
from sqlalchemy import and_, asc, desc, func, or_
from sqlalchemy.orm import Session, joinedload

VALID_STATUSES = [s.value for s in CloudRunJobTrackerStatus]


def get_cloud_run_job_by_job_id(job_id, db_session: Optional[Session] = None):
    """
    Retrieve a CloudRunJobTracker entry using its job_id from the database.

    Queries the CloudRunJobTracker table to fetch a record corresponding to the
    specified job_id. Optionally, an external database session can be used to
    wrap the operation.

    :param job_id: Identifier of the CloudRunJobTracker record to fetch.
    :type job_id: str
    :param db_session: Optional database session to use for querying.
    :type db_session: Optional[Session]
    :return: CloudRunJobTracker instance if found, else None.
    """
    with get_db_session(db_session) as session:
        cloud_run_job = session.query(CloudRunJobTracker).filter(CloudRunJobTracker.job_id == job_id).first()

    if cloud_run_job and not session:
        session.expunge(cloud_run_job)

    return cloud_run_job


def get_paginated_jobs(
        session: Session,
        page: int,
        limit: int,
        status: Optional[str] = None,
        sort_by=CloudRunJobTracker.created_at,
        sort_order: str = 'desc',
        search_term: str = '',
        job_type: Optional[str] = None,
        plan_type: Optional[str] = None,
        duration: Optional[str] = None
) -> Tuple[List[CloudRunJobTracker], int]:
    """
    Query jobs with pagination, filtering, sorting, and searching.
    Optimized for Google Spanner compatibility.

    :param session: Database session
    :param page: Page number
    :param limit: Items per page
    :param status: Filter by job status
    :param sort_by: Field to sort by
    :param sort_order: Sort order (asc/desc)
    :param search_term: Search term for filtering
    :param job_type: Filter by job type
    :param plan_type: Filter by plan type
    :param duration: Filter running jobs by duration (1, 2, 3, 3+)
    :return: Tuple of (jobs list, total count)
    """
    query = session.query(CloudRunJobTracker)
    query = query.outerjoin(User, CloudRunJobTracker.user_id == User.id)
    query = query.outerjoin(Project, CloudRunJobTracker.project_id == Project.id)
    query = query.outerjoin(Subscription, Subscription.user_id == User.id)

    query = query.options(
        joinedload(CloudRunJobTracker.user).joinedload(User.subscription),
        joinedload(CloudRunJobTracker.project)
    )

    if status:
        try:
            valid_statuses = [s.value for s in CloudRunJobTrackerStatus]
            if status not in valid_statuses:
                raise ValueError(f"Invalid status value: '{status}'. Valid values are: {', '.join(valid_statuses)}")
            status_enum = CloudRunJobTrackerStatus(status)
            query = query.filter(CloudRunJobTracker.job_status == status_enum)
        except ValueError as e:
            raise ValueError(str(e))

    # Apply duration filter for running jobs
    if status == "RUNNING" and duration:
        now = datetime.utcnow()

        if duration == "1":
            # Jobs running for 1 day
            one_day_ago = now - timedelta(days=1)
            query = query.filter(
                CloudRunJobTracker.updated_at >= one_day_ago
            )
        elif duration == "2":
            # Jobs running for 2 days
            two_days_ago = now - timedelta(days=2)
            one_day_ago = now - timedelta(days=1)
            query = query.filter(
                and_(
                    CloudRunJobTracker.updated_at >= two_days_ago,
                    CloudRunJobTracker.updated_at < one_day_ago
                )
            )
        elif duration == "3":
            # Jobs running for 3 days
            three_days_ago = now - timedelta(days=3)
            two_days_ago = now - timedelta(days=2)
            query = query.filter(
                and_(
                    CloudRunJobTracker.updated_at >= three_days_ago,
                    CloudRunJobTracker.updated_at < two_days_ago
                )
            )
        elif duration == "3+":
            # Jobs running for more than 3 days
            three_days_ago = now - timedelta(days=3)
            query = query.filter(
                CloudRunJobTracker.updated_at < three_days_ago
            )

    if search_term:
        search_pattern = f"%{search_term.lower()}%"
        search_filters = [
            sql.func.lower(User.email).like(search_pattern),
            sql.func.lower(User.first_name).like(search_pattern),
            sql.func.lower(User.last_name).like(search_pattern),
            sql.func.lower(Project.name).like(search_pattern),
            sql.func.lower(CloudRunJobTracker.job_id).like(search_pattern),
            sql.func.lower(CloudRunJobTracker.event_data['job_id'].astext).like(search_pattern),
        ]
        query = query.filter(or_(*search_filters))

    if job_type:
        job_type_pattern = f"{job_type}-%"
        query = query.filter(CloudRunJobTracker.job_id.like(job_type_pattern))

    if plan_type:
        query = query.filter(Subscription.plan_name == plan_type)

    count_query = session.query(func.count()).select_from(query.subquery())
    total = count_query.scalar()

    order_func = asc if sort_order == 'asc' else desc
    query = query.order_by(order_func(sort_by))

    offset = (page - 1) * limit
    query = query.offset(offset).limit(limit)

    jobs = query.all()

    return jobs, total


def get_cloud_run_job_tracker_by_id(job_id: str, db_session: Optional[Session] = None) -> Optional[CloudRunJobTracker]:
    """
    Retrieve a CloudRunJobTracker entry by its job ID from the database.

    :param job_id: Job ID to search for.
    :param db_session: Optional database session to use. If not provided, a new session is created.
    :return: CloudRunJobTracker instance if found, otherwise None.
    """
    with get_db_session(db_session) as session:
        cloud_run_job = (session.query(CloudRunJobTracker)
                         .options(joinedload(CloudRunJobTracker.user),
                                  joinedload(CloudRunJobTracker.project))
                         .filter(CloudRunJobTracker.id == job_id).first())

        if cloud_run_job and not session:
            session.expunge(cloud_run_job)

    return cloud_run_job
