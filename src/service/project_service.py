from sqlalchemy.orm import Session, joinedload
from typing import Optional

from common_models.db_client import get_db_session
from common_models.models import Project


def get_project_by_id(project_id: str, session: Optional[Session] = None) -> Optional[Project]:
    with get_db_session(session) as session:
        project = (session.query(Project)
                   .options(joinedload(Project.user),
                            joinedload(Project.software_requirement),
                            joinedload(Project.technical_spec),
                            joinedload(Project.code_generation))
                   .filter(Project.id == project_id).first())
        if project:
            session.expunge(project)
            return project
        return None
