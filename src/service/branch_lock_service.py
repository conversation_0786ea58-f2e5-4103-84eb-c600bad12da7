from common_models.db_client import get_db_session
from typing import Optional

from sqlalchemy.orm import Session

from common_models.models import BranchLock


def get_branch_lock_by_branch_id(branch_pattern_id: str, session: Optional[Session] = None) -> Optional[BranchLock]:
    """
    Retrieve the branch lock associated with the given branch pattern ID.

    :param branch_pattern_id: Identifier of the branch pattern.
    :type branch_pattern_id: str
    :param session: Optional database session.
    :type session: Optional[Session]
    :return: Branch lock object associated with the branch pattern ID or None if not found.
    """
    with get_db_session(session) as session:
        branch_lock = (session.query(BranchLock).filter(BranchLock.branch_pattern_id == branch_pattern_id,
                                                        BranchLock.is_active == True).first())

        if branch_lock and not session:
            session.expunge(branch_lock)
        return branch_lock


def update_branch_lock_by_id(lock_id: str, update_payload: dict, session: Optional[Session] = None):
    """
    Update a branch lock by its ID with the provided payload.

    :param lock_id: Identifier for the branch lock to be updated.
    :type lock_id: str
    :param update_payload: Data to update the branch lock's attributes.
    :type update_payload: dict
    :param session: Optional database session to be reused during the operation.
    :type session: Optional[Session]
    :return: None
    :raises Exception: If the branch lock update fails.
    """
    with get_db_session(session) as session:
        branch_lock_updated = session.query(BranchLock).filter(BranchLock.id == lock_id).update(update_payload)
        session.flush()

        if not branch_lock_updated:
            raise Exception(f"Failed to update branch lock for the id {lock_id}.")
