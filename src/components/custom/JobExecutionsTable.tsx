// src/components/custom/JobExecutionsTable.tsx
import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  PlayCircle,
  AlertCircle,
  CheckCircle,
  Loader2,
  MoreVertical,
  RefreshCw,
  FileText,
  Clock,
  Search,
  ChevronLeft,
  ChevronRight,
  RotateCw,
  X,
  Download,
} from "lucide-react";
import {
  triggerJob,
  getJobDetails,
  refreshJobStatus,
  downloadLatestBinary,
} from "../../services/api";
import { toast } from "sonner";
import JobTypeFilter from "./JobTypeFilter";
import PlanTypeFilter from "./PlanTypeFilter";
import DurationFilter from "./DurationFilter";

interface FormattedJob {
  id: string;
  jobId: string;
  executionId: string;
  userEmail: string;
  projectName: string;
  creationTime: string;
  taskStatus: string;
  endTime: string;
  status: string;
  isTriggered: boolean;
  jobType: string;
  userFirstName?: string;
  userLastName?: string;
  duration?: number;
}

interface JobPayload {
  eventData: Record<string, unknown>;
}

interface JobExecutionsTableProps {
  jobs: FormattedJob[];
  activeJobId: string | null;
  setActiveJobId: (id: string) => void;
  retriggerPayload: string;
  setRetriggerPayload: (payload: string) => void;
  onRefreshSuccess?: (jobId: string) => void;
  title?: string;
  isLoading: boolean;
  pagination?: {
    limit: number;
    page: number;
    pages: number;
    total: number;
  };
  onPageChange?: (page: number) => void;
  searchTerm?: string;
  onSearch?: (term: string) => void;
  availableJobTypes?: string[];
  selectedJobType?: string;
  onJobTypeChange?: (jobType: string) => void;
  availablePlanTypes?: string[];
  selectedPlanType?: string;
  onPlanTypeChange?: (planType: string) => void;
  selectedDuration?: string;
  onDurationChange?: (duration: string) => void;
  isRunningTab?: boolean;
}

const JobExecutionsTable = ({
  jobs,
  activeJobId,
  setActiveJobId,
  setRetriggerPayload,
  onRefreshSuccess,
  title = "All Job Executions",
  isLoading,
  pagination,
  onPageChange,
  searchTerm = "",
  onSearch,
  availableJobTypes = [],
  selectedJobType = "all",
  onJobTypeChange,
  availablePlanTypes = [],
  selectedPlanType = "all",
  onPlanTypeChange,
  selectedDuration = "all",
  onDurationChange,
  isRunningTab = false,
}: JobExecutionsTableProps) => {
  const [triggerLoading, setTriggerLoading] = useState<{
    [key: string]: boolean;
  }>({});
  const [refreshingStatus, setRefreshingStatus] = useState<{
    [key: string]: boolean;
  }>({});
  const [showPayload, setShowPayload] = useState(false);
  const [jobPayload, setJobPayload] = useState<JobPayload | null>(null);
  const [loadingPayload, setLoadingPayload] = useState(false);
  const [localSearchTerm, setLocalSearchTerm] = useState(searchTerm);
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState(searchTerm);
  const [highlightedJobId, setHighlightedJobId] = useState<string | null>(null);
  const [downloadingBinary, setDownloadingBinary] = useState<{
    [key: string]: boolean;
  }>({});

  // Update local search when prop changes
  useEffect(() => {
    setLocalSearchTerm(searchTerm);
  }, [searchTerm]);

  // Debounce search term
  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedSearchTerm(localSearchTerm);
    }, 500); // 500ms debounce delay

    return () => {
      clearTimeout(handler);
    };
  }, [localSearchTerm]);

  // Apply the search when the debounced term changes
  useEffect(() => {
    if (debouncedSearchTerm !== searchTerm && onSearch) {
      onSearch(debouncedSearchTerm);
    }
  }, [debouncedSearchTerm, searchTerm, onSearch]);

  // Function to handle job retrigger
  const handleRetriggerJob = async (jobId: string) => {
    try {
      setTriggerLoading((prev) => ({ ...prev, [jobId]: true }));
      const response = await triggerJob(jobId);

      if (response.message === "success") {
        toast.success(`Job ${jobId} retriggered successfully!`);
        if (onRefreshSuccess) {
          onRefreshSuccess(jobId);
        }
      }
      setRetriggerPayload("");
    } catch (error) {
      toast.error(
        `Failed to retrigger job. Please try again. ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    } finally {
      setTriggerLoading((prev) => ({ ...prev, [jobId]: false }));
    }
  };

  // Function to handle job status refresh
  const handleRefreshStatus = async (jobId: string) => {
    try {
      setRefreshingStatus((prev) => ({ ...prev, [jobId]: true }));
      await refreshJobStatus(jobId);
      toast.success(`Job status refreshed successfully!`);
      if (onRefreshSuccess) {
        onRefreshSuccess(jobId);
      }
    } catch (error) {
      toast.error(
        `Failed to refresh status. Please try again. ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    } finally {
      setRefreshingStatus((prev) => ({ ...prev, [jobId]: false }));
    }
  };

  // Function to handle view payload
  const handleViewPayload = async (jobId: string) => {
    try {
      setLoadingPayload(true);
      setActiveJobId(jobId);
      const details = await getJobDetails(jobId);
      setJobPayload(details);
      setShowPayload(true);
    } catch (error) {
      toast.error(
        `Failed to fetch job payload. Please try again. ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    } finally {
      setLoadingPayload(false);
    }
  };

  // Function to handle binary download
  const handleDownloadBinary = async (jobId: string) => {
    try {
      setDownloadingBinary((prev) => ({ ...prev, [jobId]: true }));
      const blob = await downloadLatestBinary();

      // Create a download link and trigger the download
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `binary-${jobId}.zip`; // You can customize the filename
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      toast.success("Binary downloaded successfully!");
    } catch (error) {
      toast.error(
        `Failed to download binary. Please try again. ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    } finally {
      setDownloadingBinary((prev) => ({ ...prev, [jobId]: false }));
    }
  };

  // Handle Job ID click - filter by job ID
  const handleJobIdClick = (jobId: string) => {
    if (onSearch) {
      setLocalSearchTerm(jobId);
      setDebouncedSearchTerm(jobId);
      onSearch(jobId);
      setHighlightedJobId(jobId);
    }
  };

  // Handle search
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setLocalSearchTerm(e.target.value);
  };

  // Clear search
  const handleClearSearch = () => {
    // Clear the local search term
    setLocalSearchTerm("");
    // Clear the debounced search term to prevent it from triggering
    setDebouncedSearchTerm("");
    // Clear highlighted job ID
    setHighlightedJobId(null);
    // Immediately notify parent component to clear the search
    if (onSearch) {
      onSearch("");
    }
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    if (onPageChange && page > 0 && page <= (pagination?.pages || 1)) {
      onPageChange(page);
    }
  };

  // Get the latest job for highlighting (when filtering by job ID)
  const getLatestJobForHighlighting = () => {
    if (!highlightedJobId || jobs.length === 0) return null;

    // Filter jobs with the highlighted job ID
    const filteredJobs = jobs.filter((job) => job.jobId === highlightedJobId);

    if (filteredJobs.length === 0) return null;

    // Sort by creation time to find the latest one
    const sortedJobs = filteredJobs.sort((a, b) => {
      const dateA = new Date(a.creationTime);
      const dateB = new Date(b.creationTime);
      return dateB.getTime() - dateA.getTime(); // Latest first
    });

    return sortedJobs[0]; // Return the latest job
  };

  const latestJob = getLatestJobForHighlighting();
  const getJobStatusInfo = (job: FormattedJob) => {
    // Check if the job is failed but has been retriggered
    if (job.status === "failed" && job.isTriggered) {
      return {
        text: "Retriggered",
        bgClass: "bg-blue-100 text-blue-800",
        icon: <RotateCw className="mr-1 h-3 w-3" />,
      };
    }

    // Original statuses
    switch (job.status) {
      case "succeeded":
        return {
          text: "Succeeded",
          bgClass: "bg-green-100 text-green-800",
          icon: <CheckCircle className="mr-1 h-3 w-3" />,
        };
      case "running":
        return {
          text: "Running",
          bgClass: "bg-blue-100 text-blue-800",
          icon: <Clock className="mr-1 h-3 w-3" />,
        };
      case "failed":
        return {
          text: "Failed",
          bgClass: "bg-red-100 text-red-800",
          icon: <AlertCircle className="mr-1 h-3 w-3" />,
        };
      default:
        return {
          text: job.status.charAt(0).toUpperCase() + job.status.slice(1),
          bgClass: "bg-gray-100 text-gray-800",
          icon: <AlertCircle className="mr-1 h-3 w-3" />,
        };
    }
  };

  // Calculate job duration in days
  const calculateJobDuration = (job: FormattedJob): number => {
    const startTime = new Date(job.creationTime);
    const endTime =
      job.status === "running" ? new Date() : new Date(job.endTime);
    const durationMs = endTime.getTime() - startTime.getTime();
    return Math.floor(durationMs / (1000 * 60 * 60 * 24)); // Convert to days
  };

  // Filter jobs based on duration
  const getFilteredJobsByDuration = (
    jobsList: FormattedJob[]
  ): FormattedJob[] => {
    if (!isRunningTab || selectedDuration === "all") return jobsList;

    const durationDays = parseInt(selectedDuration);
    return jobsList.filter((job) => {
      const jobDuration = calculateJobDuration(job);
      if (selectedDuration === "3+") {
        return jobDuration > 3;
      }
      return jobDuration >= durationDays;
    });
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>{title}</CardTitle>
        </CardHeader>
        <CardContent className="flex justify-center items-center h-40">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>{title}</CardTitle>
          <div className="flex w-full max-w-4xl items-center justify-end gap-2 ml-4">
            <div className="flex-shrink-0 flex gap-2">
              <JobTypeFilter
                availableJobTypes={availableJobTypes}
                selectedJobType={selectedJobType}
                onJobTypeChange={onJobTypeChange || (() => {})}
                isLoading={isLoading}
              />
              <PlanTypeFilter
                availablePlanTypes={availablePlanTypes}
                selectedPlanType={selectedPlanType}
                onPlanTypeChange={onPlanTypeChange || (() => {})}
                isLoading={isLoading}
              />
              {isRunningTab && (
                <DurationFilter
                  selectedDuration={selectedDuration}
                  onDurationChange={onDurationChange || (() => {})}
                  isLoading={isLoading}
                />
              )}
            </div>
            <div className="relative w-96">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search by name, email, project, or execution ID..."
                value={localSearchTerm}
                onChange={handleSearchChange}
                className="pl-8 pr-8 w-full"
              />
              {localSearchTerm && (
                <button
                  type="button"
                  onClick={handleClearSearch}
                  className="absolute right-2 top-2.5 text-muted-foreground hover:text-foreground"
                >
                  <X className="h-4 w-4" />
                </button>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {jobs.length === 0 ? (
            <div className="text-center p-8 text-muted-foreground">
              {searchTerm ? "No matching jobs found." : "No jobs found."}
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow className="bg-gray-50 dark:bg-gray-900">
                    <TableHead className="font-semibold">User Email</TableHead>
                    <TableHead className="font-semibold">
                      Project Name
                    </TableHead>
                    <TableHead className="font-semibold">Job ID</TableHead>
                    <TableHead className="font-semibold">
                      Execution ID
                    </TableHead>
                    <TableHead className="font-semibold">
                      Creation Time
                    </TableHead>
                    <TableHead className="font-semibold">Duration</TableHead>
                    <TableHead className="font-semibold">Phase</TableHead>
                    <TableHead className="font-semibold">Status</TableHead>
                    <TableHead className="font-semibold">End Time</TableHead>
                    <TableHead className="text-right font-semibold">
                      Actions
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {getFilteredJobsByDuration(jobs).map((job) => {
                    const statusInfo = getJobStatusInfo(job);
                    const isLatestHighlightedJob =
                      latestJob && latestJob.id === job.id;
                    const duration = calculateJobDuration(job);

                    return (
                      <TableRow
                        key={job.id}
                        className={`hover:bg-gray-50 dark:hover:bg-gray-900 transition-colors duration-200 ${
                          isLatestHighlightedJob
                            ? "bg-blue-50 dark:bg-blue-950 border-l-4"
                            : ""
                        }`}
                      >
                        <TableCell className="font-medium">
                          {job.userEmail}
                        </TableCell>
                        <TableCell>{job.projectName}</TableCell>
                        <TableCell className="font-mono text-sm">
                          <button
                            onClick={() => handleJobIdClick(job.jobId)}
                            className={`text-blue-600 hover:text-blue-800 hover:underline cursor-pointer transition-colors duration-200 ${
                              isLatestHighlightedJob ? "font-bold" : ""
                            }`}
                            title="Click to filter by this Job ID"
                          >
                            {job.jobId}
                          </button>
                          {isLatestHighlightedJob && (
                            <span className="ml-2 px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full font-semibold">
                              Latest
                            </span>
                          )}
                        </TableCell>
                        <TableCell
                          className={`font-mono text-sm ${
                            isLatestHighlightedJob ? "font-bold" : ""
                          }`}
                        >
                          {job.executionId}
                        </TableCell>
                        <TableCell className="whitespace-normal">
                          <div className="flex flex-col">
                            <span>{job.creationTime.split(", ")[0]},</span>
                            <span>{job.creationTime.split(", ")[1]}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          {duration > 0
                            ? `${duration} day${duration > 1 ? "s" : ""}`
                            : "< 1 day"}
                        </TableCell>
                        <TableCell>{job.taskStatus}</TableCell>
                        <TableCell>
                          <div
                            className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-semibold ${statusInfo.bgClass}
                            `}
                          >
                            {statusInfo.icon}
                            {statusInfo.text}
                          </div>
                        </TableCell>
                        <TableCell className="whitespace-normal">
                          {job.status === "running" ? (
                            <span className="text-muted-foreground">-</span>
                          ) : (
                            <div className="flex flex-col">
                              <span>{job.endTime.split(", ")[0]},</span>
                              <span>{job.endTime.split(", ")[1]}</span>
                            </div>
                          )}
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="icon">
                                <MoreVertical className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              {job.status === "failed" ? (
                                job.isTriggered ? (
                                  // Show "Already Retriggered" as non-clickable menu item
                                  <DropdownMenuItem
                                    disabled
                                    className="text-blue-500"
                                  >
                                    <CheckCircle className="h-4 w-4 mr-2" />
                                    Already Retriggered
                                  </DropdownMenuItem>
                                ) : (
                                  // Show active "Retrigger" button
                                  <DropdownMenuItem
                                    onClick={() => handleRetriggerJob(job.id)}
                                    disabled={triggerLoading[job.id]}
                                  >
                                    {triggerLoading[job.id] ? (
                                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                                    ) : (
                                      <PlayCircle className="h-4 w-4 mr-2" />
                                    )}
                                    Retrigger
                                  </DropdownMenuItem>
                                )
                              ) : null}
                              <DropdownMenuItem
                                onClick={() => handleViewPayload(job.id)}
                                disabled={loadingPayload}
                              >
                                {loadingPayload && activeJobId === job.id ? (
                                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                                ) : (
                                  <FileText className="h-4 w-4 mr-2" />
                                )}
                                View Payload
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={() => handleRefreshStatus(job.id)}
                                disabled={refreshingStatus[job.id]}
                              >
                                {refreshingStatus[job.id] ? (
                                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                                ) : (
                                  <RefreshCw className="h-4 w-4 mr-2" />
                                )}
                                Refresh Status
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={() => handleDownloadBinary(job.id)}
                                disabled={downloadingBinary[job.id]}
                              >
                                {downloadingBinary[job.id] ? (
                                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                                ) : (
                                  <Download className="h-4 w-4 mr-2" />
                                )}
                                Download Binary
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </div>
          )}

          {/* Pagination */}
          {pagination && pagination.pages > 1 && (
            <div className="flex items-center justify-between space-x-2 py-4">
              <div className="text-sm text-muted-foreground">
                Showing {(pagination.page - 1) * pagination.limit + 1}-
                {Math.min(pagination.page * pagination.limit, pagination.total)}{" "}
                of {pagination.total} entries
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(pagination.page - 1)}
                  disabled={pagination.page === 1}
                >
                  <ChevronLeft className="h-4 w-4" />
                  <span className="sr-only">Previous Page</span>
                </Button>
                <div className="flex items-center">
                  {(() => {
                    // Logic to show a reasonable number of page buttons
                    const pageButtons = [];
                    const maxButtons = 5; // Max number of buttons to show
                    let startPage = Math.max(1, pagination.page - 2);
                    const endPage = Math.min(
                      pagination.pages,
                      startPage + maxButtons - 1
                    );

                    if (endPage - startPage < maxButtons - 1) {
                      startPage = Math.max(1, endPage - maxButtons + 1);
                    }

                    // Add first page button if not included
                    if (startPage > 1) {
                      pageButtons.push(
                        <Button
                          key={1}
                          variant={
                            1 === pagination.page ? "default" : "outline"
                          }
                          size="sm"
                          className="w-8 h-8 p-0 mx-1"
                          onClick={() => handlePageChange(1)}
                        >
                          1
                        </Button>
                      );

                      if (startPage > 2) {
                        pageButtons.push(
                          <span key="ellipsis1" className="px-2">
                            ...
                          </span>
                        );
                      }
                    }

                    // Add page buttons
                    for (let i = startPage; i <= endPage; i++) {
                      pageButtons.push(
                        <Button
                          key={i}
                          variant={
                            i === pagination.page ? "default" : "outline"
                          }
                          size="sm"
                          className="w-8 h-8 p-0 mx-1"
                          onClick={() => handlePageChange(i)}
                        >
                          {i}
                        </Button>
                      );
                    }

                    // Add last page button if not included
                    if (endPage < pagination.pages) {
                      if (endPage < pagination.pages - 1) {
                        pageButtons.push(
                          <span key="ellipsis2" className="px-2">
                            ...
                          </span>
                        );
                      }

                      pageButtons.push(
                        <Button
                          key={pagination.pages}
                          variant={
                            pagination.pages === pagination.page
                              ? "default"
                              : "outline"
                          }
                          size="sm"
                          className="w-8 h-8 p-0 mx-1"
                          onClick={() => handlePageChange(pagination.pages)}
                        >
                          {pagination.pages}
                        </Button>
                      );
                    }

                    return pageButtons;
                  })()}
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(pagination.page + 1)}
                  disabled={pagination.page === pagination.pages}
                >
                  <ChevronRight className="h-4 w-4" />
                  <span className="sr-only">Next Page</span>
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Payload Dialog */}
      <Dialog open={showPayload} onOpenChange={setShowPayload}>
        <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Job Payload</DialogTitle>
          </DialogHeader>
          <div className="mt-4 p-4 bg-gray-100 dark:bg-gray-800 rounded-md overflow-auto">
            <pre className="text-sm">
              {jobPayload
                ? JSON.stringify(jobPayload.eventData || {}, null, 2)
                : "Loading..."}
            </pre>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default JobExecutionsTable;
