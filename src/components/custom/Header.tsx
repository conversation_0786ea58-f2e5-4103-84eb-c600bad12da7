// src/components/custom/Header.tsx
import { Button } from "@/components/ui/button";
import {
  RefreshCw,
  Settings,
  ChevronDown,
  LogOut,
  Download,
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import iconBlitzy from "../../assets/images/blitzy-icon-text.svg";
import { logout, getUserEmail } from "../../utils/auth";
import { downloadLatestBinary } from "../../services/api";
import { toast } from "sonner";
import { useState } from "react";

interface HeaderProps {
  onRefresh?: () => void;
  isRefreshing?: boolean;
}

const Header = ({ onRefresh, isRefreshing = false }: HeaderProps) => {
  const userEmail = getUserEmail();
  const [isDownloading, setIsDownloading] = useState(false);

  const handleDownload = async () => {
    try {
      setIsDownloading(true);
      const blob = await downloadLatestBinary();

      // Create a download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;

      // Get filename from Content-Disposition header or use default
      const contentDisposition =
        blob.type === "application/json"
          ? JSON.parse(await blob.text()).filename
          : "binary-build.zip";

      link.setAttribute("download", contentDisposition);
      document.body.appendChild(link);
      link.click();

      // Cleanup
      link.parentNode?.removeChild(link);
      window.URL.revokeObjectURL(url);

      toast.success("Binary downloaded successfully");
    } catch (error) {
      console.error("Download failed:", error);
      toast.error("Failed to download binary. Please try again.");
    } finally {
      setIsDownloading(false);
    }
  };

  return (
    <header className="sticky top-0 z-30 flex h-16 items-center border-b bg-white dark:bg-gray-950 px-4">
      <div className="flex items-center ml-4">
        {/* Company Logo */}
        <img src={iconBlitzy} alt="Blitzy" className="h-8 w-auto" />
      </div>
      <div className="ml-auto flex items-center gap-4">
        {/* Download Button */}
        <Button
          variant="outline"
          size="sm"
          className="text-green-600 border-green-600 hover:bg-green-50 mr-2"
          onClick={handleDownload}
          disabled={isDownloading}
        >
          <Download
            className={`mr-2 h-4 w-4 ${isDownloading ? "animate-spin" : ""}`}
          />
          {isDownloading ? "Downloading..." : "Download Binary"}
        </Button>
        {/* Looker Studio Button */}
        <a
          href="https://lookerstudio.google.com/reporting/5be27fde-2a7f-4265-b601-afd5b65cf2bc"
          target="_blank"
          rel="noopener noreferrer"
        >
          <Button
            variant="outline"
            size="sm"
            className="text-blue-600 border-blue-600 hover:bg-blue-50 mr-2"
          >
            Looker Studio
          </Button>
        </a>
        {userEmail && (
          <span className="text-sm text-muted-foreground mr-2">
            {userEmail}
          </span>
        )}
        <Button
          variant="outline"
          size="sm"
          onClick={onRefresh}
          disabled={isRefreshing}
        >
          <RefreshCw
            className={`mr-2 h-4 w-4 ${isRefreshing ? "animate-spin" : ""}`}
          />
          Refresh
        </Button>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm">
              <Settings className="mr-2 h-4 w-4" />
              Settings
              <ChevronDown className="ml-2 h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={logout}>
              <LogOut className="mr-2 h-4 w-4" />
              Logout
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </header>
  );
};

export default Header;
