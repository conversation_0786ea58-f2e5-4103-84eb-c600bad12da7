// src/components/custom/Dashboard.tsx
import { useState, useEffect } from "react";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import StatCards from "./StatCards";
import JobExecutionsTable from "./JobExecutionsTable";
import {
  getJobs,
  getAllJobsStats,
  Job,
  JobStats,
  processJobStatus,
} from "../../services/api";
import { toast } from "sonner";
import TriggerCustomJob from "./TriggerCustomJob";

const Dashboard = () => {
  const [jobs, setJobs] = useState<Job[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeJobId, setActiveJobId] = useState<string | null>(null);
  const [retriggerPayload, setRetriggerPayload] = useState("");
  const [searchTerm, setSearchTerm] = useState("");
  const [activeTab, setActiveTab] = useState("all");
  const [selectedJobType, setSelectedJobType] = useState<string>("all");
  const [pagination, setPagination] = useState({
    limit: 20,
    page: 1,
    pages: 1,
    total: 0,
  });
  const [jobStats, setJobStats] = useState<JobStats>({
    total: 0,
    succeeded: 0,
    failed: 0,
    running: 0,
  });
  const [allJobTypes, setAllJobTypes] = useState<string[]>([]);
  const [selectedPlanType, setSelectedPlanType] = useState<string>("all");
  const [availablePlanTypes, setAvailablePlanTypes] = useState<string[]>([]);
  const [tabPages, setTabPages] = useState<Record<TabKey, number>>({
    succeeded: 1,
    running: 1,
    failed: 1,
  });
  const [selectedDuration, setSelectedDuration] = useState<string>("all");

  // Add type for tab keys
  const tabKeys = ["succeeded", "running", "failed"] as const;
  type TabKey = (typeof tabKeys)[number];

  // Helper function to extract job type from execution ID
  const extractJobType = (executionId: string): string => {
    const parts = executionId.split("-");
    if (parts.length >= 2) {
      // Join all parts except the last one (which is the random suffix)
      return parts.slice(0, -1).join("-");
    }
    return executionId;
  };

  // Helper function to extract unique job types from jobs
  const extractJobTypes = (jobsList: Job[]): string[] => {
    const types = new Set<string>();
    jobsList.forEach((job) => {
      const jobType = extractJobType(job.jobId);
      types.add(jobType);
    });
    return Array.from(types).sort();
  };

  // Helper to extract unique plan types from jobs
  const extractPlanTypes = (jobsList: Job[]): string[] => {
    const types = new Set<string>();
    jobsList.forEach((job) => {
      if (job.planName) types.add(job.planName);
    });
    return Array.from(types).sort();
  };

  useEffect(() => {
    // Load everything at once with the current search term, job type, and plan type
    fetchAllData(
      searchTerm,
      selectedJobType,
      selectedPlanType,
      selectedDuration
    );
  }, [searchTerm, selectedJobType, selectedPlanType, selectedDuration]);

  // Add method to refresh data for the App component
  useEffect(() => {
    // Define a function to handle refresh events from Header
    const handleAppRefresh = () => {
      fetchAllData(
        searchTerm,
        selectedJobType,
        selectedPlanType,
        selectedDuration
      );
    };

    // Add event listener
    window.addEventListener("app:refresh", handleAppRefresh);

    // Clean up
    return () => {
      window.removeEventListener("app:refresh", handleAppRefresh);
    };
  }, [searchTerm, selectedJobType, selectedPlanType, selectedDuration]);

  const fetchAllData = async (
    search: string = "",
    jobType: string = "all",
    planType: string = "all",
    duration: string = "all"
  ) => {
    setLoading(true);
    try {
      const [
        allJobsResponse,
        statsData,
        succeededJobsData,
        runningJobsData,
        failedJobsData,
        allJobsUnfilteredResponse,
      ] = await Promise.all([
        getJobs(1, 20, search, undefined, jobType, planType), // All jobs (filtered)
        getAllJobsStats(search, jobType, planType, duration), // Stats
        getJobs(1, 20, search, "SUCCESS", jobType, planType), // Succeeded jobs
        getJobs(1, 20, search, "RUNNING", jobType, planType, duration), // Running jobs with duration
        getJobs(1, 20, search, "FAILED", jobType, planType), // Failed jobs
        getJobs(1, 1000, "", undefined, "all", "all"), // All jobs, unfiltered, large page size
      ]);

      setJobs(allJobsResponse.items);
      setPagination(allJobsResponse.pagination);
      setJobStats(statsData);

      // Extract and set available job types and plan types from ALL jobs (unfiltered)
      setAllJobTypes(extractJobTypes(allJobsUnfilteredResponse.items));
      setAvailablePlanTypes(extractPlanTypes(allJobsUnfilteredResponse.items));

      setSucceededJobs(succeededJobsData.items);
      setRunningJobs(runningJobsData.items);
      setFailedJobs(failedJobsData.items);
    } catch (error) {
      toast.error("Failed to fetch data. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const fetchJobs = async (page: number = 1) => {
    try {
      setLoading(true);
      const data = await getJobs(
        page,
        20,
        searchTerm,
        undefined,
        selectedJobType,
        selectedPlanType,
        activeTab === "running" ? selectedDuration : undefined // Only pass duration for running jobs
      );
      setJobs(data.items);
      setPagination(data.pagination);
      setAvailablePlanTypes(extractPlanTypes(data.items));
    } catch (error) {
      toast.error("Failed to fetch jobs. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  // Handle tab change
  const handleTabChange = (value: string) => {
    setActiveTab(value);
    if (tabKeys.includes(value as TabKey)) {
      setTabPages((prev) => ({
        ...prev,
        [value]: 1,
      }));
    }
    if (value !== "all") {
      setPagination((prev) => ({
        ...prev,
        page: 1,
      }));
    }
  };

  // Handle filter click from stat cards
  const handleFilterClick = (filter: string) => {
    setActiveTab(filter);

    // Reset to page 1 when clicking stat cards
    setPagination((prev) => ({
      ...prev,
      page: 1,
    }));
  };

  // Handle job type filter change
  const handleJobTypeChange = (jobType: string) => {
    setSelectedJobType(jobType);
    // Reset to page 1 when changing job type filter
    setPagination((prev) => ({
      ...prev,
      page: 1,
    }));
  };

  // Handle plan type filter change
  const handlePlanTypeChange = (planType: string) => {
    setSelectedPlanType(planType);
    setPagination((prev) => ({ ...prev, page: 1 }));
  };

  // Handle duration filter change
  const handleDurationChange = (duration: string) => {
    setSelectedDuration(duration);
    // Reset to page 1 when changing duration filter
    setPagination((prev) => ({
      ...prev,
      page: 1,
    }));
  };

  // We'll use API filtering instead of client-side filtering for the tabs
  const fetchJobsByStatus = async (status: string, page: number = 1) => {
    try {
      setLoading(true);
      const statusParam =
        status === "succeeded"
          ? "SUCCESS"
          : status === "failed"
          ? "FAILED"
          : status === "running"
          ? "RUNNING"
          : undefined;

      const data = await getJobs(
        page,
        20,
        searchTerm,
        statusParam,
        selectedJobType,
        selectedPlanType,
        status === "running" ? selectedDuration : undefined // Only pass duration for running jobs
      );
      return data.items;
    } catch (error) {
      toast.error(`Failed to fetch ${status} jobs. Please try again.`);
      return [];
    } finally {
      setLoading(false);
    }
  };

  // When the activeTab changes, fetch the corresponding jobs
  useEffect(() => {
    if (activeTab !== "all" && tabKeys.includes(activeTab as TabKey)) {
      fetchJobsByStatus(activeTab, tabPages[activeTab as TabKey]).then(
        (filteredJobs) => {
          if (activeTab === "succeeded") {
            setSucceededJobs(filteredJobs);
          } else if (activeTab === "failed") {
            setFailedJobs(filteredJobs);
          } else if (activeTab === "running") {
            setRunningJobs(filteredJobs);
          }
        }
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeTab, searchTerm, selectedJobType, selectedPlanType, tabPages]);

  // State for filtered jobs by status
  const [succeededJobs, setSucceededJobs] = useState<Job[]>([]);
  const [failedJobs, setFailedJobs] = useState<Job[]>([]);
  const [runningJobs, setRunningJobs] = useState<Job[]>([]);

  // Format job data for display
  const formatJobForDisplay = (job: Job) => {
    // Extract job_id from eventData, fallback to jobId if not available
    const eventDataJobId = job.eventData?.job_id;
    const displayJobId = eventDataJobId || job.jobId || "N/A";

    return {
      id: job.id,
      jobId: displayJobId, // This will now show the job_id from eventData
      executionId: job.jobId, // Keep the original jobId as executionId
      userEmail: job.userEmail || "N/A",
      projectName: job.projectName || "N/A",
      creationTime: new Date(job.createdAt).toLocaleString(),
      taskStatus: job.jobPhase,
      endTime: new Date(job.updatedAt).toLocaleString(),
      status: processJobStatus(job),
      isTriggered: job.isTriggered,
      jobType: extractJobType(job.jobId),
      // We don't include firstName and lastName as columns but they're used for search
      userFirstName: job.userFirstName,
      userLastName: job.userLastName,
    };
  };

  const handleJobUpdate = () => {
    // Refresh both jobs and stats with current search term
    fetchAllData(
      searchTerm,
      selectedJobType,
      selectedPlanType,
      selectedDuration
    );
  };

  const handlePageChange = (page: number) => {
    fetchJobs(page);
  };

  const handleSearch = (term: string) => {
    setSearchTerm(term);
  };

  // Get jobs for display (no client-side job type filtering needed)
  const getFilteredJobs = (jobsList: Job[]) => {
    return jobsList.map(formatJobForDisplay);
  };

  // Handler for page change in filtered tabs
  const handleTabPageChange = (tab: TabKey, page: number) => {
    setTabPages((prev) => ({ ...prev, [tab]: page }));
    fetchJobsByStatus(tab, page).then((jobs) => {
      if (tab === "succeeded") setSucceededJobs(jobs);
      if (tab === "running") setRunningJobs(jobs);
      if (tab === "failed") setFailedJobs(jobs);
    });
  };

  // Reset filtered tab pages when filters change
  useEffect(() => {
    setTabPages({ succeeded: 1, running: 1, failed: 1 });
  }, [searchTerm, selectedJobType, selectedPlanType]);

  return (
    <main className="flex-1 p-4 md:p-6">
      <div className="mb-6 flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Cloud Run Jobs</h2>
          <p className="text-muted-foreground">
            Monitor and manage your GCP Cloud Run jobs
          </p>
        </div>
        <div className="self-start">
          <TriggerCustomJob availableJobTypes={allJobTypes} />
        </div>
      </div>

      {/* Stats cards - now using the total counts from all jobs and made clickable */}
      <StatCards
        jobCount={jobStats.total}
        succeededJobsCount={jobStats.succeeded}
        failedJobsCount={jobStats.failed}
        runningJobsCount={jobStats.running}
        isLoading={loading}
        onFilterClick={handleFilterClick}
      />

      {/* Jobs tabs */}
      <Tabs
        value={activeTab}
        onValueChange={handleTabChange}
        className="space-y-4"
      >
        <TabsList>
          <TabsTrigger value="all">All Jobs</TabsTrigger>
          <TabsTrigger value="succeeded">Succeeded</TabsTrigger>
          <TabsTrigger value="running">Running</TabsTrigger>
          <TabsTrigger value="failed">Failed</TabsTrigger>
        </TabsList>

        <TabsContent value="all">
          <JobExecutionsTable
            jobs={getFilteredJobs(jobs)}
            activeJobId={activeJobId}
            setActiveJobId={setActiveJobId}
            retriggerPayload={retriggerPayload}
            setRetriggerPayload={setRetriggerPayload}
            onRefreshSuccess={handleJobUpdate}
            isLoading={loading}
            pagination={pagination}
            onPageChange={handlePageChange}
            searchTerm={searchTerm}
            onSearch={handleSearch}
            availableJobTypes={allJobTypes}
            selectedJobType={selectedJobType}
            onJobTypeChange={handleJobTypeChange}
            availablePlanTypes={availablePlanTypes}
            selectedPlanType={selectedPlanType}
            onPlanTypeChange={handlePlanTypeChange}
            selectedDuration={selectedDuration}
            onDurationChange={handleDurationChange}
            isRunningTab={false}
          />
        </TabsContent>

        <TabsContent value="succeeded">
          <JobExecutionsTable
            jobs={getFilteredJobs(succeededJobs)}
            activeJobId={activeJobId}
            setActiveJobId={setActiveJobId}
            retriggerPayload={retriggerPayload}
            setRetriggerPayload={setRetriggerPayload}
            onRefreshSuccess={handleJobUpdate}
            title="Succeeded Job Executions"
            isLoading={loading}
            pagination={{
              ...pagination,
              page: tabPages.succeeded,
              total: jobStats.succeeded,
            }}
            onPageChange={(page) => handleTabPageChange("succeeded", page)}
            searchTerm={searchTerm}
            onSearch={handleSearch}
            availableJobTypes={allJobTypes}
            selectedJobType={selectedJobType}
            onJobTypeChange={handleJobTypeChange}
            availablePlanTypes={availablePlanTypes}
            selectedPlanType={selectedPlanType}
            onPlanTypeChange={handlePlanTypeChange}
            selectedDuration={selectedDuration}
            onDurationChange={handleDurationChange}
            isRunningTab={false}
          />
        </TabsContent>

        <TabsContent value="running">
          <JobExecutionsTable
            jobs={getFilteredJobs(runningJobs)}
            activeJobId={activeJobId}
            setActiveJobId={setActiveJobId}
            retriggerPayload={retriggerPayload}
            setRetriggerPayload={setRetriggerPayload}
            onRefreshSuccess={handleJobUpdate}
            title="Running Job Executions"
            isLoading={loading}
            pagination={{
              ...pagination,
              page: tabPages.running,
              total: jobStats.running,
            }}
            onPageChange={(page) => handleTabPageChange("running", page)}
            searchTerm={searchTerm}
            onSearch={handleSearch}
            availableJobTypes={allJobTypes}
            selectedJobType={selectedJobType}
            onJobTypeChange={handleJobTypeChange}
            availablePlanTypes={availablePlanTypes}
            selectedPlanType={selectedPlanType}
            onPlanTypeChange={handlePlanTypeChange}
            selectedDuration={selectedDuration}
            onDurationChange={handleDurationChange}
            isRunningTab={true}
          />
        </TabsContent>

        <TabsContent value="failed">
          <JobExecutionsTable
            jobs={getFilteredJobs(failedJobs)}
            activeJobId={activeJobId}
            setActiveJobId={setActiveJobId}
            retriggerPayload={retriggerPayload}
            setRetriggerPayload={setRetriggerPayload}
            onRefreshSuccess={handleJobUpdate}
            title="Failed Job Executions"
            isLoading={loading}
            pagination={{
              ...pagination,
              page: tabPages.failed,
              total: jobStats.failed,
            }}
            onPageChange={(page) => handleTabPageChange("failed", page)}
            searchTerm={searchTerm}
            onSearch={handleSearch}
            availableJobTypes={allJobTypes}
            selectedJobType={selectedJobType}
            onJobTypeChange={handleJobTypeChange}
            availablePlanTypes={availablePlanTypes}
            selectedPlanType={selectedPlanType}
            onPlanTypeChange={handlePlanTypeChange}
            selectedDuration={selectedDuration}
            onDurationChange={handleDurationChange}
            isRunningTab={false}
          />
        </TabsContent>
      </Tabs>
    </main>
  );
};

export default Dashboard;
