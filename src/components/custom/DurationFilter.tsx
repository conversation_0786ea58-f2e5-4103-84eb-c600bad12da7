import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface DurationFilterProps {
  selectedDuration: string;
  onDurationChange: (duration: string) => void;
  isLoading: boolean;
}

const DurationFilter = ({
  selectedDuration,
  onDurationChange,
  isLoading,
}: DurationFilterProps) => {
  if (isLoading) {
    return (
      <div className="h-10 w-48 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
    );
  }

  return (
    <Select value={selectedDuration} onValueChange={onDurationChange}>
      <SelectTrigger className="w-48">
        <SelectValue placeholder="Filter by duration..." />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="all">All Durations</SelectItem>
        <SelectItem value="1">More than 1 day</SelectItem>
        <SelectItem value="2">More than 2 days</SelectItem>
        <SelectItem value="3">More than 3 days</SelectItem>
        <SelectItem value="3+">Greater than 3 days</SelectItem>
      </SelectContent>
    </Select>
  );
};

export default DurationFilter;
