// src/components/custom/JobTypeFilter.tsx
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface JobTypeFilterProps {
  availableJobTypes: string[];
  selectedJobType: string;
  onJobTypeChange: (jobType: string) => void;
  isLoading: boolean;
}

const JobTypeFilter = ({
  availableJobTypes,
  selectedJobType,
  onJobTypeChange,
  isLoading,
}: JobTypeFilterProps) => {
  if (isLoading) {
    return (
      <div className="h-10 w-48 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
    );
  }

  return (
    <Select value={selectedJobType} onValueChange={onJobTypeChange}>
      <SelectTrigger className="w-48">
        <SelectValue placeholder="Filter by type..." />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="all">All Job Types</SelectItem>
        {availableJobTypes.map((jobType) => (
          <SelectItem key={jobType} value={jobType}>
            {jobType}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
};

export default JobTypeFilter;
