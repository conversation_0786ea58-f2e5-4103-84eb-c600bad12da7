import { But<PERSON> } from "../ui/button";
import {
  <PERSON><PERSON>Des<PERSON>,
  DialogFooter,
  <PERSON><PERSON>T<PERSON>le,
  DialogTrigger,
} from "../ui/dialog";
import { DialogContent, DialogHeader } from "../ui/dialog";
import { Dialog } from "../ui/dialog";
import { Textarea } from "../ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";
import { useState } from "react";
import { toast } from "sonner";
import { triggerCustomJob } from "../../services/api";

interface TriggerCustomJobProps {
  availableJobTypes: string[];
}

export default function TriggerCustomJob({
  availableJobTypes = [],
}: TriggerCustomJobProps) {
  const [selectedJobType, setSelectedJobType] = useState<string>("");
  const [selectedJobTypeError, setSelectedJobTypeError] = useState<
    string | null
  >(null);
  const [jsonString, setJsonString] = useState<string>("");
  const [jsonError, setJsonError] = useState<string | null>(null);
  const [triggerLoading, setTriggerLoading] = useState<boolean>(false);
  const [open, setOpen] = useState(false);

  const onJsonChange = (value: string) => {
    setJsonString(value);
    validateJson(value);
  };

  const validateJson = (value: string) => {
    let isValid = true;
    try {
      if (value) {
        JSON.parse(value);
        setJsonError(null);
        isValid = true;
      } else {
        setJsonError("JSON payload is required");
        isValid = false;
      }
      return isValid;
    } catch (e) {
      const error = e as Error;
      console.log(error.message || "Invalid JSON format");
      setJsonError("Invalid JSON format");
      isValid = false;
      return isValid;
    }
  };

  const handleJobTypeChange = (value: string) => {
    setSelectedJobType(value);
    validateJobType(value);
  };

  const validateJobType = (value: string) => {
    let isValid = true;
    if (!value) {
      setSelectedJobTypeError("Job type is required");
      isValid = false;
    } else {
      setSelectedJobTypeError(null);
      isValid = true;
    }
    return isValid;
  };

  const handleTriggerJob = async () => {
    const isValid =
      validateJobType(selectedJobType) && validateJson(jsonString);
    if (isValid) {
      const payload = JSON.parse(jsonString);
      setTriggerLoading(true);
      try {
        await triggerCustomJob(selectedJobType, payload);
        toast.success("Job triggered successfully!");
        setOpen(false);
      } catch (error: any) {
        toast.error(
          error?.message || "Something went wrong. Please try again."
        );
      } finally {
        setTriggerLoading(false);
      }
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline">Trigger Custom Job</Button>
      </DialogTrigger>
      <DialogContent aria-describedby="trigger-job-dialog-description">
        <DialogHeader>
          <DialogTitle>Trigger Custom Job with Payload</DialogTitle>
        </DialogHeader>
        <DialogDescription className="sr-only">
          Trigger a new job execution.
        </DialogDescription>
        <div className="space-y-2">
          <p className="text-sm text-muted-foreground">Select the job type</p>
          <Select value={selectedJobType} onValueChange={handleJobTypeChange}>
            <SelectTrigger className="w-full">
              <SelectValue className="w-full" placeholder="Select job type" />
            </SelectTrigger>
            <SelectContent>
              {availableJobTypes.map((jobType) => (
                <SelectItem key={jobType} value={jobType}>
                  {jobType}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {selectedJobTypeError && (
            <p className="text-xs text-red-600">{selectedJobTypeError}</p>
          )}
        </div>
        <div className="space-y-2">
          <p className="text-sm text-muted-foreground">
            Enter the JSON payload for the job
          </p>
          <Textarea
            placeholder="Enter JSON payload here..."
            className="font-mono min-h-[200px]"
            value={jsonString}
            onChange={(e) => onJsonChange(e.target.value)}
          />
          {jsonError && <p className="text-xs text-red-600">{jsonError}</p>}
        </div>
        <DialogFooter>
          <Button
            variant="outline"
            onClick={handleTriggerJob}
            disabled={triggerLoading}
          >
            {triggerLoading && (
              <svg
                className="animate-spin mr-2 h-4 w-4 text-current"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                ></circle>
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"
                ></path>
              </svg>
            )}
            {triggerLoading ? "Triggering..." : "Trigger Job"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
