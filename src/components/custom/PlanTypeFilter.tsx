import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface PlanTypeFilterProps {
  availablePlanTypes: string[];
  selectedPlanType: string;
  onPlanTypeChange: (planType: string) => void;
  isLoading: boolean;
}

const PLAN_TYPE_OPTIONS = [
  { label: "All Plans", value: "all" },
  { label: "FREE", value: "FREE" },
  { label: "PRO", value: "PRO" },
  { label: "ENTERPRISE", value: "ENTERPRISE" },
  { label: "TEAMS", value: "TEAMS" },
];

const PlanTypeFilter = ({
  selectedPlanType,
  onPlanTypeChange,
  isLoading,
}: PlanTypeFilterProps) => {
  if (isLoading) {
    return (
      <div className="h-10 w-48 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
    );
  }

  return (
    <Select value={selectedPlanType} onValueChange={onPlanTypeChange}>
      <SelectTrigger className="w-48">
        <SelectValue placeholder="Filter by plan..." />
      </SelectTrigger>
      <SelectContent>
        {PLAN_TYPE_OPTIONS.map((option) => (
          <SelectItem key={option.value} value={option.value}>
            {option.label}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
};

export default PlanTypeFilter;
