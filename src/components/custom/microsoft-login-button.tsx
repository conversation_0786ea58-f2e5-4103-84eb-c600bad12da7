import {useCallback, useState} from 'react';
import {useNavigate} from 'react-router-dom';
import {useAuthContext} from '@/context/auth-context';
import {useToast} from '@/hooks/use-toast';
import {logGAEvent} from '@/lib/utils';
import microsoftLogo from '/images/microsoft-logo.png';
import {FirebaseError} from 'firebase/app';
import {LinkAccountsDialog} from '@/panel/workspace/link-accounts-dialog';
import AuthenticatingUserModal from '@/modal/authenticating-user-modal';

interface MicrosoftLoginButtonProps {
    className?: string;
    buttonText?: string;
    onSuccess?: () => void;
    successPath?: string;
    successSearch?: string;
}

export function MicrosoftLoginButton({
    className = 'flex justify-center items-center gap-[8px] sm:gap-[12px] self-stretch rounded-[32px] bg-[var(--background-bg-tertiary,#F5F5F5)] px-[16px] sm:px-[20px] py-[10px] sm:py-[8px]',
    buttonText = 'Continue with Microsoft',
    onSuccess,
    successPath = '/workspace',
    successSearch,
}: MicrosoftLoginButtonProps) {
    const {microsoftLogin} = useAuthContext();
    const {toast, dismiss} = useToast();
    const navigateTo = useNavigate();

    const [isLinkAccountsDialogOpen, setIsLinkAccountsDialogOpen] =
        useState(false);
    const [existingAccountEmail, setExistingAccountEmail] =
        useState<string>('');
    const [existingAccountProvider, setExistingAccountProvider] = useState<
        'google' | 'microsoft'
    >('microsoft');
    const [isMicrosoftLoginLoading, setIsMicrosoftLoginLoading] =
        useState(false);

    const handleMicrosoftLogin = useCallback(async () => {
        if (microsoftLogin) {
            dismiss();
            setIsMicrosoftLoginLoading(true);

            const timeoutId = setTimeout(() => {}, 3000);

            const handlePopupClose = () => {
                setIsMicrosoftLoginLoading(false);
                clearTimeout(timeoutId);
                toast({
                    variant: 'default',
                    description: 'Sign in was cancelled. Please try again.',
                });
            };

            try {
                await microsoftLogin(handlePopupClose);

                // Clear the timeout since we got a result
                clearTimeout(timeoutId);

                logGAEvent('sign_in', {
                    method: 'microsoft',
                });

                if (onSuccess) {
                    onSuccess();
                } else {
                    navigateTo(
                        {
                            pathname: successPath,
                            search: successSearch,
                        },
                        {replace: true},
                    );
                }
                setIsMicrosoftLoginLoading(false);
            } catch (error) {
                // Clear the timeout since we got an error
                clearTimeout(timeoutId);
                setIsMicrosoftLoginLoading(false);

                if (error instanceof FirebaseError) {
                    let email: string | undefined;
                    switch (error.code) {
                        case 'auth/popup-closed-by-user':
                            toast({
                                variant: 'default',
                                description:
                                    'Sign in was cancelled. Please try again.',
                            });
                            break;
                        case 'auth/popup-blocked':
                            toast({
                                variant: 'default',
                                description:
                                    'Pop-up was blocked. Please allow pop-ups for this site and try again.',
                            });
                            break;
                        case 'auth/cancelled-popup-request':
                            toast({
                                variant: 'default',
                                description:
                                    'Another sign-in window is already open. Please close it and try again.',
                            });
                            break;
                        case 'auth/account-exists-with-different-credential':
                            email = error.customData?.email as string;
                            if (email) {
                                setIsLinkAccountsDialogOpen(true);
                                setExistingAccountEmail(email);
                                setExistingAccountProvider('microsoft');
                            }
                            break;
                        case 'auth/user-cancelled':
                            toast({
                                variant: 'default',
                                description:
                                    'Sign in was cancelled by the user. Please try again.',
                            });
                            break;
                        case 'auth/invalid-credential':
                            toast({
                                variant: 'destructive',
                                description:
                                    'Unable to access Microsoft profile. Please try again or use a different account.',
                            });
                            break;
                        default:
                            console.error('Microsoft login error:', error);
                            toast({
                                variant: 'destructive',
                                description:
                                    'An error occurred during sign in. Please try again.',
                            });
                    }
                } else {
                    console.error(
                        'Unexpected error during Microsoft login:',
                        error,
                    );
                    toast({
                        variant: 'destructive',
                        description:
                            'An unexpected error occurred. Please try again.',
                    });
                }
            }
        }
    }, [
        microsoftLogin,
        navigateTo,
        toast,
        dismiss,
        onSuccess,
        successPath,
        successSearch,
    ]);

    return (
        <>
            <div
                role="button"
                onClick={handleMicrosoftLogin}
                className={className}>
                <img
                    className="w-[20px] h-[20px]"
                    src={microsoftLogo}
                    alt="microsoft"
                />
                <div className="text-[var(--text-text-secondary,#333)] text-[16px] font-semibold">
                    {buttonText}
                </div>
            </div>

            <LinkAccountsDialog
                isOpen={isLinkAccountsDialogOpen}
                onClose={() => setIsLinkAccountsDialogOpen(false)}
                email={existingAccountEmail}
                provider={existingAccountProvider}
            />

            {/* Show loading modal during Microsoft authentication */}
            {isMicrosoftLoginLoading && <AuthenticatingUserModal />}
        </>
    );
}
