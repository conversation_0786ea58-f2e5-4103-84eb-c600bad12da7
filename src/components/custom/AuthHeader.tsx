// src/components/custom/AuthHeader.tsx
import iconBlitzy from "../../assets/images/blitzy-icon-text.svg";

const AuthHeader = () => {
  return (
    <header className="sticky top-0 z-30 flex h-16 items-center border-b bg-white dark:bg-gray-950 px-4">
      <div className="flex items-center ml-4">
        {/* Company Logo */}
        <img src={iconBlitzy} alt="Blitzy" className="h-8 w-auto" />
      </div>
    </header>
  );
};

export default AuthHeader;
