// src/components/custom/StatCards.tsx
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";

interface StatCardsProps {
  jobCount: number;
  succeededJobsCount: number;
  failedJobsCount: number;
  runningJobsCount: number;
  isLoading: boolean;
  onFilterClick?: (filter: string) => void;
}

const StatCards = ({
  jobCount,
  succeededJobsCount,
  failedJobsCount,
  runningJobsCount,
  isLoading,
  onFilterClick,
}: StatCardsProps) => {
  if (isLoading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 mb-6">
        {[1, 2, 3, 4].map((i) => (
          <Card key={i}>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium h-5 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
            </CardHeader>
            <CardContent>
              <div className="h-8 w-16 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  const cards = [
    {
      title: "Total Jobs",
      count: jobCount,
      color: "text-foreground",
      filter: "all",
    },
    {
      title: "Succeeded Jobs",
      count: succeededJobsCount,
      color: "text-green-600",
      filter: "succeeded",
    },
    {
      title: "Running Jobs",
      count: runningJobsCount,
      color: "text-blue-600",
      filter: "running",
    },
    {
      title: "Failed Jobs",
      count: failedJobsCount,
      color: "text-red-600",
      filter: "failed",
    },
  ];

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 mb-6">
      {cards.map((card) => (
        <Card
          key={card.filter}
          className={
            onFilterClick
              ? "cursor-pointer transition-all hover:shadow-md hover:border-primary/50"
              : ""
          }
          onClick={() => onFilterClick && onFilterClick(card.filter)}
        >
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">{card.title}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${card.color}`}>
              {card.count}
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

export default StatCards;
