import {Extension} from '@tiptap/core';
import {Plugin, Plugin<PERSON><PERSON>} from '@tiptap/pm/state';

export const MarkdownPasteHandler = Extension.create({
    name: 'markdownPasteHandler',

    addProseMirrorPlugins() {
        return [
            new Plugin({
                key: new PluginKey('markdownPasteHandler'),
                props: {
                    handlePaste: (view, event) => {
                        // Only process in WYSIWYG mode (not plain text)
                        if (!view.editable) return false;

                        // Check if we're inside a code block - if so, let default paste behavior work
                        const {state} = view;
                        const {$from} = state.selection;
                        if ($from.parent?.type.name === 'codeBlock') {
                            return false; // Let default paste behavior handle it
                        }

                        const clipboardData = event.clipboardData;
                        if (!clipboardData) return false;

                        // Get the plain text from clipboard
                        const text = clipboardData.getData('text/plain');
                        if (!text || text.length === 0) return false;

                        // Check if the text looks like markdown
                        const markdownPatterns = [
                            /^#{1,6}\s+.+/m, // Headers
                            /\*\*[^*]+\*\*/, // Bold
                            /\*[^*]+\*/, // Italic
                            /`[^`]+`/, // Inline code
                            /^\s*[-*+]\s+/m, // Unordered lists
                            /^\s*\d+\.\s+/m, // Ordered lists
                            /^\s*>\s+/m, // Blockquotes
                            /```[\s\S]*?```/, // Code blocks
                            /^\s*\|.+\|/m, // Tables
                            /\[.+\]\(.+\)/, // Links
                            /!\[.*\]\(.+\)/, // Images
                            /^---+$/m, // Horizontal rules
                        ];

                        const looksLikeMarkdown = markdownPatterns.some(
                            pattern => pattern.test(text),
                        );

                        try {
                            // Prevent the default paste behavior
                            event.preventDefault();

                            // Use the editor's setContent command which will properly parse markdown
                            setTimeout(() => {
                                const {from, to} = this.editor.state.selection;

                                // If there's a selection, delete it first
                                if (from !== to) {
                                    this.editor.commands.deleteSelection();
                                }

                                if (looksLikeMarkdown) {
                                    // Get current content
                                    const currentContent =
                                        this.editor.storage.markdown.getMarkdown();

                                    // Calculate where to insert the new content
                                    const beforeCursor = currentContent.slice(
                                        0,
                                        this.editor.state.selection.from,
                                    );
                                    const afterCursor = currentContent.slice(
                                        this.editor.state.selection.to,
                                    );

                                    // Combine the content
                                    const newContent =
                                        beforeCursor + text + afterCursor;

                                    // Set the entire content to trigger proper markdown parsing
                                    this.editor.commands.setContent(newContent);
                                } else {
                                    this.editor.commands.setContent(text);
                                }
                            }, 0);

                            return true;
                        } catch (error) {
                            console.warn(
                                'Failed to parse markdown content:',
                                error,
                            );
                            // Fall back to default paste behavior
                            return false;
                        }

                        // Not markdown-like content, use default behavior
                        return false;
                    },
                },
            }),
        ];
    },
});
