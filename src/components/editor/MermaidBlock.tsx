import React, {useEffect, useRef, useState, useCallback} from 'react';
import {NodeViewWrapper, NodeViewProps} from '@tiptap/react';
import mermaid from 'mermaid';
import {AlertCircle, Check, Eye} from 'lucide-react';
import {Edit, Copy, ExpandAlt, CollapseAlt} from './icons';

// Initialize mermaid with a unique ID generator
let mermaidIdCounter = 0;

export const MermaidBlock: React.FC<NodeViewProps> = ({
    node,
    updateAttributes,
    selected,
}) => {
    const [isEditing, setIsEditing] = useState(false);
    const [tempContent, setTempContent] = useState(node.attrs.content || '');
    const [error, setError] = useState<string | null>(null);
    const [copied, setCopied] = useState(false);
    const [isFullscreen, setIsFullscreen] = useState(false);
    const containerRef = useRef<HTMLDivElement>(null);
    const previewRef = useRef<HTMLDivElement>(null);
    const mermaidIdRef = useRef<string>(
        `mermaid-${Date.now()}-${mermaidIdCounter++}`,
    );

    // Initialize mermaid once
    useEffect(() => {
        mermaid.initialize({
            startOnLoad: false,
            theme: 'default',
            securityLevel: 'loose',
            fontFamily: 'Inter, system-ui, sans-serif',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true,
                curve: 'basis',
                padding: 15,
            },
            sequence: {
                diagramMarginX: 50,
                diagramMarginY: 10,
                actorMargin: 50,
                width: 150,
                height: 65,
                boxMargin: 10,
                boxTextMargin: 5,
                noteMargin: 10,
                messageMargin: 35,
                mirrorActors: true,
            },
            gantt: {
                fontSize: 11,
                sectionFontSize: 11,
                numberSectionStyles: 4,
                axisFormat: '%Y-%m-%d',
            },
        });
    }, []);

    // Render mermaid diagram
    const renderMermaid = useCallback(
        async (content: string, targetRef: HTMLDivElement | null) => {
            if (!targetRef || !content.trim()) return;

            let tempDiv: HTMLDivElement | null = null;

            try {
                // Clear any previous content
                targetRef.innerHTML = '';
                setError(null);

                // Generate a new unique ID for each render
                const renderElementId = `${mermaidIdRef.current}-render-${Date.now()}`;

                // Create a temporary div for mermaid to render into
                tempDiv = document.createElement('div');
                tempDiv.id = renderElementId;
                tempDiv.style.display = 'none';
                document.body.appendChild(tempDiv);

                // Parse and render the diagram
                await mermaid.parse(content);
                const {svg} = await mermaid.render(renderElementId, content);

                // Insert the SVG into our target container
                if (targetRef) {
                    targetRef.innerHTML = svg;

                    // Make SVG responsive and properly sized
                    const svgElement = targetRef.querySelector('svg');
                    if (svgElement) {
                        svgElement.removeAttribute('height');
                        svgElement.removeAttribute('width');
                        svgElement.style.maxWidth = '100%';
                        svgElement.style.maxHeight = '400px';
                        svgElement.style.height = 'auto';
                        svgElement.style.width = 'auto';
                    }
                }
            } catch (err) {
                console.error('Mermaid rendering error:', err);
                const errorMessage =
                    err instanceof Error ? err.message : 'Unknown error';
                setError(errorMessage);

                if (targetRef) {
                    targetRef.innerHTML = `
          <div class="mermaid-error">
            <div class="error-icon">⚠️</div>
            <div class="error-message">
              <strong>Diagram Error:</strong><br/>
              ${errorMessage}
            </div>
          </div>
        `;
                }
            } finally {
                // Clean up the temporary div safely
                if (tempDiv && document.body.contains(tempDiv)) {
                    try {
                        document.body.removeChild(tempDiv);
                    } catch (removeError) {
                        console.warn(
                            'Failed to remove temporary div:',
                            removeError,
                        );
                    }
                }
            }
        },
        [],
    );

    // Render diagram when content changes and not editing
    useEffect(() => {
        if (!isEditing && containerRef.current) {
            renderMermaid(node.attrs.content, containerRef.current);
        }
    }, [node.attrs.content, isEditing, renderMermaid]);

    // Live preview in edit mode
    useEffect(() => {
        if (isEditing && previewRef.current) {
            // Debounce the preview rendering
            const timeoutId = setTimeout(() => {
                renderMermaid(tempContent, previewRef.current);
            }, 500);

            return () => clearTimeout(timeoutId);
        }
    }, [tempContent, isEditing, renderMermaid]);

    const handleSave = () => {
        updateAttributes({content: tempContent});
        setIsEditing(false);
    };

    const handleCancel = () => {
        setTempContent(node.attrs.content || '');
        setIsEditing(false);
        setError(null);
    };

    const handleCopy = () => {
        navigator.clipboard.writeText(node.attrs.content || tempContent);
        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
    };

    const toggleFullscreen = () => {
        setIsFullscreen(!isFullscreen);
    };

    // Example templates
    const templates = [
        {
            name: 'Flowchart',
            code: `graph TD
    A[Start] --> B{Is it?}
    B -->|Yes| C[OK]
    B -->|No| D[End]`,
        },
        {
            name: 'Sequence',
            code: `sequenceDiagram
    Alice->>John: Hello John
    John-->>Alice: Hi Alice!`,
        },
        {
            name: 'Gantt',
            code: `gantt
    title Project Schedule
    dateFormat YYYY-MM-DD
    section Phase 1
    Task 1 :a1, 2024-01-01, 30d
    Task 2 :after a1, 20d`,
        },
        {
            name: 'Pie Chart',
            code: `pie title Pets
    "Dogs" : 386
    "Cats" : 85
    "Rats" : 15`,
        },
    ];
    return (
        <NodeViewWrapper
            className={`mermaid-block ${selected ? 'selected' : ''} ${isFullscreen ? 'fullscreen' : ''}`}>
            <div className="mermaid-toolbar">
                <div className="mermaid-toolbar-buttons">
                    <button
                        onClick={() => {
                            if (!isEditing) {
                                setTempContent(node.attrs.content || '');
                            }
                            setIsEditing(!isEditing);
                        }}
                        contentEditable={false}
                        className="mermaid-toolbar-button"
                        title={isEditing ? 'Preview' : 'Edit'}
                        type="button">
                        {isEditing ? (
                            <Eye size={24} color="#666666" />
                        ) : (
                            <Edit width={24} height={24} color="#666666" />
                        )}
                    </button>

                    {!isEditing && (
                        <>
                            <button
                                onClick={handleCopy}
                                className="mermaid-toolbar-button"
                                title="Copy diagram code"
                                contentEditable={false}
                                type="button">
                                {copied ? (
                                    <Check size={24} color="#666666" />
                                ) : (
                                    <Copy
                                        width={24}
                                        height={24}
                                        color="#666666"
                                    />
                                )}
                            </button>
                            {!isFullscreen ? (
                                <button
                                    onClick={toggleFullscreen}
                                    className="mermaid-toolbar-button"
                                    title="Toggle fullscreen"
                                    contentEditable={false}
                                    type="button">
                                    <ExpandAlt
                                        width={24}
                                        height={24}
                                        color="#666666"
                                    />
                                </button>
                            ) : (
                                <button
                                    onClick={() => {
                                        setIsFullscreen(false);
                                    }}
                                    className="mermaid-toolbar-button"
                                    title="Close"
                                    contentEditable={false}
                                    type="button">
                                    <CollapseAlt
                                        width={24}
                                        height={24}
                                        color="#666666"
                                    />
                                </button>
                            )}
                        </>
                    )}
                </div>

                {error && !isEditing && (
                    <div className="toolbar-error">
                        <AlertCircle size={16} />
                        <span>Invalid diagram</span>
                    </div>
                )}
            </div>

            {isEditing ? (
                <div className="mermaid-edit-container">
                    <div className="mermaid-editor-section">
                        <div className="editor-header">
                            <h4 className="!text-[16px]">Mermaid code</h4>
                            <div className="template-selector">
                                <select
                                    onChange={e => {
                                        if (e.target.value) {
                                            setTempContent(e.target.value);
                                            e.target.value = '';
                                        }
                                    }}
                                    className="template-dropdown">
                                    <option value="">Select template</option>
                                    {templates.map((t, i) => (
                                        <option key={i} value={t.code}>
                                            {t.name}
                                        </option>
                                    ))}
                                </select>
                                <svg
                                    className="template-chevron"
                                    width="24"
                                    height="24"
                                    viewBox="0 0 24 24"
                                    fill="none"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <path
                                        d="M6.29004 8.29004L12 14L17.71 8.29004"
                                        stroke="#999999"
                                        strokeWidth="2"
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                    />
                                </svg>
                            </div>
                        </div>
                        <textarea
                            value={tempContent}
                            onChange={e => setTempContent(e.target.value)}
                            className="mermaid-textarea"
                            rows={12}
                            placeholder="Enter Mermaid diagram code..."
                            spellCheck={false}
                        />
                        <div className="mermaid-actions">
                            <button
                                onClick={handleCancel}
                                contentEditable={false}
                                className="bg-white rounded-[2rem] border-[1px] border-[#5B39F3] text-[#5B39F3] text-[18px] font-semibold">
                                Cancel
                            </button>
                            <button
                                onClick={handleSave}
                                contentEditable={false}
                                className="bg-[#5B39F3] rounded-[2rem] border-[1px] border-[#5B39F3] text-white text-[18px] font-semibold">
                                Save changes
                            </button>
                        </div>
                    </div>

                    <div className="mermaid-preview-section">
                        <div className="preview-header">
                            <h4 className="!text-[16px]">Live preview</h4>
                            {error && (
                                <span className="preview-error">
                                    <AlertCircle size={14} />
                                    Error in diagram
                                </span>
                            )}
                        </div>
                        <div
                            ref={previewRef}
                            className="mermaid-preview-content"
                        />
                    </div>
                </div>
            ) : (
                <div ref={containerRef} className="mermaid-preview" />
            )}
        </NodeViewWrapper>
    );
};
