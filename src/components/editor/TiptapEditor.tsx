import {
    useEffect,
    useState,
    forwardRef,
    useImperative<PERSON>andle,
    useRef,
    useCallback,
} from 'react';
import {useEditor, EditorContent} from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Typography from '@tiptap/extension-typography';
import Table from '@tiptap/extension-table';
import TableRow from '@tiptap/extension-table-row';
import TableCell from '@tiptap/extension-table-cell';
import TableHeader from '@tiptap/extension-table-header';
import TextAlign from '@tiptap/extension-text-align';
import Link from '@tiptap/extension-link';
import Image from '@tiptap/extension-image';
import Heading from '@tiptap/extension-heading';
import {all, createLowlight} from 'lowlight';
import {Markdown} from 'tiptap-markdown';
import {EditorToolbar} from './EditorToolbar';
import {TableControls} from './TableControls';
import {MermaidExtension} from './extensions/MermaidExtension';
import {CustomCodeBlock} from './extensions/CustomCodeBlock';
import {RawTextBlock} from './extensions/RawTextBlock';
import {MarkdownPasteHandler} from './extensions/MarkdownPasteHandler';
import './TiptapEditor.css';

// Create lowlight instance
const lowlight = createLowlight(all);

// Import and register additional languages (only those that exist in highlight.js)
import javascript from 'highlight.js/lib/languages/javascript';
import typescript from 'highlight.js/lib/languages/typescript';
import python from 'highlight.js/lib/languages/python';
import java from 'highlight.js/lib/languages/java';
import cpp from 'highlight.js/lib/languages/cpp';
import c from 'highlight.js/lib/languages/c';
import csharp from 'highlight.js/lib/languages/csharp';
import go from 'highlight.js/lib/languages/go';
import rust from 'highlight.js/lib/languages/rust';
import php from 'highlight.js/lib/languages/php';
import ruby from 'highlight.js/lib/languages/ruby';
import swift from 'highlight.js/lib/languages/swift';
import kotlin from 'highlight.js/lib/languages/kotlin';
import scala from 'highlight.js/lib/languages/scala';
import dart from 'highlight.js/lib/languages/dart';
import elixir from 'highlight.js/lib/languages/elixir';
import erlang from 'highlight.js/lib/languages/erlang';
import haskell from 'highlight.js/lib/languages/haskell';
import clojure from 'highlight.js/lib/languages/clojure';
import fsharp from 'highlight.js/lib/languages/fsharp';
import objectivec from 'highlight.js/lib/languages/objectivec';
import perl from 'highlight.js/lib/languages/perl';
import r from 'highlight.js/lib/languages/r';
import matlab from 'highlight.js/lib/languages/matlab';
import julia from 'highlight.js/lib/languages/julia';
import lua from 'highlight.js/lib/languages/lua';
import vim from 'highlight.js/lib/languages/vim';
import sql from 'highlight.js/lib/languages/sql';
import bash from 'highlight.js/lib/languages/bash';
import powershell from 'highlight.js/lib/languages/powershell';
import dos from 'highlight.js/lib/languages/dos'; // For batch files
import yaml from 'highlight.js/lib/languages/yaml';
import json from 'highlight.js/lib/languages/json';
import xml from 'highlight.js/lib/languages/xml';
import ini from 'highlight.js/lib/languages/ini';
import properties from 'highlight.js/lib/languages/properties';
import dockerfile from 'highlight.js/lib/languages/dockerfile';
import nginx from 'highlight.js/lib/languages/nginx';
import apache from 'highlight.js/lib/languages/apache';
import markdown from 'highlight.js/lib/languages/markdown';
import latex from 'highlight.js/lib/languages/latex';
import asciidoc from 'highlight.js/lib/languages/asciidoc';
import x86asm from 'highlight.js/lib/languages/x86asm';
import armasm from 'highlight.js/lib/languages/armasm';
import lisp from 'highlight.js/lib/languages/lisp';
import scheme from 'highlight.js/lib/languages/scheme';
import prolog from 'highlight.js/lib/languages/prolog';
import ocaml from 'highlight.js/lib/languages/ocaml';
import reasonml from 'highlight.js/lib/languages/reasonml';
import elm from 'highlight.js/lib/languages/elm';
import glsl from 'highlight.js/lib/languages/glsl';
import fortran from 'highlight.js/lib/languages/fortran';
import delphi from 'highlight.js/lib/languages/delphi';
import vbnet from 'highlight.js/lib/languages/vbnet';
import basic from 'highlight.js/lib/languages/basic';
import smalltalk from 'highlight.js/lib/languages/smalltalk';
import ada from 'highlight.js/lib/languages/ada';
import tcl from 'highlight.js/lib/languages/tcl';
import awk from 'highlight.js/lib/languages/awk';
import makefile from 'highlight.js/lib/languages/makefile';
import cmake from 'highlight.js/lib/languages/cmake';
import gradle from 'highlight.js/lib/languages/gradle';
import diff from 'highlight.js/lib/languages/diff';
import scss from 'highlight.js/lib/languages/scss';
import less from 'highlight.js/lib/languages/less';
import stylus from 'highlight.js/lib/languages/stylus';
import css from 'highlight.js/lib/languages/css';
import django from 'highlight.js/lib/languages/django';
import handlebars from 'highlight.js/lib/languages/handlebars';
import twig from 'highlight.js/lib/languages/twig';
import graphql from 'highlight.js/lib/languages/graphql';
import pgsql from 'highlight.js/lib/languages/pgsql';
import coffeescript from 'highlight.js/lib/languages/coffeescript';
import actionscript from 'highlight.js/lib/languages/actionscript';
import applescript from 'highlight.js/lib/languages/applescript';
import autohotkey from 'highlight.js/lib/languages/autohotkey';
import vbscript from 'highlight.js/lib/languages/vbscript';
import vhdl from 'highlight.js/lib/languages/vhdl';
import verilog from 'highlight.js/lib/languages/verilog';
import inform7 from 'highlight.js/lib/languages/inform7';
import pony from 'highlight.js/lib/languages/pony';
import crystal from 'highlight.js/lib/languages/crystal';
import d from 'highlight.js/lib/languages/d';
import nim from 'highlight.js/lib/languages/nim';
import nix from 'highlight.js/lib/languages/nix';
import groovy from 'highlight.js/lib/languages/groovy';
import moonscript from 'highlight.js/lib/languages/moonscript';
import processing from 'highlight.js/lib/languages/processing';
import puppet from 'highlight.js/lib/languages/puppet';
import qml from 'highlight.js/lib/languages/qml';
import roboconf from 'highlight.js/lib/languages/roboconf';
import sas from 'highlight.js/lib/languages/sas';
import stata from 'highlight.js/lib/languages/stata';
import thrift from 'highlight.js/lib/languages/thrift';
import vala from 'highlight.js/lib/languages/vala';

// Register all languages
// Web Technologies
lowlight.register('javascript', javascript);
lowlight.register('typescript', typescript);
lowlight.register('html', xml);
lowlight.register('css', css);
lowlight.register('scss', scss);
lowlight.register('sass', scss);
lowlight.register('less', less);
lowlight.register('stylus', stylus);
lowlight.register('php', php);

// Popular Programming Languages
lowlight.register('python', python);
lowlight.register('java', java);
lowlight.register('csharp', csharp);
lowlight.register('cpp', cpp);
lowlight.register('c', c);
lowlight.register('go', go);
lowlight.register('rust', rust);
lowlight.register('ruby', ruby);
lowlight.register('swift', swift);
lowlight.register('kotlin', kotlin);
lowlight.register('scala', scala);
lowlight.register('dart', dart);
lowlight.register('elixir', elixir);
lowlight.register('erlang', erlang);
lowlight.register('haskell', haskell);
lowlight.register('clojure', clojure);
lowlight.register('fsharp', fsharp);
lowlight.register('objectivec', objectivec);
lowlight.register('perl', perl);
lowlight.register('r', r);
lowlight.register('matlab', matlab);
lowlight.register('julia', julia);
lowlight.register('lua', lua);
lowlight.register('vim', vim);

// Shell & Scripting
lowlight.register('bash', bash);
lowlight.register('shell', bash);
lowlight.register('powershell', powershell);
lowlight.register('batch', dos);
lowlight.register('fish', bash);
lowlight.register('zsh', bash);

// Databases & Query Languages
lowlight.register('sql', sql);
lowlight.register('mysql', sql);
lowlight.register('postgresql', sql);
lowlight.register('sqlite', sql);
lowlight.register('plsql', sql);
lowlight.register('graphql', graphql);

// Data & Configuration
lowlight.register('json', json);
lowlight.register('yaml', yaml);
lowlight.register('xml', xml);
lowlight.register('toml', ini); // TOML uses ini highlighting
lowlight.register('ini', ini);
lowlight.register('properties', properties);

// DevOps & Infrastructure
lowlight.register('dockerfile', dockerfile);
lowlight.register('nginx', nginx);
lowlight.register('apache', apache);

// Documentation & Markup
lowlight.register('markdown', markdown);
lowlight.register('latex', latex);
lowlight.register('asciidoc', asciidoc);

// Assembly & Low Level
lowlight.register('x86asm', x86asm);
lowlight.register('armasm', armasm);
lowlight.register('assembly', x86asm);
lowlight.register('nasm', x86asm);
lowlight.register('arm', armasm);

// Functional & Logic
lowlight.register('lisp', lisp);
lowlight.register('scheme', scheme);
lowlight.register('prolog', prolog);
lowlight.register('ocaml', ocaml);
lowlight.register('reasonml', reasonml);
lowlight.register('reason', reasonml);
lowlight.register('elm', elm);

// Game Development
lowlight.register('glsl', glsl);
lowlight.register('hlsl', glsl);

// Other Notable Languages
lowlight.register('cobol', fortran); // Use fortran as fallback for COBOL
lowlight.register('fortran', fortran);
lowlight.register('pascal', delphi); // Use delphi as fallback for Pascal
lowlight.register('delphi', delphi);
lowlight.register('vbnet', vbnet);
lowlight.register('visual-basic', basic);
lowlight.register('ada', ada);
lowlight.register('smalltalk', smalltalk);
lowlight.register('tcl', tcl);
lowlight.register('awk', awk);
lowlight.register('makefile', makefile);
lowlight.register('cmake', cmake);
lowlight.register('gradle', gradle);

// Web Frameworks & Templates
lowlight.register('django', django);
lowlight.register('handlebars', handlebars);
lowlight.register('pug', handlebars); // Use handlebars as fallback for Pug
lowlight.register('twig', twig);

// Specialized
lowlight.register('diff', diff);

// Additional languages
lowlight.register('coffeescript', coffeescript);
lowlight.register('coffee', coffeescript);
lowlight.register('actionscript', actionscript);
lowlight.register('applescript', applescript);
lowlight.register('autohotkey', autohotkey);
lowlight.register('vbscript', vbscript);
lowlight.register('vhdl', vhdl);
lowlight.register('verilog', verilog);
lowlight.register('inform7', inform7);
lowlight.register('pony', pony);
lowlight.register('crystal', crystal);
lowlight.register('d', d);
lowlight.register('nim', nim);
lowlight.register('nix', nix);
lowlight.register('groovy', groovy);
lowlight.register('moonscript', moonscript);
lowlight.register('processing', processing);
lowlight.register('puppet', puppet);
lowlight.register('qml', qml);
lowlight.register('roboconf', roboconf);
lowlight.register('sas', sas);
lowlight.register('stata', stata);
lowlight.register('thrift', thrift);
lowlight.register('vala', vala);
lowlight.register('postgresql', pgsql);
lowlight.register('postgres', pgsql);

// Languages using fallbacks for similar syntax
lowlight.register('vue', xml); // Vue templates use HTML-like syntax
lowlight.register('svelte', xml); // Svelte components use HTML-like syntax
lowlight.register('jsx', javascript); // JSX uses JavaScript syntax
lowlight.register('tsx', typescript); // TSX uses TypeScript syntax
lowlight.register('sass', scss); // Sass is similar to SCSS
lowlight.register('flutter', dart); // Flutter uses Dart
lowlight.register('reactnative', javascript); // React Native uses JavaScript
lowlight.register('docker-compose', yaml); // Docker Compose uses YAML
lowlight.register('kubernetes', yaml); // Kubernetes manifests use YAML
lowlight.register('terraform', ini); // Terraform uses HCL which is similar to ini
lowlight.register('ansible', yaml); // Ansible uses YAML
lowlight.register('vagrant', ruby); // Vagrant uses Ruby-like syntax
lowlight.register('github-actions', yaml); // GitHub Actions use YAML
lowlight.register('gitlab-ci', yaml); // GitLab CI uses YAML
lowlight.register('jenkins', groovy); // Jenkins pipelines often use Groovy
lowlight.register('aws', json); // AWS configs often use JSON
lowlight.register('gcp', json); // GCP configs often use JSON
lowlight.register('azure', json); // Azure configs often use JSON
lowlight.register('restructuredtext', markdown); // Similar to markdown
lowlight.register('textile', markdown); // Similar to markdown
lowlight.register('assembly', x86asm); // Generic assembly
lowlight.register('nasm', x86asm); // NASM uses x86 assembly
lowlight.register('arm', armasm); // ARM assembly
lowlight.register('reason', reasonml); // ReasonML
lowlight.register('purescript', haskell); // PureScript is similar to Haskell
lowlight.register('hlsl', glsl); // HLSL is similar to GLSL
lowlight.register('gdscript', python); // GDScript is similar to Python
lowlight.register('unrealscript', cpp); // UnrealScript is similar to C++
lowlight.register('mathematica', matlab); // Mathematica is similar to MATLAB
lowlight.register('octave', matlab); // Octave is similar to MATLAB
lowlight.register('spss', sql); // SPSS uses SQL-like syntax
lowlight.register('flask', python); // Flask is Python
lowlight.register('django', python); // Django templates use Python-like syntax
lowlight.register('rails', ruby); // Rails is Ruby
lowlight.register('laravel', php); // Laravel is PHP
lowlight.register('symfony', php); // Symfony is PHP
lowlight.register('express', javascript); // Express.js is JavaScript
lowlight.register('fastapi', python); // FastAPI is Python
lowlight.register('spring', java); // Spring is Java
lowlight.register('regex', javascript); // Regex highlighting
lowlight.register('log', bash); // Log files often look like shell output
lowlight.register('patch', diff); // Patch files are diffs
lowlight.register('gitignore', ini); // Git ignore files
lowlight.register('gitconfig', ini); // Git config files
lowlight.register('editorconfig', ini); // EditorConfig files
lowlight.register('browserslist', ini); // Browserslist files
lowlight.register('csv', ini); // CSV files
lowlight.register('env', ini); // Environment files
lowlight.register('sed', bash); // Sed is shell-like
lowlight.register('mongodb', javascript); // MongoDB queries use JavaScript-like syntax
lowlight.register('redis', bash); // Redis commands are shell-like

// Register aliases
lowlight.registerAlias('javascript', 'js');
lowlight.registerAlias('typescript', 'ts');
lowlight.registerAlias('python', 'py');
lowlight.registerAlias('csharp', 'cs');
lowlight.registerAlias('cpp', 'c++');
lowlight.registerAlias('bash', 'sh');
lowlight.registerAlias('yaml', 'yml');
lowlight.registerAlias('markdown', 'md');
lowlight.registerAlias('objectivec', 'objc');
lowlight.registerAlias('fsharp', 'fs');
lowlight.registerAlias('reasonml', 're');
lowlight.registerAlias('dockerfile', 'docker');
lowlight.registerAlias('x86asm', 'asm');
lowlight.registerAlias('x86asm', 'nasm');
lowlight.registerAlias('armasm', 'arm');
lowlight.registerAlias('visual-basic', 'vb');
lowlight.registerAlias('visual-basic', 'vbscript');
lowlight.registerAlias('powershell', 'ps1');
lowlight.registerAlias('batch', 'bat');
lowlight.registerAlias('batch', 'cmd');

export interface IEditor {
    content: () => string | undefined;
}

interface TiptapEditorProps {
    content?: string;
    onChange?: (content: string) => void;
    editable?: boolean;
    onContentChange?: (isChanged: boolean) => void;
    markdown?: string;
    docType?: string;
}

export const TiptapEditor = forwardRef<IEditor, TiptapEditorProps>(
    (
        {
            content = '',
            onChange,
            editable = true,
            onContentChange,
            markdown = '',
        },
        ref,
    ) => {
        const [initialContent] = useState(markdown || content);
        const [isSettingContent, setIsSettingContent] = useState(false);

        // Sticky header state
        const [stickyHeader, setStickyHeader] = useState<{
            text: string;
            level: number;
        } | null>(null);
        const editorContentRef = useRef<HTMLDivElement>(null);

        const editor = useEditor({
            extensions: [
                StarterKit.configure({
                    heading: false,
                    codeBlock: false,
                }),
                Typography,
                Heading.configure({
                    levels: [1, 2, 3, 4, 5, 6],
                    HTMLAttributes: {
                        class: 'tiptap-heading',
                    },
                }),
                Table.configure({
                    resizable: true,
                    handleWidth: 5,
                    cellMinWidth: 25,
                    HTMLAttributes: {
                        class: 'tiptap-table',
                    },
                }),
                TableRow.configure({
                    HTMLAttributes: {
                        class: 'tiptap-table-row',
                    },
                }),
                TableHeader.configure({
                    HTMLAttributes: {
                        class: 'tiptap-table-header',
                    },
                }),
                TableCell.configure({
                    HTMLAttributes: {
                        class: 'tiptap-table-cell',
                    },
                }),
                CustomCodeBlock.configure({
                    lowlight,
                    HTMLAttributes: {
                        class: 'tiptap-code-block',
                        spellcheck: 'false',
                    },
                    defaultLanguage: 'javascript',
                    exitOnTripleEnter: true,
                    exitOnArrowDown: true,
                }),
                TextAlign.configure({
                    types: ['heading', 'paragraph'],
                    alignments: ['left', 'center', 'right', 'justify'],
                }),
                Link.configure({
                    openOnClick: false,
                    HTMLAttributes: {
                        class: 'tiptap-link',
                    },
                }),
                Image.configure({
                    inline: true,
                    HTMLAttributes: {
                        class: 'tiptap-image',
                    },
                }),
                MermaidExtension,
                RawTextBlock,
                MarkdownPasteHandler,
                Markdown.configure({
                    html: true,
                    tightLists: true,
                    tightListClass: 'tight',
                    bulletListMarker: '-',
                    linkify: true,
                    breaks: false,
                    transformPastedText: true,
                    transformCopiedText: false,
                }),
            ],
            content: '', // Start with empty content
            editable,
            // Add event handlers for paste handling
            editorProps: {
                handlePaste: () => {
                    return false; // Let other handlers process the paste
                },
            },
            onUpdate: ({editor}) => {
                // Skip update callbacks while we're setting content
                if (isSettingContent) return;

                // Helper function to detect if content contains markdown formatting
                const hasMarkdownFormatting = (text: string): boolean => {
                    const markdownPatterns = [
                        /^#{1,6}\s/m, // Headers
                        /\*\*[^*]+\*\*/, // Bold
                        /\*[^*]+\*/, // Italic
                        /__[^_]+__/, // Bold (underscore)
                        /_[^_]+_/, // Italic (underscore)
                        /`[^`]+`/, // Inline code
                        /```[\s\S]*?```/, // Code blocks
                        /^\s*[-*+]\s/m, // Unordered lists
                        /^\s*\d+\.\s/m, // Ordered lists
                        /\[([^\]]+)\]\(([^)]+)\)/, // Links
                        /!\[([^\]]*)\]\(([^)]+)\)/, // Images
                        /^\s*>/m, // Blockquotes
                        /\|.*\|/, // Tables
                        /^\s*---+\s*$/m, // Horizontal rules
                        /~~[^~]+~~/, // Strikethrough
                    ];

                    return markdownPatterns.some(pattern => pattern.test(text));
                };

                // Get plain text first to check for markdown patterns
                const plainText = editor.getText();

                // Check if the plain text content contains markdown formatting
                const isMarkdownFormatted = hasMarkdownFormatting(plainText);

                // If it's markdown-formatted, use markdown; otherwise use plain text
                const finalContent = isMarkdownFormatted
                    ? editor.storage.markdown.getMarkdown()
                    : plainText;

                onChange?.(finalContent);

                // Always call onContentChange to trigger validation
                // The parent component handles the validation logic
                onContentChange?.(
                    finalContent.trim() !== initialContent.trim(),
                );
            },
        });

        // Handle markdown prop changes with async content setting
        useEffect(() => {
            if (editor && markdown) {
                const currentContent = editor.storage.markdown.getMarkdown();
                if (markdown !== currentContent) {
                    setIsSettingContent(true);

                    // For large documents, defer content setting to prevent blocking
                    const setContentAsync = () => {
                        if (markdown.length > 5000) {
                            // Use requestAnimationFrame for better performance
                            requestAnimationFrame(() => {
                                editor.commands.setContent(markdown);
                                setIsSettingContent(false);
                            });
                        } else {
                            editor.commands.setContent(markdown);
                            setIsSettingContent(false);
                        }
                    };

                    setContentAsync();
                }
            }
        }, [markdown, editor]);

        // Sticky header functionality
        const updateStickyHeader = useCallback(() => {
            if (!editorContentRef.current) {
                setStickyHeader(null);
                return;
            }
            const editorElement =
                editorContentRef.current.querySelector('.ProseMirror');
            if (!editorElement) return;

            const headings = editorElement.querySelectorAll(
                'h1.tiptap-heading, h2.tiptap-heading',
            );
            const containerTop =
                editorContentRef.current.getBoundingClientRect().top;

            let activeHeading: Element | null = null;
            let activeLevel = 0;

            // Find the active heading based on scroll position
            headings.forEach(heading => {
                const rect = heading.getBoundingClientRect();
                const headingTop = rect.top - containerTop;

                // If heading is above the sticky position (scrolled past)
                if (Math.floor(headingTop) <= -24) {
                    const level = parseInt(heading.tagName.charAt(1));
                    activeHeading = heading;
                    activeLevel = level;
                }
            });

            if (activeHeading && activeLevel > 0) {
                setStickyHeader({
                    text: (activeHeading as HTMLElement).textContent || '',
                    level: activeLevel,
                });
            } else {
                setStickyHeader(null);
            }
        }, []);

        // Set up scroll listener for sticky headers
        useEffect(() => {
            const editorContent = editorContentRef.current;
            if (!editorContent) return;

            editorContent.addEventListener('scroll', updateStickyHeader);
            // Also update on content changes
            updateStickyHeader();

            return () => {
                editorContent.removeEventListener('scroll', updateStickyHeader);
            };
        }, [updateStickyHeader, editor]);

        // Update sticky header when content changes
        useEffect(() => {
            if (editor) {
                const handleUpdate = () => {
                    // Delay to ensure DOM is updated
                    setTimeout(updateStickyHeader, 100);
                };

                const handlePaste = () => {
                    // Skip paste handling if we're setting content
                    if (isSettingContent) return;

                    // Force validation after paste with async handling for large content
                    const processAfterPaste = () => {
                        const plainText = editor.getText();
                        const hasMarkdownFormatting = (
                            text: string,
                        ): boolean => {
                            const markdownPatterns = [
                                /^#{1,6}\s/m, // Headers
                                /\*\*[^*]+\*\*/, // Bold
                                /\*[^*]+\*/, // Italic
                                /__[^_]+__/, // Bold (underscore)
                                /_[^_]+_/, // Italic (underscore)
                                /`[^`]+`/, // Inline code
                                /```[\s\S]*?```/, // Code blocks
                                /^\s*[-*+]\s/m, // Unordered lists
                                /^\s*\d+\.\s/m, // Ordered lists
                                /\[([^\]]+)\]\(([^)]+)\)/, // Links
                                /!\[([^\]]*)\]\(([^)]+)\)/, // Images
                                /^\s*>/m, // Blockquotes
                                /\|.*\|/, // Tables
                                /^\s*---+\s*$/m, // Horizontal rules
                                /~~[^~]+~~/, // Strikethrough
                            ];
                            return markdownPatterns.some(pattern =>
                                pattern.test(text),
                            );
                        };

                        const isMarkdownFormatted =
                            hasMarkdownFormatting(plainText);
                        const finalContent = isMarkdownFormatted
                            ? editor.storage.markdown.getMarkdown()
                            : plainText;

                        onChange?.(finalContent);
                        onContentChange?.(
                            finalContent.trim() !== initialContent.trim(),
                        );
                    };

                    // For large content, use requestAnimationFrame for better performance
                    if (editor.getText().length > 5000) {
                        requestAnimationFrame(processAfterPaste);
                    } else {
                        setTimeout(processAfterPaste, 100);
                    }
                };

                editor.on('update', handleUpdate);
                editor.on('selectionUpdate', handleUpdate);

                // Listen for paste events
                const editorElement = editor.view.dom;
                editorElement.addEventListener('paste', handlePaste);

                return () => {
                    editor.off('update', handleUpdate);
                    editor.off('selectionUpdate', handleUpdate);
                    editorElement.removeEventListener('paste', handlePaste);
                };
            }
        }, [
            editor,
            updateStickyHeader,
            onChange,
            onContentChange,
            initialContent,
            isSettingContent,
        ]);

        // Expose content method via ref
        useImperativeHandle(ref, () => ({
            content: () => {
                if (!editor) return undefined;

                // Helper function to detect if content contains markdown formatting
                const hasMarkdownFormatting = (text: string): boolean => {
                    const markdownPatterns = [
                        /^#{1,6}\s/m, // Headers
                        /\*\*[^*]+\*\*/, // Bold
                        /\*[^*]+\*/, // Italic
                        /__[^_]+__/, // Bold (underscore)
                        /_[^_]+_/, // Italic (underscore)
                        /`[^`]+`/, // Inline code
                        /```[\s\S]*?```/, // Code blocks
                        /^\s*[-*+]\s/m, // Unordered lists
                        /^\s*\d+\.\s/m, // Ordered lists
                        /\[([^\]]+)\]\(([^)]+)\)/, // Links
                        /!\[([^\]]*)\]\(([^)]+)\)/, // Images
                        /^\s*>/m, // Blockquotes
                        /\|.*\|/, // Tables
                        /^\s*---+\s*$/m, // Horizontal rules
                        /~~[^~]+~~/, // Strikethrough
                    ];

                    return markdownPatterns.some(pattern => pattern.test(text));
                };

                // Get plain text first to check for markdown patterns
                const plainText = editor.getText();

                // Check if the plain text content contains markdown formatting
                const isMarkdownFormatted = hasMarkdownFormatting(plainText);

                // If it's markdown-formatted, use markdown; otherwise use plain text
                return isMarkdownFormatted
                    ? editor.storage.markdown.getMarkdown()
                    : plainText;
            },
        }));

        if (!editor) {
            return null;
        }

        return (
            <div className="tiptap-editor-wrapper">
                <EditorToolbar editor={editor} />
                {editor.isActive('table') && <TableControls editor={editor} />}

                {/* Sticky Header */}
                {stickyHeader && (
                    <div className="tiptap-sticky-header">
                        <div
                            className={`sticky-header-content sticky-header-h${stickyHeader.level}`}>
                            {stickyHeader.text}
                        </div>
                    </div>
                )}

                <div
                    ref={editorContentRef}
                    className="tiptap-editor-content-wrapper">
                    <EditorContent
                        editor={editor}
                        className="tiptap-editor-content"
                    />
                </div>
            </div>
        );
    },
);

TiptapEditor.displayName = 'TiptapEditor';
