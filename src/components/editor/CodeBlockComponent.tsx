import {<PERSON>de<PERSON><PERSON>w<PERSON><PERSON>nt, NodeViewProps, NodeViewWrapper} from '@tiptap/react';
import {Check, ChevronDown, Search, X} from 'lucide-react';
import React, {useCallback, useEffect, useRef, useState} from 'react';
import {createPortal} from 'react-dom';
import {Copy, Trash} from './icons';

const LANGUAGE_OPTIONS = [
    // Web Technologies
    {value: 'javascript', label: 'JavaScript'},
    {value: 'typescript', label: 'TypeScript'},
    {value: 'html', label: 'HTML'},
    {value: 'css', label: 'CSS'},
    {value: 'scss', label: 'SCSS'},
    {value: 'sass', label: 'Sass'},
    {value: 'less', label: 'Less'},
    {value: 'php', label: 'PHP'},
    {value: 'vue', label: 'Vue'},
    {value: 'svelte', label: 'Svelte'},
    {value: 'jsx', label: 'JSX'},
    {value: 'tsx', label: 'TSX'},

    // Popular Programming Languages
    {value: 'python', label: 'Python'},
    {value: 'java', label: 'Java'},
    {value: 'csharp', label: 'C#'},
    {value: 'cpp', label: 'C++'},
    {value: 'c', label: 'C'},
    {value: 'go', label: 'Go'},
    {value: 'rust', label: 'Rust'},
    {value: 'ruby', label: 'Ruby'},
    {value: 'swift', label: 'Swift'},
    {value: 'kotlin', label: 'Kotlin'},
    {value: 'scala', label: 'Scala'},
    {value: 'dart', label: 'Dart'},
    {value: 'elixir', label: 'Elixir'},
    {value: 'erlang', label: 'Erlang'},
    {value: 'haskell', label: 'Haskell'},
    {value: 'clojure', label: 'Clojure'},
    {value: 'fsharp', label: 'F#'},
    {value: 'objectivec', label: 'Objective-C'},
    {value: 'perl', label: 'Perl'},
    {value: 'r', label: 'R'},
    {value: 'matlab', label: 'MATLAB'},
    {value: 'julia', label: 'Julia'},
    {value: 'lua', label: 'Lua'},
    {value: 'vim', label: 'Vim Script'},

    // Mobile Development
    {value: 'flutter', label: 'Flutter'},
    {value: 'reactnative', label: 'React Native'},

    // Shell & Scripting
    {value: 'bash', label: 'Bash'},
    {value: 'shell', label: 'Shell'},
    {value: 'powershell', label: 'PowerShell'},
    {value: 'batch', label: 'Batch'},
    {value: 'fish', label: 'Fish'},
    {value: 'zsh', label: 'Zsh'},

    // Databases & Query Languages
    {value: 'sql', label: 'SQL'},
    {value: 'mysql', label: 'MySQL'},
    {value: 'postgresql', label: 'PostgreSQL'},
    {value: 'sqlite', label: 'SQLite'},
    {value: 'plsql', label: 'PL/SQL'},
    {value: 'mongodb', label: 'MongoDB'},
    {value: 'redis', label: 'Redis'},
    {value: 'graphql', label: 'GraphQL'},

    // Data & Configuration
    {value: 'json', label: 'JSON'},
    {value: 'yaml', label: 'YAML'},
    {value: 'xml', label: 'XML'},
    {value: 'toml', label: 'TOML'},
    {value: 'ini', label: 'INI'},
    {value: 'csv', label: 'CSV'},
    {value: 'properties', label: 'Properties'},
    {value: 'env', label: 'Environment'},

    // DevOps & Infrastructure
    {value: 'dockerfile', label: 'Dockerfile'},
    {value: 'docker-compose', label: 'Docker Compose'},
    {value: 'kubernetes', label: 'Kubernetes'},
    {value: 'terraform', label: 'Terraform'},
    {value: 'ansible', label: 'Ansible'},
    {value: 'vagrant', label: 'Vagrant'},
    {value: 'nginx', label: 'Nginx'},
    {value: 'apache', label: 'Apache'},

    // Cloud & CI/CD
    {value: 'github-actions', label: 'GitHub Actions'},
    {value: 'gitlab-ci', label: 'GitLab CI'},
    {value: 'jenkins', label: 'Jenkins'},
    {value: 'aws', label: 'AWS'},
    {value: 'gcp', label: 'Google Cloud'},
    {value: 'azure', label: 'Azure'},

    // Documentation & Markup
    {value: 'markdown', label: 'Markdown'},
    {value: 'latex', label: 'LaTeX'},
    {value: 'asciidoc', label: 'AsciiDoc'},
    {value: 'restructuredtext', label: 'reStructuredText'},
    {value: 'textile', label: 'Textile'},

    // Assembly & Low Level
    {value: 'assembly', label: 'Assembly'},
    {value: 'nasm', label: 'NASM'},
    {value: 'arm', label: 'ARM Assembly'},
    {value: 'x86asm', label: 'x86 Assembly'},

    // Functional & Logic
    {value: 'lisp', label: 'Lisp'},
    {value: 'scheme', label: 'Scheme'},
    {value: 'prolog', label: 'Prolog'},
    {value: 'ocaml', label: 'OCaml'},
    {value: 'reason', label: 'ReasonML'},
    {value: 'elm', label: 'Elm'},
    {value: 'purescript', label: 'PureScript'},

    // Game Development
    {value: 'glsl', label: 'GLSL'},
    {value: 'hlsl', label: 'HLSL'},
    {value: 'gdscript', label: 'GDScript'},
    {value: 'unrealscript', label: 'UnrealScript'},

    // Scientific & Mathematical
    {value: 'mathematica', label: 'Mathematica'},
    {value: 'octave', label: 'Octave'},
    {value: 'sas', label: 'SAS'},
    {value: 'stata', label: 'Stata'},
    {value: 'spss', label: 'SPSS'},

    // Web Frameworks & Libraries
    {value: 'django', label: 'Django'},
    {value: 'flask', label: 'Flask'},
    {value: 'rails', label: 'Ruby on Rails'},
    {value: 'laravel', label: 'Laravel'},
    {value: 'symfony', label: 'Symfony'},
    {value: 'express', label: 'Express.js'},
    {value: 'fastapi', label: 'FastAPI'},
    {value: 'spring', label: 'Spring'},

    // Other Notable Languages
    {value: 'cobol', label: 'COBOL'},
    {value: 'fortran', label: 'Fortran'},
    {value: 'pascal', label: 'Pascal'},
    {value: 'delphi', label: 'Delphi'},
    {value: 'vbnet', label: 'VB.NET'},
    {value: 'visual-basic', label: 'Visual Basic'},
    {value: 'ada', label: 'Ada'},
    {value: 'smalltalk', label: 'Smalltalk'},
    {value: 'tcl', label: 'Tcl'},
    {value: 'awk', label: 'AWK'},
    {value: 'sed', label: 'Sed'},
    {value: 'makefile', label: 'Makefile'},
    {value: 'cmake', label: 'CMake'},
    {value: 'gradle', label: 'Gradle'},
    {value: 'maven', label: 'Maven'},

    // Specialized
    {value: 'regex', label: 'Regular Expression'},
    {value: 'log', label: 'Log Files'},
    {value: 'diff', label: 'Diff'},
    {value: 'patch', label: 'Patch'},
    {value: 'gitignore', label: 'Git Ignore'},
    {value: 'gitconfig', label: 'Git Config'},
    {value: 'editorconfig', label: 'EditorConfig'},
    {value: 'browserslist', label: 'Browserslist'},

    // Default
    {value: 'plaintext', label: 'Plain Text'},
];

export const CodeBlockComponent: React.FC<NodeViewProps> = ({
    node,
    updateAttributes,
    deleteNode,
}) => {
    const [showLanguageMenu, setShowLanguageMenu] = useState(false);
    const [copied, setCopied] = useState(false);
    const [searchTerm, setSearchTerm] = useState('');
    const [highlightedIndex, setHighlightedIndex] = useState(0);
    const [dropdownPosition, setDropdownPosition] = useState({top: 0, left: 0});

    const dropdownRef = useRef<HTMLDivElement>(null);
    const searchInputRef = useRef<HTMLInputElement>(null);
    const optionsListRef = useRef<HTMLDivElement>(null);
    const languageButtonRef = useRef<HTMLButtonElement>(null);

    const language = node.attrs.language || 'plaintext';
    const currentLanguage =
        LANGUAGE_OPTIONS.find(opt => opt.value === language) ||
        LANGUAGE_OPTIONS[0];

    // Filter languages based on search term
    const filteredLanguages = LANGUAGE_OPTIONS.filter(
        lang =>
            lang.label.toLowerCase().includes(searchTerm.toLowerCase()) ||
            lang.value.toLowerCase().includes(searchTerm.toLowerCase()),
    );

    // Close dropdown when clicking outside and handle scroll repositioning
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            const target = event.target as Node;

            // Check if clicked outside the language button
            if (
                languageButtonRef.current &&
                !languageButtonRef.current.contains(target)
            ) {
                // Check if clicked outside the dropdown (which is now in a portal)
                const dropdownElement =
                    document.querySelector('.language-dropdown');
                if (!dropdownElement || !dropdownElement.contains(target)) {
                    setShowLanguageMenu(false);
                    setSearchTerm('');
                    setHighlightedIndex(0);
                }
            }
        };

        const handleScroll = () => {
            if (showLanguageMenu && languageButtonRef.current) {
                const rect = languageButtonRef.current.getBoundingClientRect();
                setDropdownPosition({
                    top: rect.bottom + 4,
                    left: rect.left,
                });
            }
        };

        if (showLanguageMenu) {
            document.addEventListener('mousedown', handleClickOutside);
            window.addEventListener('scroll', handleScroll, true); // Use capture to catch all scroll events
            window.addEventListener('resize', handleScroll); // Also handle window resize
        }

        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
            window.removeEventListener('scroll', handleScroll, true);
            window.removeEventListener('resize', handleScroll);
        };
    }, [showLanguageMenu]);

    // Focus search input when dropdown opens
    useEffect(() => {
        if (showLanguageMenu && searchInputRef.current) {
            searchInputRef.current.focus();
        }
    }, [showLanguageMenu]);

    // Reset highlighted index when search term changes
    useEffect(() => {
        setHighlightedIndex(0);
    }, [searchTerm]);

    // Handle keyboard navigation
    useEffect(() => {
        const handleKeyDown = (event: KeyboardEvent) => {
            if (!showLanguageMenu) return;

            switch (event.key) {
                case 'ArrowDown':
                    event.preventDefault();
                    setHighlightedIndex(prev =>
                        prev < filteredLanguages.length - 1 ? prev + 1 : 0,
                    );
                    break;
                case 'ArrowUp':
                    event.preventDefault();
                    setHighlightedIndex(prev =>
                        prev > 0 ? prev - 1 : filteredLanguages.length - 1,
                    );
                    break;
                case 'Enter':
                    event.preventDefault();
                    if (filteredLanguages[highlightedIndex]) {
                        handleLanguageChange(
                            filteredLanguages[highlightedIndex].value,
                        );
                    }
                    break;
                case 'Escape':
                    event.preventDefault();
                    setShowLanguageMenu(false);
                    setSearchTerm('');
                    setHighlightedIndex(0);
                    break;
            }
        };

        if (showLanguageMenu) {
            document.addEventListener('keydown', handleKeyDown);
        }

        return () => {
            document.removeEventListener('keydown', handleKeyDown);
        };
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [showLanguageMenu, highlightedIndex, filteredLanguages]);

    // Scroll highlighted option into view
    useEffect(() => {
        if (optionsListRef.current && showLanguageMenu) {
            const highlightedElement = optionsListRef.current.children[
                highlightedIndex
            ] as HTMLElement;
            if (highlightedElement) {
                highlightedElement.scrollIntoView({
                    block: 'nearest',
                    behavior: 'smooth',
                });
            }
        }
    }, [highlightedIndex, showLanguageMenu]);

    const handleCopy = () => {
        const codeText = node.textContent;
        navigator.clipboard.writeText(codeText);
        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
    };

    const handleDelete = () => {
        if (deleteNode) {
            deleteNode();
        }
    };

    const calculateDropdownPosition = () => {
        if (languageButtonRef.current) {
            const rect = languageButtonRef.current.getBoundingClientRect();
            setDropdownPosition({
                top: rect.bottom + 4,
                left: rect.left,
            });
        }
    };

    const handleLanguageChange = useCallback(
        (newLanguage: string) => {
            updateAttributes({language: newLanguage});
            setShowLanguageMenu(false);
            setSearchTerm('');
            setHighlightedIndex(0);
        },
        [updateAttributes],
    );

    const clearSearch = () => {
        setSearchTerm('');
        setHighlightedIndex(0);
        if (searchInputRef.current) {
            searchInputRef.current.focus();
        }
    };

    return (
        <NodeViewWrapper className="code-block-wrapper">
            <div className="code-block-header">
                <div className="language-selector" ref={dropdownRef}>
                    <button
                        ref={languageButtonRef}
                        className="language-button"
                        onClick={() => {
                            if (!showLanguageMenu) {
                                calculateDropdownPosition();
                            }
                            setShowLanguageMenu(!showLanguageMenu);
                        }}
                        contentEditable={false}
                        type="button">
                        <span className="language-label">
                            {currentLanguage.label}
                        </span>
                        <ChevronDown
                            size={14}
                            className={`chevron ${showLanguageMenu ? 'open' : ''}`}
                        />
                    </button>
                    {showLanguageMenu &&
                        createPortal(
                            <div
                                className="language-dropdown"
                                style={{
                                    position: 'fixed',
                                    top: dropdownPosition.top,
                                    left: dropdownPosition.left,
                                    zIndex: 9999,
                                }}>
                                <div className="dropdown-header">
                                    <div className="search-container">
                                        <Search
                                            size={16}
                                            className="search-icon"
                                        />
                                        <input
                                            ref={searchInputRef}
                                            type="text"
                                            placeholder="Search languages..."
                                            value={searchTerm}
                                            onChange={e =>
                                                setSearchTerm(e.target.value)
                                            }
                                            className="search-input"
                                            contentEditable={false}
                                        />
                                        {searchTerm && (
                                            <button
                                                onClick={clearSearch}
                                                className="clear-search"
                                                contentEditable={false}
                                                type="button">
                                                <X size={14} />
                                            </button>
                                        )}
                                    </div>
                                    <div className="results-count">
                                        {filteredLanguages.length} language
                                        {filteredLanguages.length !== 1
                                            ? 's'
                                            : ''}
                                    </div>
                                </div>
                                <div
                                    className="language-list"
                                    ref={optionsListRef}>
                                    {filteredLanguages.length > 0 ? (
                                        filteredLanguages.map(
                                            (option, index) => (
                                                <button
                                                    key={option.value}
                                                    className={`language-option ${
                                                        option.value ===
                                                        language
                                                            ? 'active'
                                                            : ''
                                                    } ${index === highlightedIndex ? 'highlighted' : ''}`}
                                                    onClick={() =>
                                                        handleLanguageChange(
                                                            option.value,
                                                        )
                                                    }
                                                    onMouseEnter={() =>
                                                        setHighlightedIndex(
                                                            index,
                                                        )
                                                    }
                                                    contentEditable={false}
                                                    type="button">
                                                    <span className="language-name">
                                                        {option.label}
                                                    </span>
                                                    <span className="language-code">
                                                        {option.value}
                                                    </span>
                                                </button>
                                            ),
                                        )
                                    ) : (
                                        <div className="no-results">
                                            <span>No languages found</span>
                                            <small>
                                                Try a different search term
                                            </small>
                                        </div>
                                    )}
                                </div>
                                <div className="dropdown-footer">
                                    <small>
                                        Use ↑↓ to navigate, Enter to select, Esc
                                        to close
                                    </small>
                                </div>
                            </div>,
                            document.body,
                        )}
                </div>
                <div className="code-block-actions">
                    <button
                        className="mermaid-toolbar-button"
                        onClick={handleCopy}
                        title="Copy code"
                        contentEditable={false}
                        type="button">
                        {copied ? (
                            <Check size={24} />
                        ) : (
                            <Copy height={24} width={24} color="#666666" />
                        )}
                    </button>
                    <button
                        className="mermaid-toolbar-button"
                        onClick={handleDelete}
                        title="Delete code block"
                        contentEditable={false}
                        type="button">
                        <Trash width={20} height={20} color="#666666" />
                    </button>
                </div>
            </div>
            <div className="code-content">
                <pre>
                    <NodeViewContent as="code" />
                </pre>
            </div>
        </NodeViewWrapper>
    );
};
