from blitzy_utils.enums import ProjectPhase

from common_models.models import Status, BlitzyCommit, VersionControlSystem, BlitzyCommitStatus, BlitzyCommitPRAction

from blitzy_utils.logger import logger

from common_models.db_client import get_db_session
from typing import Dict, Any, Optional

from sqlalchemy.orm import Session


def process_blitzy_commit_event(payload: Dict[str, Any], session: Optional[Session] = None):
    """
    Processes the reverse code generation completion event.

    :param payload: Dictionary containing event data.
    :param session: Optional database session for executing queries. If not provided,
        a new session will be created within the function.
    """
    process_code_generation_event(payload, session)


def process_code_generation_event(payload: Dict[str, Any], session: Optional[Session] = None):
    project_phase = payload.get("phase")
    job_status = payload.get("status")
    code_gen_id = payload["code_gen_id"]
    project_run_id = payload["jobId"]
    metadata = payload.get("metadata", {})
    pr_data = metadata.get("pr_data", {})

    with get_db_session(session) as session:
        logger.info(f"In code generation received {project_phase} event.")
        if not job_status == Status.DONE.value:
            logger.info(f"Job status is not DONE. Skipping.")
            return

        # Handle cases where we have PR data
        if not pr_data:
            logger.warning(f"Neither PR data nor commit data found in {payload}. Event won't be processed.")
            return

        logger.info("Processing PR data from event")
        blitzy_commit = create_blitzy_commit_from_pr(pr_data, project_run_id, code_gen_id)
        session.add(blitzy_commit)
        session.flush()
        logger.info(f"Successfully created blitzy commit from PR for {project_run_id} and {code_gen_id}.")


def process_forward_prop_event(payload: Dict[str, Any], session: Optional[Session] = None):
    project_phase = payload.get("phase")
    job_status = payload.get("status")
    code_gen_id = payload["code_gen_id"]
    project_run_id = payload["jobId"]
    with get_db_session(session) as session:
        logger.info(f"In code generation received {project_phase} event.")
        if not job_status == Status.DONE.value:
            logger.info(f"Job status is not DONE. Skipping.")
            return

        repo_url = payload.get("metadata").get("repo_url")
        blitzy_commit = create_blitzy_commit_from_repo_url(repo_url, project_run_id, code_gen_id)
        session.add(blitzy_commit)
        session.flush()
        logger.info(f"Successfully created blitzy commit for {project_run_id} and {code_gen_id}.")
        return


def extract_pr_data(pr_data) -> Dict[str, Any]:
    """
    Summarizes and extracts pull request (PR) related data, repository details, branch
    information, commit SHA values, and user information into a structured dictionary.

    :param pr_data: The dictionary containing pull request details from an API response.
    :return: A dictionary containing extracted PR data, repository information,
        branch and commit details, and user information.
    """
    # Get repository information
    repo_name = pr_data.get("base", {}).get("repo", {}).get("name")
    org_name = pr_data.get("base", {}).get("repo", {}).get("owner", {}).get("login")

    # Construct repo_url
    repo_url = f"https://github.com/{org_name}/{repo_name}" if org_name and repo_name else None

    return {
        # PR specific fields
        "pr_number": pr_data.get("number"),
        "pr_link": pr_data.get("html_url"),
        "pr_state": pr_data.get("state"),
        "pr_title": pr_data.get("title"),
        "pr_body": pr_data.get("body"),
        "pr_created_at": pr_data.get("created_at"),
        "pr_updated_at": pr_data.get("updated_at"),

        # Repository information
        "repo_id": str(pr_data.get("base", {}).get("repo", {}).get("id")),
        "repo_name": repo_name,
        "org_name": org_name,
        "repo_url": repo_url,

        # Branch information
        "head_branch": pr_data.get("head", {}).get("ref"),
        "base_branch": pr_data.get("base", {}).get("ref"),

        # Commit information
        "head_commit_sha": pr_data.get("head", {}).get("sha"),
        "base_commit_sha": pr_data.get("base", {}).get("sha"),

        # User information
        "user_login": pr_data.get("user", {}).get("login"),
        "user_id": str(pr_data.get("user", {}).get("id")),
    }


def create_blitzy_commit_from_pr(pr_data: Dict[str, Any], project_run_id: str, code_gen_id: str) -> BlitzyCommit:
    """
    Creates a BlitzyCommit instance from pull request data.

    :param pr_data: Pull request metadata consumed to generate the BlitzyCommit.
    :param project_run_id: Unique identifier for the project run.
    :param code_gen_id: Identifier for the code generation associated with the commit.
    :return: A BlitzyCommit instance constructed from the provided pull request data.
    """
    extracted_data = extract_pr_data(pr_data)

    branch_url = None
    if extracted_data["repo_url"] and extracted_data["head_branch"]:
        branch_url = f"{extracted_data['repo_url']}/tree/{extracted_data['head_branch']}"

    return BlitzyCommit(
        project_run_id=project_run_id,
        code_gen_id=code_gen_id,
        org_name=extracted_data["org_name"],
        repo_name=extracted_data["repo_name"],
        repo_id=extracted_data["repo_id"],
        repo_url=extracted_data["repo_url"],  # Added repo_url field
        branch_name=extracted_data["head_branch"],
        version_control_system=VersionControlSystem.GITHUB,
        blitzy_commit_hash=extracted_data["head_commit_sha"],
        blitzy_commit_url=f"https://github.com/{extracted_data['org_name']}/{extracted_data['repo_name']}/commit/{extracted_data['head_commit_sha']}",
        blitzy_branch_url=branch_url,
        status=BlitzyCommitStatus.PENDING,
        pr_number=int(extracted_data["pr_number"]),
        pr_link=extracted_data["pr_link"],
        pr_action=BlitzyCommitPRAction.NOT_TAKEN,
        commit_metadata=pr_data,
    )


def create_blitzy_commit_from_repo_url(repo_url: str, project_run_id: str, code_gen_id: str):
    """
    Creates a BlitzyCommit instance from a given GitHub repository URL.

    :param repo_url: The URL of the GitHub repository.
    :param project_run_id: Unique identifier for the project run.
    :param code_gen_id: Unique identifier for the code generation.
    :return: BlitzyCommit instance with populated fields.
    """
    try:
        # Remove protocol and domain
        path = repo_url.replace('https://github.com/', '')

        # Split the path into parts
        parts = path.strip('/').split('/')

        if len(parts) >= 2:
            org_name = parts[0]
            repo_name = parts[1]
        else:
            org_name = None
            repo_name = None

    except Exception as e:
        logger.error(f"Error parsing repo URL: {e}")
        org_name = None
        repo_name = None

    return BlitzyCommit(
        project_run_id=project_run_id,
        code_gen_id=code_gen_id,
        org_name=org_name,
        repo_name=repo_name,
        repo_url=repo_url,  # Added repo_url field
        version_control_system=VersionControlSystem.GITHUB,
        status=BlitzyCommitStatus.PENDING,
        pr_action=None,
        commit_metadata={"repo_url": repo_url}
    )


def create_blitzy_commit_from_commit(commit_data: Dict[str, Any], payload: Dict[str, Any], project_run_id: str,
                                     code_gen_id: str) -> BlitzyCommit:
    """
    Creates a BlitzyCommit instance from commit data.

    :param commit_data: Commit metadata consumed to generate the BlitzyCommit.
    :param payload: The original event payload containing additional context.
    :param project_run_id: Unique identifier for the project run.
    :param code_gen_id: Identifier for the code generation associated with the commit.
    :return: A BlitzyCommit instance constructed from the provided commit data.
    """
    # Extract organization and repository information
    org_name = payload.get("org_name", "")
    repo_name = payload.get("metadata", {}).get("repo_name", "")
    repo_id = payload.get("repo_id", "")
    branch_name = payload.get("branch_name", "")

    # Extract repo URL from commit html_url
    commit_html_url = commit_data.get("html_url", "")
    repo_url = None

    if commit_html_url:
        # Format: https://github.com/BhagyashreeBlitzy/hao-backprop-test/commit/eceb6a220cd78fbb3e3f49028c53001a19a00f95
        # We need to extract: https://github.com/BhagyashreeBlitzy/hao-backprop-test
        parts = commit_html_url.split('/commit/')
        if len(parts) > 0:
            repo_url = parts[0]  # This gives us the repo URL without the commit part

    # If we couldn't extract from commit URL, try constructing from org and repo name
    if not repo_url and org_name and repo_name:
        repo_url = f"https://github.com/{org_name}/{repo_name}"

    # Create branch URL
    branch_url = None
    if repo_url and branch_name:
        branch_url = f"{repo_url}/tree/{branch_name}"

    # Extract commit specific information
    commit_hash = commit_data.get("sha")
    commit_url = commit_data.get("html_url")

    # Get original head commit from parents
    original_head_commit_hash = None
    parents = commit_data.get("parents", [])
    if parents and len(parents) > 0:
        original_head_commit_hash = parents[0].get("sha")

    return BlitzyCommit(
        project_run_id=project_run_id,
        code_gen_id=code_gen_id,
        org_name=org_name,
        repo_name=repo_name,
        repo_id=repo_id,
        repo_url=repo_url,
        branch_name=branch_name,
        blitzy_branch_url=branch_url,  # Added branch URL
        version_control_system=VersionControlSystem.GITHUB,
        blitzy_commit_hash=commit_hash,
        blitzy_commit_url=commit_url,
        status=BlitzyCommitStatus.PENDING,
        original_head_commit_hash=original_head_commit_hash,
        pr_action=BlitzyCommitPRAction.NOT_TAKEN,
        commit_metadata=commit_data,
    )
