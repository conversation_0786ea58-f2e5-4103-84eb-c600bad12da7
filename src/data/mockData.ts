// src/data/mockData.ts
import { <PERSON>RunJob, JobExecution } from "../types";

// Mock data for Cloud Run jobs
const cloudRunJobs: CloudRunJob[] = [
  {
    name: "code-spec-generator",
    region: "us-central1",
    lastUpdated: "Apr 5, 2025, 8:44:30 AM",
    executions: 30,
    completed: 27,
    failed: 3,
  },
  {
    name: "data-etl-processor",
    region: "us-central1",
    lastUpdated: "Apr 6, 2025, 1:22:15 PM",
    executions: 42,
    completed: 40,
    failed: 2,
  },
  {
    name: "report-generator",
    region: "us-east1",
    lastUpdated: "Apr 4, 2025, 11:05:47 PM",
    executions: 18,
    completed: 15,
    failed: 3,
  },
];

// Mock data for job executions
const jobExecutions: JobExecution[] = [
  {
    id: "code-spec-generator-xp6rm",
    creationTime: "Apr 4, 2025, 6:08:17 PM",
    taskStatus: "1/1 completed",
    endTime: "Apr 4, 2025, 7:32:32 PM",
    status: "succeeded",
  },
  {
    id: "code-spec-generator-ng5l9",
    creationTime: "Apr 4, 2025, 5:53:00 PM",
    taskStatus: "1/1 completed",
    endTime: "Apr 4, 2025, 7:22:52 PM",
    status: "succeeded",
  },
  {
    id: "code-spec-generator-pk6rj",
    creationTime: "Apr 4, 2025, 4:47:02 PM",
    taskStatus: "1/1 completed",
    endTime: "Apr 4, 2025, 5:43:34 PM",
    status: "succeeded",
  },
  {
    id: "code-spec-generator-q8fpr",
    creationTime: "Apr 4, 2025, 4:46:13 PM",
    taskStatus: "1/1 completed",
    endTime: "Apr 4, 2025, 5:22:42 PM",
    status: "succeeded",
  },
  {
    id: "code-spec-generator-94drc",
    creationTime: "Mar 28, 2025, 6:17:46 PM",
    taskStatus: "Failed with errors",
    endTime: "Mar 28, 2025, 8:41:55 PM",
    status: "failed",
  },
  {
    id: "code-spec-generator-zcqtj",
    creationTime: "Mar 28, 2025, 3:10:09 PM",
    taskStatus: "Failed with errors",
    endTime: "Mar 28, 2025, 3:11:09 PM",
    status: "failed",
  },
  {
    id: "code-spec-generator-sm6v7",
    creationTime: "Mar 26, 2025, 11:15:18 PM",
    taskStatus: "Failed with errors",
    endTime: "Mar 26, 2025, 11:16:30 PM",
    status: "failed",
  },
  {
    id: "data-etl-processor-adf32",
    creationTime: "Apr 5, 2025, 10:15:23 AM",
    taskStatus: "1/1 completed",
    endTime: "Apr 5, 2025, 11:22:18 AM",
    status: "succeeded",
  },
  {
    id: "data-etl-processor-cxv45",
    creationTime: "Apr 4, 2025, 9:30:55 PM",
    taskStatus: "1/1 completed",
    endTime: "Apr 4, 2025, 10:15:33 PM",
    status: "succeeded",
  },
  {
    id: "data-etl-processor-jkl78",
    creationTime: "Apr 3, 2025, 2:45:12 PM",
    taskStatus: "Failed with errors",
    endTime: "Apr 3, 2025, 2:50:32 PM",
    status: "failed",
  },
  {
    id: "report-generator-mnb43",
    creationTime: "Apr 4, 2025, 8:20:33 PM",
    taskStatus: "1/1 completed",
    endTime: "Apr 4, 2025, 9:15:17 PM",
    status: "succeeded",
  },
  {
    id: "report-generator-uio56",
    creationTime: "Apr 3, 2025, 5:10:45 PM",
    taskStatus: "Failed with errors",
    endTime: "Apr 3, 2025, 5:15:22 PM",
    status: "failed",
  },
];

export const jobData = {
  cloudRunJobs,
  jobExecutions,
};
