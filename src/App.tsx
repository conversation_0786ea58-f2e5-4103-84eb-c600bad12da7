// src/App.tsx
import { useState } from "react";
import {
  BrowserRouter as Router,
  Routes,
  Route,
  Navigate,
} from "react-router-dom";
import "./index.css";
import Header from "./components/custom/Header";
import AuthHeader from "./components/custom/AuthHeader";
import Dashboard from "./components/custom/Dashboard";
import Login from "./pages/Login";
import { Toaster } from "sonner";
import { isAuthenticated } from "./utils/auth";

// Protected route component
const ProtectedRoute = ({ children }: { children: React.ReactNode }) => {
  if (!isAuthenticated()) {
    return <Navigate to="/login" replace />;
  }
  return <>{children}</>;
};

function App() {
  const [refreshing, setRefreshing] = useState(false);
  const [dashboardKey, setDashboardKey] = useState(0);

  const handleRefresh = () => {
    setRefreshing(true);
    setDashboardKey((prev) => prev + 1);
    setTimeout(() => setRefreshing(false), 500);
  };

  return (
    <Router>
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <div className="flex flex-col min-h-screen">
          <Routes>
            <Route
              path="/login"
              element={
                <>
                  <AuthHeader />
                  <Login />
                </>
              }
            />
            <Route
              path="/dashboard"
              element={
                <ProtectedRoute>
                  <Header onRefresh={handleRefresh} isRefreshing={refreshing} />
                  <Dashboard key={dashboardKey} />
                </ProtectedRoute>
              }
            />
            <Route
              path="/"
              element={
                <Navigate
                  to={isAuthenticated() ? "/dashboard" : "/login"}
                  replace
                />
              }
            />
          </Routes>
          <Toaster position="bottom-right" />
        </div>
      </div>
    </Router>
  );
}

export default App;
