apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: {{SERVICE_NAME}}
  labels:
    managed-by: github-actions
    cloud.googleapis.com/location: {{DEPLOYMENT_REGION}}
spec:
  template:
    metadata:
      annotations:
        autoscaling.knative.dev/minScale: '1'
        run.googleapis.com/container-dependencies: '{"archie-job-tracker-1":["pgadapter"]}'
        run.googleapis.com/vpc-access-connector: {{CONNECTOR_NAME}}
        run.googleapis.com/vpc-access-egress: {{EGRESS_SETTING}}
    spec:
      containerConcurrency: 50
      timeoutSeconds: 3600
      serviceAccountName: {{SERVICE_ACCOUNT}}
      volumes:
      - name: sockets-dir
        emptyDir:
          sizeLimit: 50Mi
          medium: Memory
      containers:
      - name: archie-job-tracker-1
        image: {{ARTIFACTORY_REGION}}-docker.pkg.dev/{{PROJECT_ID}}/{{REPOSITORY}}/{{IMAGE_NAME}}:{{IMAGE_TAG}}
        ports:
        - name: http1
          containerPort: {{PORT}}
        env:
        - name: SPANNER_DATABASE_NAME
          value: {{SPANNER_DATABASE_NAME}}
        - name: ALLOW_TABLE_CREATION
          value: '{{ALLOW_TABLE_CREATION}}'
        - name: PROJECT_ID
          value: {{PROJECT_ID}}
        - name: LOG_LEVEL
          value: {{LOG_LEVEL}}
        - name: SERVICE_NAME
          value: {{SERVICE_NAME}}
        - name: AUTH_EMAIL
          value: {{AUTH_EMAIL}}
        - name: AUTH_PASSWORD
          value: {{AUTH_PASSWORD}}
        - name: JWT_SECRET_KEY
          value: {{JWT_SECRET_KEY}}
        resources:
          limits:
            cpu: 1000m
            memory: 512Mi
        startupProbe:
          timeoutSeconds: 10
          periodSeconds: 10
          failureThreshold: 1
          tcpSocket:
            port: 8080
        volumeMounts:
        - mountPath: /sockets
          name: sockets-dir
      - name: pgadapter
        image: gcr.io/cloud-spanner-pg-adapter/pgadapter
        volumeMounts:
        - mountPath: /sockets
          name: sockets-dir
        args:
        - -dir /sockets
        - -x
        resources:
          limits:
            memory: 2Gi  # Adjust this value as needed
            cpu: 1000m    # Optional: you can also set CPU limits
        startupProbe:
          initialDelaySeconds: 10
          timeoutSeconds: 10
          periodSeconds: 10
          failureThreshold: 3
          tcpSocket:
            port: 5432
  traffic:
  - percent: 100
    latestRevision: true
