import logging
import os
from contextlib import contextmanager
from threading import Lock
from typing import Optional

from sqlalchemy import create_engine, event
from sqlalchemy.orm import ORMExecuteState, Session, sessionmaker, with_loader_criteria

from common_models.base_model import Base, VisibilityMixin


class SpannerClientSingleton:
    _instance = None
    _lock = Lock()

    def __new__(cls, *args, **kwargs):
        # Double-checked locking to avoid multiple initializations in multi-threaded scenarios.
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(SpannerClientSingleton, cls).__new__(cls)
                    cls._instance.__initialized = False
        return cls._instance

    def __init__(self, logger=None):
        with self._lock:  # Added lock here
            if not self.__initialized:
                self.__initialized = True
                self.logger = logger or self._create_default_logger()
                self.logger.info("Initializing DB client")
                self._initialize_client()
                self.create_tables()

    def _initialize_client(self):
        """
        Initializes the SQLAlchemy engine and session factory for Google Cloud Spanner.
        """
        spanner_database_name = os.getenv("SPANNER_DATABASE_NAME")
        database_host = os.getenv("DATABASE_HOST", "localhost")
        database_port = os.getenv("DATABASE_PORT", "5432")

        if not spanner_database_name:
            self.logger.error("SPANNER_DATABASE_NAME environment variable missing.")
            raise ValueError("SPANNER_DATABASE_NAME environment variable missing.")

        database_url = f"postgresql+psycopg://{database_host}:{database_port}/{spanner_database_name}"
        self.engine = create_engine(
            database_url,
            connect_args={
                "client_encoding": "utf8",
                "options": "-c client_encoding=utf8"
            }
        ).execution_options(autocommit=True)
        self.SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)

        @event.listens_for(self.SessionLocal, "do_orm_execute")
        def _add_filtering_criteria(execute_state: ORMExecuteState):
            skip_filter = execute_state.execution_options.get("include_deleted", False)
            if execute_state.is_select and not skip_filter:
                execute_state.statement = execute_state.statement.options(
                    with_loader_criteria(
                        VisibilityMixin,
                        lambda cls: cls.is_deleted.is_(False),
                        include_aliases=True,
                    )
                )

    def _create_default_logger(self):
        """
        Creates a default logger if an external logger is not provided.
        """
        return logging.getLogger("SpannerClientSingleton")

    def create_tables(self):
        """Creates tables in Spanner based on the defined models."""
        allow_table_creation = os.getenv("ALLOW_TABLE_CREATION")
        if allow_table_creation and allow_table_creation.lower() == "true":
            self.logger.info("Table creation flag is enabled. Attempting to create tables in Spanner.")
            Base.metadata.create_all(self.engine)

    def get_session(self):
        """
        Returns a new SQLAlchemy session from the singleton client.
        """
        self.logger.debug("Creating a new session")
        return self.SessionLocal()


@contextmanager
def get_db_session(session: Optional[Session] = None):
    """
    Get database session - either use provided session or create new one.
    :param session: Existing SQLAlchemy session (optional).
    :yield: SQLAlchemy Session object.
    :raises: SQLAlchemyError if database operation fails.
    """
    should_close = False

    if session is None:
        # Create new session if none provided
        session = SpannerClientSingleton().get_session()
        should_close = True

    try:
        yield session
    finally:
        # Only close if we created the session
        if should_close:
            session.close()
