[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "db-common-model"
version = "0.0.68"
description = "A shared library for common models"
requires-python = ">=3.12"
dependencies = [
    "sqlalchemy>=1.4",
    "psycopg[binary]>=3.0"
]

[[project.authors]]
name = "Chaitanya Baraskar"
email = "<EMAIL>"

[tool.setuptools.packages.find]
include = ["common_models", "common_models.*"]
