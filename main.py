# Copyright 2019 Google, LLC.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
from json import JSONDecodeError

# [START cloudrun_pubsub_server]
import base64
import json

from blitzy_utils.logger import logger
from flask import Flask, request
from flask_utils.middleware.request_context import RequestContextMiddleware

from src.process_event import process_payload

app = Flask(__name__)

middleware = RequestContextMiddleware(app, logger=logger)


@app.route("/", methods=["POST"])
def index():
    """Receive and parse Pub/Sub messages."""
    envelope = request.get_json()
    if not isinstance(envelope, dict) or "message" not in envelope:
        msg = "invalid Pub/Sub message format"
        logger.error(f"error: {msg}")
        return f"Bad Request: {msg}", 400

    pubsub_message = envelope["message"]

    try:
        if isinstance(pubsub_message, dict) and "data" in pubsub_message:
            decoded_result = base64.b64decode(pubsub_message["data"]).decode("utf-8").strip()
            payload = json.loads(decoded_result)
            logger.info(f"Processing payload: {payload}")
            process_payload(payload)
    except JSONDecodeError as e:
        logger.error(f"Failed to process payload: {str(e)}")
    return "Event processed successfully", 200


if __name__ == "__main__":
    # Running application locally, outside of a Google Cloud Environment
    # handles Ctrl-C termination
    print("Starting the server....")
    app.run(host="localhost", port=8081, debug=True)
