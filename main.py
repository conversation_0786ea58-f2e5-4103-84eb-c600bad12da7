import base64
import os

import requests
from requests import HTT<PERSON><PERSON>rror

from job_alert import notify_slack
from log_alert import log_alert

FIREBASE_USER_NAME = os.getenv("FIREBASE_USER_NAME")
FIREBASE_USER_PASSWORD = os.getenv("FIREBASE_USER_PASSWORD")
BLITZY_BASE_URL = os.getenv("BLITZY_BASE_URL")
API_TIMEOUT = int(os.getenv("BLITZY_API_TIMEOUT"))


def get_auth_token():
    try:
        auth_url = f"{BLITZY_BASE_URL}/v1/auth"
        auth_string = f"{FIREBASE_USER_NAME}:{FIREBASE_USER_PASSWORD}"
        encoded_auth = base64.b64encode(auth_string.encode()).decode()
        auth_headers = {
            "X-Basic-Auth": f"Basic {encoded_auth}",
            "Content-Type": "application/json"
        }

        auth_response = requests.post(auth_url, headers=auth_headers, timeout=API_TIMEOUT)
        auth_response.raise_for_status()

        id_token = auth_response.json()["access_token"]
        return id_token
    except Exception as e:
        print(e)


def on_create_user(data, context):
    """Triggered by creation of a Firebase Auth user object.
    Args:
           data (dict): The event payload.
           context (google.cloud.functions.Context): Metadata for the event.
    """
    try:
        print(f"Attempting to create user with payload {data}")
        auth_token = get_auth_token()
        user_url = f"{BLITZY_BASE_URL}/v1/user"
        authorisation_header = {"Authorization": f"Bearer {auth_token}"}
        auth_payload = {
            "email": data["email"],
            "userId": data["uid"],
        }
        user_response = requests.post(user_url, headers=authorisation_header, json=auth_payload, timeout=API_TIMEOUT)
        user_response.raise_for_status()
        response_body = user_response.json()
        print(f"User created successfully with id {response_body['id']}")
    except HTTPError as e:
        print(f"Failed to create user with status code {e.response.status_code} and body {e.response.json()}")
        raise e


def handle_log_alert(cloud_event, context):
    return log_alert(cloud_event)


def handle_job_alert(cloud_event, context):
    notify_slack(cloud_event, context)


# To run this locally.
if __name__ == "__main__":
    on_create_user({
        "uid": "RyHDUjV5FFQGbq505mErctJKUZM2",
        "email": "<EMAIL>"
    }, {})
