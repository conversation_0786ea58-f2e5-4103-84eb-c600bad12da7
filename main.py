from blitzy_utils.logger import logger
from flask import Flask, jsonify, request
from flask_cors import CORS
from flask_utils.middleware.request_context import RequestContextMiddleware

from src.api.routes.auth import auth_bp
from src.api.routes.job import job_api_bp
from src.api.routes.job_event import job_event_bp
from src.auth.middleware import AuthMiddleware

app = Flask(__name__)

CORS(app, resources={r"/*": {"origins": "*"}})

app.register_blueprint(job_event_bp)
app.register_blueprint(job_api_bp)
app.register_blueprint(auth_bp)

middleware = RequestContextMiddleware(app, logger=logger)


@app.before_request
def check_auth_error():
    auth_error = request.environ.get('auth_error')
    if auth_error:
        return jsonify({'message': auth_error}), 401


exclude_paths = [
    '/v1/auth/login',
    '/v1/events',
    '/v1/job/tracking/run'
]
app.wsgi_app = AuthMiddleware(app.wsgi_app, exclude_paths=exclude_paths)

if __name__ == "__main__":
    logger.info("Starting the server..")
    app.run(host="localhost", port=8081, debug=True)
