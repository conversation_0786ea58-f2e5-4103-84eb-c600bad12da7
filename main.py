import functions_framework
import os
import json
from cloudevents.http import CloudEvent
from base64 import b64decode
from google.cloud import run_v2, storage, pubsub_v1

jobs_client = run_v2.JobsClient()
storage_client = storage.Client()
publisher = pubsub_v1.PublisherClient()

JOB_NAME = os.environ["JOB_NAME"]
PROJECT_ID = os.environ["PROJECT_ID"]
TOPIC_NAME = os.environ["TOPIC_NAME"]
TRIGGER_TOPIC = os.environ["TRIGGER_TOPIC"]

topic_path = publisher.topic_path(PROJECT_ID, TOPIC_NAME)


@functions_framework.cloud_event
def trigger_job(cloud_event: CloudEvent):
    # Decode and parse the event data
    event_data_str = b64decode(cloud_event.data["message"]["data"]).decode()
    print(f'received event: {event_data_str}')
    event_data = json.loads(event_data_str)
    prompt = event_data.get('prompt', None)
    repo_name = event_data.get('repo_name')
    if prompt:
        del event_data['prompt']

    request = run_v2.RunJobRequest(
        name=JOB_NAME,
        overrides=run_v2.RunJobRequest.Overrides(
            container_overrides=[
                run_v2.RunJobRequest.Overrides.ContainerOverride(
                    env=[
                        run_v2.EnvVar(
                            name="EVENT_DATA",
                            value=json.dumps(event_data)
                        )
                    ]
                )
            ]
        )
    )
    response = jobs_client.run_job(request=request)
    print(f'triggered job: {JOB_NAME}')

    execution_name = response.metadata.name
    execution_id = execution_name.split('/')[-1] if execution_name else ''
    event_data["resume"] = True
    custom_payload = {
        "triggerTopic": TRIGGER_TOPIC,
        "eventType": "CUSTOM_JOB_SUBMISSION",
        "jobName": JOB_NAME,
        "eventData": event_data,
        "jobSubmissionMetadata": {
            "executionName": execution_name,
            "executionId": execution_id,
            "status": "QUEUED",
            "submissionTime": response.metadata.create_time.isoformat() if hasattr(response.metadata,
                                                                                   'create_time') else None
        }
    }
    # Publish to Pub/Sub
    try:
        future = publisher.publish(
            topic_path,
            data=json.dumps(custom_payload).encode("utf-8"),
            eventType="CUSTOM_JOB_SUBMISSION"  # Added as an attribute
        )
        message_id = future.result()
        print(f"Published message ID: {message_id}")
    except Exception as e:
        print(f"Error publishing to Pub/Sub: {e}")
    return repo_name
