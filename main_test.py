from src.process_event import process_payload

payload = {'projectId': 'b6ea1f51-dad6-48b9-ade0-f91c30c1488a', 'jobId': '5bca03f6-7920-428e-a3b1-4c5d99de5d4e',
           'tech_spec_id': '30edd2a5-a814-430c-b0ec-0a12539e98a8', 'org_name': '', 'repo_id': '*********',
           'branch_name': 'main', 'branch_id': '12b30cb6-7efb-4ca5-9b4a-13fb2cad8b3d',
           'head_commit_hash': '3fa60e9a8746285f7fc8e2004fec7125df59d9ae', 'prev_head_commit_hash': '',
           'phase': 'CODE_DOWNLOAD', 'status': 'DONE', 'user_id': 'eb1625b6-377a-4b81-ab89-6feb1bd36a35',
           'team_id': 'default', 'company_id': 'default', 'graph_needs_update': False,
           'repo_name': 'tc915-ff-merge-pr-from-blitzy-r0dp0k', 'metadata': {'propagate': True}}

if __name__ == '__main__':
    process_payload(payload)
